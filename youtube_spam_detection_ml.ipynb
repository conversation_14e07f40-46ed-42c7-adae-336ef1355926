{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score\n", "from nltk.corpus import stopwords\n", "import nltk\n", "import re\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[nltk_data] Downloading package stopwords to\n", "[nltk_data]     C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data...\n", "[nltk_data]   Package stopwords is already up-to-date!\n"]}], "source": ["\n", "# <PERSON><PERSON> belum, unduh stopwords untuk nltk\n", "nltk.download('stopwords')\n", "stop_words = set(stopwords.words('english'))\n", "\n", "# Load data\n", "# file_path = 'youtube_comments_i6IOiUi6IYY.xlsx'\n", "file_path = 'youtube_comments_labeled_rule_based_advanced.xlsx'\n", "data = pd.read_excel(file_path)\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# Pra-p<PERSON><PERSON><PERSON> Te<PERSON>\n", "def preprocess_text(text):\n", "    # Ubah teks menjadi lowercase\n", "    text = text.lower()\n", "    # Hilangkan karakter khusus dan angka\n", "    text = re.sub(r'[^a-z\\s]', '', text)\n", "    # Hilangkan stop words\n", "    text = ' '.join(word for word in text.split() if word not in stop_words)\n", "    return text"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Akurasi: 0.95\n", "Precision: 0.00\n", "Recall: 0.00\n", "F1 Score: 0.00\n", "File with labeled comments saved as youtube_comments_ml_labeled.xlsx\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Python312\\Lib\\site-packages\\sklearn\\metrics\\_classification.py:1517: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 due to no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n"]}], "source": ["data['Comment'] = data['Comment'].apply(preprocess_text)\n", "\n", "# Membagi data menjadi training dan testing (80-20 split)\n", "X_train, X_test, y_train, y_test = train_test_split(data['Comment'], data['SPAM'], test_size=0.2, random_state=42)\n", "\n", "# Ekstraksi Fitur dengan TF-IDF\n", "tfidf = TfidfVectorizer(max_features=5000)  # Membatasi fitur ke 5000 kata paling sering muncul\n", "X_train_tfidf = tfidf.fit_transform(X_train)\n", "X_test_tfidf = tfidf.transform(X_test)\n", "\n", "# Melatih model Logistic Regression\n", "model = LogisticRegression()\n", "model.fit(X_train_tfidf, y_train)\n", "\n", "# Prediksi pada data testing\n", "y_pred = model.predict(X_test_tfidf)\n", "\n", "# Evaluasi Model\n", "accuracy = accuracy_score(y_test, y_pred)\n", "precision = precision_score(y_test, y_pred, pos_label='SPAM')\n", "recall = recall_score(y_test, y_pred, pos_label='SPAM')\n", "f1 = f1_score(y_test, y_pred, pos_label='SPAM')\n", "\n", "print(f\"Akurasi: {accuracy:.2f}\")\n", "print(f\"Precision: {precision:.2f}\")\n", "print(f\"Recall: {recall:.2f}\")\n", "print(f\"F1 Score: {f1:.2f}\")\n", "\n", "# Menggunakan model untuk melabeli seluruh data\n", "data['Predicted_SPAM'] = model.predict(tfidf.transform(data['Comment']))\n", "\n", "# Simpan hasil ke file baru\n", "output_path = 'youtube_comments_ml_labeled.xlsx'\n", "data.to_excel(output_path, index=False)\n", "\n", "print(f\"File with labeled comments saved as {output_path}\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}