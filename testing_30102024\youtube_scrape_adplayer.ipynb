{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Youtube Scrape and Check Youtube Ads"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [], "source": ["from selenium import webdriver\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "import time\n", "import requests\n", "\n"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [], "source": ["# ID Video YouTube\n", "# https://www.youtube.com/watch?v=o0oW3lPoOXM\n", "youtubeid = \"i6IOiUi6IYY\"  # Ganti dengan ID video yang diing<PERSON>an"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Koneksi ke YouTube berhasil.\n"]}], "source": ["try:\n", "    response = requests.get(\"https://www.youtube.com\")\n", "    if response.status_code == 200:\n", "        print(\"Koneksi ke YouTube berhasil.\")\n", "    else:\n", "        print(\"Koneksi ke YouTube tidak berhasil.\")\n", "except requests.ConnectionError as e:\n", "    print(\"Gagal menghubungi YouTube:\", e)"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [], "source": ["\n", "# Path ke driver Chrome\n", "# driver_path = '/path/to/chromedriver'  # Ganti dengan path chromedriver Anda\n", "profile_path = r'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Default'  # Ganti dengan path profil Chrome Anda\n", "\n", "def initialize_driver_with_profile(profile_path):\n", "    \"\"\"Inisialisasi driver dengan profil Chrome yang sudah ada.\"\"\"\n", "    chrome_options = Options()\n", "    chrome_options.add_argument(f\"user-data-dir={profile_path}\")\n", "    chrome_options.add_argument(\"--no-sandbox\")\n", "    chrome_options.add_argument(\"--disable-dev-shm-usage\")\n", "    chrome_options.add_argument(\"--no-first-run\")\n", "    chrome_options.add_argument(\"--remote-debugging-port=9222\")\n", "    chrome_options.add_argument(\"--enable-logging\")\n", "    chrome_options.add_argument(\"--v=1\")  # Level logging dasar\n", "    # service = Service(driver_path)\n", "    # driver = webdriver.Chrome(service=service, options=chrome_options)\n", "    driver = webdriver.Chrome(options=chrome_options)\n", "    return driver\n"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [], "source": ["def detect_and_skip_ad(driver):\n", "    \"\"\"<PERSON>iksa iklan dan klik tombol 'Skip Ad' saat tersedia.\"\"\"\n", "    ad_present = False\n", "    try:\n", "        # Cek jika elemen iklan atau teks sponsor ada\n", "        WebDriverWait(driver, 15).until(\n", "            EC.presence_of_any_elements_located([\n", "                (By.CLASS_NAME, \"video-ads.ytp-ad-module\"),               # Div dengan kelas iklan\n", "                (By.<PERSON>LA<PERSON>_NAME, \"ytp-ad-player-overlay-layout_\")          # Overlay iklan\n", "            ])\n", "        )\n", "        ad_present = True\n", "        print(\"Iklan terdeteksi.\")\n", "\n", "        # <PERSON><PERSON> iklan te<PERSON>, tunggu tombol \"<PERSON><PERSON>\" dan klik jika ada\n", "        skip_button = WebDriverWait(driver, 30).until(\n", "            EC.element_to_be_clickable((By.CLASS_NAME, \"ytp-skip-ad-button\"))\n", "        )\n", "        skip_button.click()\n", "        print(\"Tombol 'Skip Ad' diklik.\")\n", "        \n", "    except Exception as e:\n", "        print(\"Tidak ada iklan atau tombol 'Skip Ad' tidak ditemukan.\")\n", "        print(f\"Error: {e}\")\n", "\n", "    return ad_present"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [], "source": ["def get_all_youtube_comments(driver, max_comments=None):\n", "    \"\"\"Mengambil semua komentar dari video YouTube dengan scroll tanpa batas.\"\"\"\n", "    comments = []\n", "    last_height = driver.execute_script(\"return document.documentElement.scrollHeight\")\n", "    while True:\n", "        # Scroll ke bawah untuk memuat komentar baru\n", "        driver.execute_script(\"window.scrollTo(0, document.documentElement.scrollHeight);\")\n", "        time.sleep(2)  # <PERSON><PERSON> untuk memuat komentar baru\n", "\n", "        # Ambil elemen komentar\n", "        comment_elements = driver.find_elements(By.XPATH, '//*[@id=\"content-text\"]')\n", "        for element in comment_elements[len(comments):]:  # <PERSON><PERSON> dari komentar baru\n", "            comment_text = element.text\n", "            if comment_text:\n", "                comments.append(comment_text)\n", "                # <PERSON><PERSON> maksimal komentar jika disetel\n", "                if max_comments and len(comments) >= max_comments:\n", "                    return comments\n", "\n", "        # Cek tinggi baru halaman untuk melihat apakah ada yang baru dimuat\n", "        new_height = driver.execute_script(\"return document.documentElement.scrollHeight\")\n", "        if new_height == last_height:  # <PERSON><PERSON><PERSON><PERSON> jika tidak ada lagi yang dimuat\n", "            break\n", "        last_height = new_height\n", "\n", "    return comments"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "def save_to_csv(comments, filename):\n", "    \"\"\"Menyimpan komentar ke file CSV.\"\"\"\n", "    df = pd.DataFrame(comments, columns=[\"Comment\"])\n", "    df.to_csv(filename, index=False)\n", "    print(f\"Komentar berhasil disimpan di {filename}\")"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tidak ada iklan atau tombol 'Skip Ad' tidak ditemukan.\n", "Error: module 'selenium.webdriver.support.expected_conditions' has no attribute 'presence_of_any_elements_located'\n", "<PERSON><PERSON><PERSON> ber<PERSON>il disimpan di youtube_comments_i6IOiUi6IYY.csv\n"]}], "source": ["\n", "video_url = f\"https://www.youtube.com/watch?v={youtubeid}\"\n", "# URL video YouTube\n", "# video_url = \"https://www.youtube.com/watch?v=i6IOiUi6IYY\"  # Ganti dengan ID video yang diinginkan\n", "\n", "# Inisialisasi WebDriver dengan profil Chrome\n", "driver = initialize_driver_with_profile(profile_path)\n", "\n", "# Akses URL video YouTube\n", "driver.get(video_url)\n", "time.sleep(5)  # <PERSON><PERSON><PERSON> halaman dimuat\n", "\n", "# <PERSON><PERSON><PERSON> iklan dan lewati jika ada\n", "ad_present = detect_and_skip_ad(driver)\n", "\n", "\n", "# Jika tidak ada iklan atau iklan sudah di<PERSON>, mulai mengambil komentar\n", "if not ad_present or ad_present:\n", "    comments = get_all_youtube_comments(driver, max_comments=None)  # None untuk scroll tanpa batas\n", "    save_to_csv(comments, filename=f\"youtube_comments_{youtubeid}.csv\")\n"]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [{"data": {"text/plain": ["['<PERSON><PERSON> is the best he prove this  everytime by doing goal in every important match',\n", " '<PERSON>… man has been bringing it lately.',\n", " '<PERSON><PERSON> and <PERSON><PERSON> were so so good man ',\n", " 'That pass from Ben White to Saka was amazing. ',\n", " '<PERSON><PERSON><PERSON>, i could watch you play football for hours and not get bored. What a player man we are so lucky to have him',\n", " \"We are nearing perfection that's all I can say .Top quality performance from <PERSON><PERSON> and the team\",\n", " 'Not even a Liverpool fan but that play from Nunez and Salah ',\n", " '<PERSON> is just always there. He has so much presence',\n", " 'I’m so happy for <PERSON><PERSON> to stay at Liverpool. Magic player.',\n", " '\" Keep Saka\\'s name out your mouth,  dude is world class.',\n", " \"From a Liverpool fan, We didn't have <PERSON>, but I think those goals go in anyway, there's so much quality in this Arsenal side, I actually expected a loss, You lot are actually so good you drag our performances up with you, Especially <PERSON><PERSON>z...\",\n", " '1:23 Trent long pass moves like a golf ball, <PERSON><PERSON><PERSON> didnt even hv to control it',\n", " 'Running out of words to describe <PERSON><PERSON>',\n", " '<PERSON>ka and <PERSON> were so good',\n", " 'Appreciation for <PERSON><PERSON> how he crossed path when <PERSON><PERSON><PERSON> ran with ball. Quick and genius thinking. <PERSON><PERSON> was just unstoppable! ']"]}, "execution_count": 75, "metadata": {}, "output_type": "execute_result"}], "source": ["comments"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['<PERSON><PERSON> is the best he prove this  everytime by doing goal in every important match', '<PERSON>… man has been bringing it lately.', '<PERSON><PERSON> and <PERSON><PERSON> were so so good man ', 'That pass from <PERSON> to <PERSON><PERSON> was amazing. ', '<PERSON><PERSON><PERSON>, i could watch you play football for hours and not get bored. What a player man we are so lucky to have him', \"We are nearing perfection that's all I can say .Top quality performance from <PERSON><PERSON> and the team\", 'Not even a Liverpool fan but that play from Nunez and Salah ', '<PERSON> is just always there. He has so much presence', 'I’m so happy for <PERSON><PERSON> to stay at Liverpool. Magic player.', '\" Keep Saka\\'s name out your mouth,  dude is world class.', \"From a Liverpool fan, We didn't have <PERSON>, but I think those goals go in anyway, there's so much quality in this Arsenal side, I actually expected a loss, You lot are actually so good you drag our performances up with you, Especially Nunez...\", '1:23 Trent long pass moves like a golf ball, <PERSON><PERSON><PERSON> didnt even hv to control it', 'Running out of words to describe <PERSON><PERSON>', '<PERSON><PERSON> and <PERSON> were so good', 'Appreciation for <PERSON><PERSON> how he crossed path when <PERSON><PERSON><PERSON> ran with ball. Quick and genius thinking. <PERSON><PERSON> was just unstoppable! ']\n", "<PERSON><PERSON><PERSON> ber<PERSON>il disimpan di youtube_comments_i6IOiUi6IYY.csv\n"]}], "source": ["\n", "# print(comments)\n", "save_to_csv(comments, filename=f\"youtube_comments_{youtubeid}.csv\")\n", "# Tutup driver <PERSON><PERSON><PERSON>\n", "driver.quit()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}