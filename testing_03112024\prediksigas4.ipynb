{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Python312\\Lib\\site-packages\\sklearn\\metrics\\_regression.py:1211: UndefinedMetricWarning: R^2 score is not well-defined with less than two samples.\n", "  warnings.warn(msg, UndefinedMetricWarning)\n", "c:\\Python312\\Lib\\site-packages\\sklearn\\metrics\\_regression.py:1211: UndefinedMetricWarning: R^2 score is not well-defined with less than two samples.\n", "  warnings.warn(msg, UndefinedMetricWarning)\n", "c:\\Python312\\Lib\\site-packages\\sklearn\\metrics\\_regression.py:1211: UndefinedMetricWarning: R^2 score is not well-defined with less than two samples.\n", "  warnings.warn(msg, UndefinedMetricWarning)\n", "c:\\Python312\\Lib\\site-packages\\sklearn\\base.py:493: UserWarning: X does not have valid feature names, but LinearRegression was fitted with feature names\n", "  warnings.warn(\n", "c:\\Python312\\Lib\\site-packages\\sklearn\\base.py:493: UserWarning: X does not have valid feature names, but LinearRegression was fitted with feature names\n", "  warnings.warn(\n", "c:\\Python312\\Lib\\site-packages\\sklearn\\base.py:493: UserWarning: X does not have valid feature names, but LinearRegression was fitted with feature names\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Methane R^2: nan, Methane MSE: 1314.9459453786144\n", "CO R^2: nan, CO MSE: 28.790956741862285\n", "Hydrogen R^2: nan, Hydrogen MSE: 12.464908182528944\n", "Predicted Methane (ppm): 324.3433711790391\n", "Predicted CO (ppm): 77.96267248908296\n", "Predicted Hydrogen (ppm): 36.78719650655022\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Python312\\Lib\\site-packages\\sklearn\\base.py:493: UserWarning: X does not have valid feature names, but LinearRegression was fitted with feature names\n", "  warnings.warn(\n", "c:\\Python312\\Lib\\site-packages\\sklearn\\base.py:493: UserWarning: X does not have valid feature names, but LinearRegression was fitted with feature names\n", "  warnings.warn(\n", "c:\\Python312\\Lib\\site-packages\\sklearn\\base.py:493: UserWarning: X does not have valid feature names, but LinearRegression was fitted with feature names\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Predicted Methane (ppm) for input temperatures: 324.3433711790391\n", "Predicted CO (ppm) for input temperatures: 77.96267248908296\n", "Predicted Hydrogen (ppm) for input temperatures: 36.78719650655022\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABW0AAAMWCAYAAACKoqSLAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy80BEi2AAAACXBIWXMAAA9hAAAPYQGoP6dpAACs60lEQVR4nOzdd3hUddrG8XsSSCcJoSR0Agm9CQhKEUILHRdcRfRdULFgQRf7umtjFbu7il0X0MUCttCEUEVAQUGatARDkU4CCekkOe8fZ5OToWYgyZlJvp/r4spknilPAu+7ePPLfRyGYRgCAAAAAAAAALgFL7sXAAAAAAAAAABYCG0BAAAAAAAAwI0Q2gIAAAAAAACAGyG0BQAAAAAAAAA3QmgLAAAAAAAAAG6E0BYAAAAAAAAA3AihLQAAAAAAAAC4EUJbAAAAAAAAAHAjhLYAAAAAAAAA4EYIbQEAAHCWp59+Wg6Ho9zf9+WXX1aTJk3k7e2tDh06lPv7AwAAAO6A0BYAAKAcORyOEv1asWKF3auWu/j4eD3yyCPq3r27pk2bpueff/6iz/nhhx90/fXXq169evLx8VFISIi6du2qZ599VkeOHCmHrc+2YsUKjRw5UhEREfLx8VHt2rU1bNgwff3117bsU9E8//zz+vbbb+**************************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", "text/plain": ["<Figure size 1400x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "\n", "def load_and_prepare_data(data_path):\n", "    try:\n", "        df = pd.read_csv(data_path, delimiter=',', on_bad_lines='skip', skipinitialspace=True)\n", "    except pd.errors.<PERSON><PERSON>r<PERSON><PERSON>r as e:\n", "        print(f\"Error loading CSV: {e}\")\n", "        raise SystemExit(e)\n", "    \n", "    # Standardize column names\n", "    df.columns = df.columns.str.strip().str.upper()\n", "    # Remove rows with missing values\n", "    df.dropna(inplace=True)\n", "    # Fill missing values with column mean\n", "    df.fillna(df.mean(), inplace=True)\n", "    return df\n", "\n", "def split_data(df):\n", "    try:\n", "        X = df[['SUHU1', 'SUHU2', 'SUHU3', 'SUHU4']]\n", "        y_methane = df['METHANE']\n", "        y_co = df['CO']\n", "        y_hydrogen = df['HYDROGEN']\n", "    except KeyError as e:\n", "        print(f\"KeyError: {e}. Ensure temperature and gas component columns are present in the CSV.\")\n", "        raise SystemExit(e)\n", "    \n", "    # Split the dataset into training and testing sets (80% train, 20% test)\n", "    X_train, X_test, y_methane_train, y_methane_test = train_test_split(X, y_methane, test_size=0.2, random_state=42)\n", "    _, _, y_co_train, y_co_test = train_test_split(X, y_co, test_size=0.2, random_state=42)\n", "    _, _, y_hydrogen_train, y_hydrogen_test = train_test_split(X, y_hydrogen, test_size=0.2, random_state=42)\n", "    \n", "    return X_train, X_test, y_methane_train, y_methane_test, y_co_train, y_co_test, y_hydrogen_train, y_hydrogen_test\n", "\n", "def train_models(X_train, y_methane_train, y_co_train, y_hydrogen_train):\n", "    # Create and train the linear regression models for each gas component\n", "    methane_model = LinearRegression().fit(X_train, y_methane_train)\n", "    co_model = LinearRegression().fit(X_train, y_co_train)\n", "    hydrogen_model = LinearRegression().fit(X_train, y_hydrogen_train)\n", "    return methane_model, co_model, hydrogen_model\n", "\n", "def evaluate_models(models, X_test, y_tests):\n", "    results = {}\n", "    for model, y_test, name in zip(models, y_tests, ['Methane', 'CO', 'Hydrogen']):\n", "        y_pred = model.predict(X_test)\n", "        r2 = r2_score(y_test, y_pred)\n", "        mse = mean_squared_error(y_test, y_pred)\n", "        results[name] = {'R^2': r2, 'MSE': mse}\n", "    return results\n", "\n", "def predict_average(models, average_temp):\n", "    predictions = {}\n", "    for model, name in zip(models, ['Methane', 'CO', 'Hydrogen']):\n", "        predictions[name] = model.predict(average_temp)[0]\n", "    return predictions\n", "\n", "def predict_from_input(models):\n", "    try:\n", "        suhu1 = float(input(\"Enter average temperature for SUHU1 (in Celsius): \"))\n", "        suhu2 = float(input(\"Enter average temperature for SUHU2 (in Celsius): \"))\n", "        suhu3 = float(input(\"Enter average temperature for SUHU3 (in Celsius): \"))\n", "        suhu4 = float(input(\"Enter average temperature for SUHU4 (in Celsius): \"))\n", "        user_input = np.array([[suhu1, suhu2, suhu3, suhu4]])\n", "    except ValueError:\n", "        print(\"Invalid input. Please enter numeric values.\")\n", "        return\n", "    \n", "    predictions = predict_average(models, user_input)\n", "    for gas, value in predictions.items():\n", "        print(f\"Predicted {gas} (ppm) for input temperatures: {value}\")\n", "\n", "def plot_trends(df):\n", "    plt.figure(figsize=(14, 8))\n", "    plt.plot(df.index, df['METHANE'], label='Methane', color='blue')\n", "    plt.plot(df.index, df['CO'], label='CO', color='green')\n", "    plt.plot(df.index, df['HYDROGEN'], label='Hydrogen', color='red')\n", "    plt.xlabel('Index')\n", "    plt.ylabel('Gas Concentration')\n", "    plt.title('Trends of Gas Components')\n", "    plt.legend()\n", "    plt.xticks(rotation=45)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Main execution\n", "if __name__ == \"__main__\":\n", "    data_path = 'gasifikasibagus2.csv'\n", "    sheet1_df = load_and_prepare_data(data_path)\n", "    \n", "    X_train, X_test, y_methane_train, y_methane_test, y_co_train, y_co_test, y_hydrogen_train, y_hydrogen_test = split_data(sheet1_df)\n", "    \n", "    methane_model, co_model, hydrogen_model = train_models(X_train, y_methane_train, y_co_train, y_hydrogen_train)\n", "    \n", "    results = evaluate_models([methane_model, co_model, hydrogen_model], X_test, [y_methane_test, y_co_test, y_hydrogen_test])\n", "    for gas, metrics in results.items():\n", "        print(f\"{gas} R^2: {metrics['R^2']}, {gas} MSE: {metrics['MSE']}\")\n", "    \n", "    # Predict gas compositions for an average temperature of 300 degrees Celsius\n", "    average_temp = np.array([[300, 300, 300, 300]])\n", "    predictions = predict_average([methane_model, co_model, hydrogen_model], average_temp)\n", "    for gas, value in predictions.items():\n", "        print(f\"Predicted {gas} (ppm): {value}\")\n", "    \n", "    # Predict gas compositions based on user input temperatures\n", "    predict_from_input([methane_model, co_model, hydrogen_model])\n", "    \n", "    # Plot trends\n", "    plot_trends(sheet1_df)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}