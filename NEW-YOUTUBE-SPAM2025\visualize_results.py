#!/usr/bin/env python3
"""
YouTube Spam Classification - Enhanced Results Visualization
===========================================================

This script creates comprehensive visualizations and analysis of model performance results.
Generates split-specific visualizations, comparison tables, and step-by-step analysis.

Author: AI Assistant
Date: 2025-06-29
"""

import json
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
import os
from datetime import datetime

def load_results(results_dir="NEW-YOUTUBE-SPAM2025/dataset"):
    """Load all training results"""
    results = {}
    
    result_files = [f for f in os.listdir(results_dir) if f.endswith('_results.json')]
    
    for file in result_files:
        split_name = file.replace('_results.json', '')
        with open(os.path.join(results_dir, file), 'r') as f:
            results[split_name] = json.load(f)
    
    return results

def create_split_specific_visualizations():
    """Create separate visualizations for each train/test split"""
    results = load_results()

    # Prepare data for visualization
    data = []
    for split, models in results.items():
        for model, metrics in models.items():
            data.append({
                'Split': split.replace('_', ' ').title(),
                'Split_Code': split,
                'Model': model.replace('_', ' ').title(),
                'Model_Code': model,
                'Accuracy': metrics['accuracy'],
                'Train_Size': metrics['train_size'],
                'Test_Size': metrics['test_size'],
                'Precision_0': metrics['classification_report']['0']['precision'],
                'Recall_0': metrics['classification_report']['0']['recall'],
                'F1_0': metrics['classification_report']['0']['f1-score'],
                'Precision_1': metrics['classification_report']['1']['precision'],
                'Recall_1': metrics['classification_report']['1']['recall'],
                'F1_1': metrics['classification_report']['1']['f1-score']
            })

    df = pd.DataFrame(data)

    # Create visualizations for each split
    splits = df['Split_Code'].unique()

    for split_code in splits:
        split_data = df[df['Split_Code'] == split_code]
        split_name = split_data['Split'].iloc[0]

        # Create figure for this split
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(f'YouTube Spam Classification - {split_name} Performance Analysis',
                    fontsize=16, fontweight='bold')

        # 1. Accuracy comparison
        models = split_data['Model']
        accuracies = split_data['Accuracy']
        colors = plt.cm.viridis(np.linspace(0, 1, len(models)))

        bars = axes[0,0].bar(models, accuracies, color=colors)
        axes[0,0].set_title(f'Model Accuracy - {split_name}')
        axes[0,0].set_ylabel('Accuracy')
        axes[0,0].tick_params(axis='x', rotation=45)

        # Add value labels on bars
        for bar, acc in zip(bars, accuracies):
            axes[0,0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
                          f'{acc:.3f}', ha='center', va='bottom', fontweight='bold')

        # 2. Precision-Recall comparison
        x = np.arange(len(models))
        width = 0.35

        axes[0,1].bar(x - width/2, split_data['Precision_1'], width, label='Spam Precision', alpha=0.8)
        axes[0,1].bar(x + width/2, split_data['Recall_1'], width, label='Spam Recall', alpha=0.8)
        axes[0,1].set_title(f'Spam Detection Metrics - {split_name}')
        axes[0,1].set_ylabel('Score')
        axes[0,1].set_xticks(x)
        axes[0,1].set_xticklabels(models, rotation=45)
        axes[0,1].legend()

        # 3. F1-Score comparison for both classes
        axes[1,0].bar(x - width/2, split_data['F1_0'], width, label='Ham F1-Score', alpha=0.8)
        axes[1,0].bar(x + width/2, split_data['F1_1'], width, label='Spam F1-Score', alpha=0.8)
        axes[1,0].set_title(f'F1-Score Comparison - {split_name}')
        axes[1,0].set_ylabel('F1-Score')
        axes[1,0].set_xticks(x)
        axes[1,0].set_xticklabels(models, rotation=45)
        axes[1,0].legend()

        # 4. Overall metrics radar-like comparison
        metrics_data = split_data[['Accuracy', 'Precision_1', 'Recall_1', 'F1_1']].values
        metric_names = ['Accuracy', 'Spam Precision', 'Spam Recall', 'Spam F1']

        x_pos = np.arange(len(metric_names))
        for i, (model, row) in enumerate(zip(models, metrics_data)):
            axes[1,1].plot(x_pos, row, marker='o', label=model, linewidth=2, markersize=6)

        axes[1,1].set_title(f'Overall Performance Metrics - {split_name}')
        axes[1,1].set_ylabel('Score')
        axes[1,1].set_xticks(x_pos)
        axes[1,1].set_xticklabels(metric_names, rotation=45)
        axes[1,1].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        axes[1,1].grid(True, alpha=0.3)

        plt.tight_layout()

        # Save split-specific visualization
        filename = f'NEW-YOUTUBE-SPAM2025/results/split_analysis/{split_code}_analysis.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✅ Created visualization: {filename}")

    return df

def create_overall_performance_comparison():
    """Create overall performance comparison visualizations"""
    results = load_results()

    # Prepare data for visualization
    data = []
    for split, models in results.items():
        for model, metrics in models.items():
            data.append({
                'Split': split.replace('_', ' ').title(),
                'Model': model.replace('_', ' ').title(),
                'Accuracy': metrics['accuracy'],
                'Train_Size': metrics['train_size'],
                'Test_Size': metrics['test_size']
            })

    df = pd.DataFrame(data)

    # Set up the plotting style
    plt.style.use('seaborn-v0_8')
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('YouTube Spam Classification - Overall Model Performance Analysis', fontsize=16, fontweight='bold')

    # 1. Accuracy comparison by model and split
    pivot_df = df.pivot(index='Model', columns='Split', values='Accuracy')
    sns.heatmap(pivot_df, annot=True, fmt='.3f', cmap='YlOrRd', ax=axes[0,0])
    axes[0,0].set_title('Accuracy Heatmap by Model and Split')
    axes[0,0].set_ylabel('Model')

    # 2. Bar plot of accuracies
    sns.barplot(data=df, x='Model', y='Accuracy', hue='Split', ax=axes[0,1])
    axes[0,1].set_title('Accuracy Comparison Across Models and Splits')
    axes[0,1].set_ylabel('Accuracy')
    axes[0,1].tick_params(axis='x', rotation=45)
    axes[0,1].legend(title='Split', bbox_to_anchor=(1.05, 1), loc='upper left')

    # 3. Model performance ranking
    model_avg = df.groupby('Model')['Accuracy'].mean().sort_values(ascending=False)
    sns.barplot(x=model_avg.values, y=model_avg.index, hue=model_avg.index, palette='viridis', ax=axes[1,0], legend=False)
    axes[1,0].set_title('Average Model Performance Ranking')
    axes[1,0].set_xlabel('Average Accuracy')

    # 4. Split performance comparison
    split_avg = df.groupby('Split')['Accuracy'].mean().sort_values(ascending=False)
    sns.barplot(x=split_avg.index, y=split_avg.values, hue=split_avg.index, palette='plasma', ax=axes[1,1], legend=False)
    axes[1,1].set_title('Average Performance by Train/Test Split')
    axes[1,1].set_ylabel('Average Accuracy')
    axes[1,1].tick_params(axis='x', rotation=45)

    plt.tight_layout()
    plt.savefig('NEW-YOUTUBE-SPAM2025/results/visualizations/overall_performance_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("✅ Created overall performance visualization")
    return df

def create_comparison_tables():
    """Create comprehensive comparison tables in CSV and XLSX formats"""
    results = load_results()

    # Prepare detailed data
    detailed_data = []
    summary_data = []

    for split, models in results.items():
        split_name = split.replace('_', ' ').title()

        for model, metrics in models.items():
            model_name = model.replace('_', ' ').title()

            # Detailed data with all metrics
            detailed_data.append({
                'Split': split_name,
                'Split_Code': split,
                'Model': model_name,
                'Model_Code': model,
                'Accuracy': metrics['accuracy'],
                'Train_Size': metrics['train_size'],
                'Test_Size': metrics['test_size'],
                'Ham_Precision': metrics['classification_report']['0']['precision'],
                'Ham_Recall': metrics['classification_report']['0']['recall'],
                'Ham_F1_Score': metrics['classification_report']['0']['f1-score'],
                'Ham_Support': metrics['classification_report']['0']['support'],
                'Spam_Precision': metrics['classification_report']['1']['precision'],
                'Spam_Recall': metrics['classification_report']['1']['recall'],
                'Spam_F1_Score': metrics['classification_report']['1']['f1-score'],
                'Spam_Support': metrics['classification_report']['1']['support'],
                'Macro_Avg_Precision': metrics['classification_report']['macro avg']['precision'],
                'Macro_Avg_Recall': metrics['classification_report']['macro avg']['recall'],
                'Macro_Avg_F1_Score': metrics['classification_report']['macro avg']['f1-score'],
                'Weighted_Avg_Precision': metrics['classification_report']['weighted avg']['precision'],
                'Weighted_Avg_Recall': metrics['classification_report']['weighted avg']['recall'],
                'Weighted_Avg_F1_Score': metrics['classification_report']['weighted avg']['f1-score']
            })

            # Summary data for quick comparison
            summary_data.append({
                'Split': split_name,
                'Model': model_name,
                'Accuracy': metrics['accuracy'],
                'Spam_Precision': metrics['classification_report']['1']['precision'],
                'Spam_Recall': metrics['classification_report']['1']['recall'],
                'Spam_F1_Score': metrics['classification_report']['1']['f1-score']
            })

    # Create DataFrames
    detailed_df = pd.DataFrame(detailed_data)
    summary_df = pd.DataFrame(summary_data)

    # Sort by accuracy descending
    detailed_df = detailed_df.sort_values('Accuracy', ascending=False)
    summary_df = summary_df.sort_values('Accuracy', ascending=False)

    # Save CSV files
    detailed_df.to_csv('NEW-YOUTUBE-SPAM2025/results/tables/detailed_results.csv', index=False)
    summary_df.to_csv('NEW-YOUTUBE-SPAM2025/results/tables/summary_results.csv', index=False)

    # Save XLSX files with multiple sheets
    with pd.ExcelWriter('NEW-YOUTUBE-SPAM2025/results/tables/complete_results.xlsx', engine='openpyxl') as writer:
        summary_df.to_excel(writer, sheet_name='Summary', index=False)
        detailed_df.to_excel(writer, sheet_name='Detailed_Metrics', index=False)

        # Create split-specific sheets
        for split_code in detailed_df['Split_Code'].unique():
            split_data = detailed_df[detailed_df['Split_Code'] == split_code]
            sheet_name = split_code.replace('_', '_').title()
            split_data.to_excel(writer, sheet_name=sheet_name, index=False)

    print("✅ Created comparison tables:")
    print("   - detailed_results.csv")
    print("   - summary_results.csv")
    print("   - complete_results.xlsx (with multiple sheets)")

    return detailed_df, summary_df

def create_step_by_step_analysis():
    """Create detailed step-by-step analysis of training results"""
    results = load_results()

    analysis_lines = []
    analysis_lines.append("YOUTUBE SPAM CLASSIFICATION - STEP-BY-STEP RESULTS ANALYSIS")
    analysis_lines.append("=" * 70)
    analysis_lines.append(f"Analysis Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    analysis_lines.append("")

    # Step 1: Dataset Overview
    analysis_lines.append("STEP 1: DATASET OVERVIEW")
    analysis_lines.append("-" * 30)
    analysis_lines.append("• Original Dataset: 1,956 YouTube comments")
    analysis_lines.append("• After Cleaning: 1,896 comments")
    analysis_lines.append("• Class Distribution: 955 Spam (50.4%) | 941 Ham (49.6%)")
    analysis_lines.append("• Features: Text content with TF-IDF vectorization")
    analysis_lines.append("• Target: Binary classification (0=Ham, 1=Spam)")
    analysis_lines.append("")

    # Step 2: Training Configuration
    analysis_lines.append("STEP 2: TRAINING CONFIGURATION")
    analysis_lines.append("-" * 30)
    analysis_lines.append("• Models Trained: 4 algorithms × 3 splits = 12 total models")
    analysis_lines.append("• Algorithms: Naive Bayes, Logistic Regression, Random Forest, SVM")
    analysis_lines.append("• Train/Test Splits: 75/25, 70/30, 65/35")
    analysis_lines.append("• Feature Engineering: TF-IDF with 5,000 features, unigrams + bigrams")
    analysis_lines.append("• Cross-validation: Stratified splits to maintain class balance")
    analysis_lines.append("")

    # Step 3: Results by Split
    step_num = 3
    for split, models in results.items():
        split_name = split.replace('_', ' ').title()
        analysis_lines.append(f"STEP {step_num}: {split_name.upper()} RESULTS")
        analysis_lines.append("-" * 30)

        # Sort models by accuracy
        sorted_models = sorted(models.items(), key=lambda x: x[1]['accuracy'], reverse=True)

        analysis_lines.append(f"Training Set: {sorted_models[0][1]['train_size']} samples")
        analysis_lines.append(f"Test Set: {sorted_models[0][1]['test_size']} samples")
        analysis_lines.append("")

        analysis_lines.append("Model Performance (sorted by accuracy):")
        for i, (model, metrics) in enumerate(sorted_models, 1):
            model_name = model.replace('_', ' ').title()
            acc = metrics['accuracy']
            spam_prec = metrics['classification_report']['1']['precision']
            spam_recall = metrics['classification_report']['1']['recall']
            spam_f1 = metrics['classification_report']['1']['f1-score']

            analysis_lines.append(f"{i}. {model_name}")
            analysis_lines.append(f"   • Accuracy: {acc:.3f} ({acc*100:.1f}%)")
            analysis_lines.append(f"   • Spam Precision: {spam_prec:.3f}")
            analysis_lines.append(f"   • Spam Recall: {spam_recall:.3f}")
            analysis_lines.append(f"   • Spam F1-Score: {spam_f1:.3f}")

        # Best model for this split
        best_model, best_metrics = sorted_models[0]
        analysis_lines.append("")
        analysis_lines.append(f"*** BEST MODEL: {best_model.replace('_', ' ').title()} ***")
        analysis_lines.append(f"   Accuracy: {best_metrics['accuracy']:.3f} ({best_metrics['accuracy']*100:.1f}%)")
        analysis_lines.append("")

        step_num += 1

    # Step 6: Overall Analysis
    analysis_lines.append(f"STEP {step_num}: OVERALL PERFORMANCE ANALYSIS")
    analysis_lines.append("-" * 30)

    # Collect all accuracies
    all_results = []
    for split, models in results.items():
        for model, metrics in models.items():
            all_results.append({
                'split': split,
                'model': model,
                'accuracy': metrics['accuracy']
            })

    # Find best overall
    best_overall = max(all_results, key=lambda x: x['accuracy'])
    worst_overall = min(all_results, key=lambda x: x['accuracy'])
    avg_accuracy = sum(r['accuracy'] for r in all_results) / len(all_results)

    analysis_lines.append(f"• Total Models Trained: {len(all_results)}")
    analysis_lines.append(f"• Best Overall Performance: {best_overall['accuracy']:.3f} ({best_overall['accuracy']*100:.1f}%)")
    analysis_lines.append(f"  Model: {best_overall['model'].replace('_', ' ').title()}")
    analysis_lines.append(f"  Split: {best_overall['split'].replace('_', ' ').title()}")
    analysis_lines.append(f"• Worst Performance: {worst_overall['accuracy']:.3f} ({worst_overall['accuracy']*100:.1f}%)")
    analysis_lines.append(f"• Average Accuracy: {avg_accuracy:.3f} ({avg_accuracy*100:.1f}%)")
    analysis_lines.append("")

    # Model ranking
    model_performance = {}
    for result in all_results:
        model = result['model']
        if model not in model_performance:
            model_performance[model] = []
        model_performance[model].append(result['accuracy'])

    model_averages = {model: sum(accs)/len(accs) for model, accs in model_performance.items()}
    sorted_models = sorted(model_averages.items(), key=lambda x: x[1], reverse=True)

    analysis_lines.append("MODEL RANKING (by average accuracy):")
    for i, (model, avg_acc) in enumerate(sorted_models, 1):
        model_name = model.replace('_', ' ').title()
        analysis_lines.append(f"{i}. {model_name}: {avg_acc:.3f} ({avg_acc*100:.1f}%)")

    analysis_lines.append("")

    # Split ranking
    split_performance = {}
    for result in all_results:
        split = result['split']
        if split not in split_performance:
            split_performance[split] = []
        split_performance[split].append(result['accuracy'])

    split_averages = {split: sum(accs)/len(accs) for split, accs in split_performance.items()}
    sorted_splits = sorted(split_averages.items(), key=lambda x: x[1], reverse=True)

    analysis_lines.append("SPLIT RANKING (by average accuracy):")
    for i, (split, avg_acc) in enumerate(sorted_splits, 1):
        split_name = split.replace('_', ' ').title()
        analysis_lines.append(f"{i}. {split_name}: {avg_acc:.3f} ({avg_acc*100:.1f}%)")

    analysis_lines.append("")
    analysis_lines.append("=" * 70)
    analysis_lines.append("ANALYSIS COMPLETE")
    analysis_lines.append("=" * 70)

    # Save analysis
    analysis_path = 'NEW-YOUTUBE-SPAM2025/results/step_by_step_analysis.txt'
    with open(analysis_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(analysis_lines))

    print(f"✅ Created step-by-step analysis: {analysis_path}")
    return analysis_lines

def print_summary_stats():
    """Print summary statistics"""
    results = load_results()
    
    print("YOUTUBE SPAM CLASSIFICATION - PERFORMANCE SUMMARY")
    print("=" * 55)
    
    all_accuracies = []
    best_models = {}
    
    for split, models in results.items():
        print(f"\n{split.replace('_', ' ').upper()}:")
        print("-" * 30)
        
        split_accuracies = []
        best_acc = 0
        best_model = ""
        
        for model, metrics in models.items():
            accuracy = metrics['accuracy']
            all_accuracies.append(accuracy)
            split_accuracies.append(accuracy)
            
            print(f"{model.replace('_', ' ').title():20}: {accuracy:.3f}")
            
            if accuracy > best_acc:
                best_acc = accuracy
                best_model = model
        
        best_models[split] = (best_model, best_acc)
        print(f"{'Best Model':20}: {best_model.replace('_', ' ').title()} ({best_acc:.3f})")
    
    print(f"\n{'='*55}")
    print("OVERALL STATISTICS:")
    print(f"{'='*55}")
    print(f"Total Models Trained: {len(all_accuracies)}")
    print(f"Average Accuracy: {sum(all_accuracies)/len(all_accuracies):.3f}")
    print(f"Best Overall Accuracy: {max(all_accuracies):.3f}")
    print(f"Worst Accuracy: {min(all_accuracies):.3f}")
    print(f"Standard Deviation: {pd.Series(all_accuracies).std():.3f}")
    
    print(f"\n{'='*55}")
    print("BEST MODELS BY SPLIT:")
    print(f"{'='*55}")
    for split, (model, acc) in best_models.items():
        print(f"{split.replace('_', ' ').title():20}: {model.replace('_', ' ').title()} ({acc:.3f})")

def main():
    """Main function to generate all visualizations and analyses"""
    print("YOUTUBE SPAM CLASSIFICATION - COMPREHENSIVE RESULTS ANALYSIS")
    print("=" * 65)
    print("Generating visualizations, tables, and step-by-step analysis...")
    print()

    try:
        # Step 1: Create split-specific visualizations
        print("📊 STEP 1: Creating split-specific visualizations...")
        df = create_split_specific_visualizations()

        # Step 2: Create overall performance comparison
        print("\n📊 STEP 2: Creating overall performance comparison...")
        create_overall_performance_comparison()

        # Step 3: Generate comparison tables
        print("\n📋 STEP 3: Generating comparison tables...")
        detailed_df, summary_df = create_comparison_tables()

        # Step 4: Create step-by-step analysis
        print("\n📝 STEP 4: Creating step-by-step analysis...")
        create_step_by_step_analysis()

        # Step 5: Print summary statistics
        print("\n📈 STEP 5: Printing summary statistics...")
        print_summary_stats()

        print(f"\n{'='*65}")
        print("✅ ALL ANALYSES COMPLETED SUCCESSFULLY!")
        print(f"{'='*65}")
        print("\nGenerated Files:")
        print("📁 /results/split_analysis/ - Individual split visualizations")
        print("📁 /results/visualizations/ - Overall performance charts")
        print("📁 /results/tables/ - CSV and XLSX comparison tables")
        print("📄 /results/step_by_step_analysis.txt - Detailed analysis")
        print(f"{'='*65}")

    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        print("Note: Some dependencies may not be available")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
