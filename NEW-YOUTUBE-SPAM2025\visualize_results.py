#!/usr/bin/env python3
"""
YouTube Spam Classification - Results Visualization
===================================================

This script creates visualizations of the model performance results.

Author: AI Assistant
Date: 2025-06-29
"""

import json
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import os

def load_results(results_dir="NEW-YOUTUBE-SPAM2025/dataset"):
    """Load all training results"""
    results = {}
    
    result_files = [f for f in os.listdir(results_dir) if f.endswith('_results.json')]
    
    for file in result_files:
        split_name = file.replace('_results.json', '')
        with open(os.path.join(results_dir, file), 'r') as f:
            results[split_name] = json.load(f)
    
    return results

def create_performance_comparison():
    """Create performance comparison visualizations"""
    results = load_results()
    
    # Prepare data for visualization
    data = []
    for split, models in results.items():
        for model, metrics in models.items():
            data.append({
                'Split': split.replace('_', ' ').title(),
                'Model': model.replace('_', ' ').title(),
                'Accuracy': metrics['accuracy'],
                'Train_Size': metrics['train_size'],
                'Test_Size': metrics['test_size']
            })
    
    df = pd.DataFrame(data)
    
    # Set up the plotting style
    plt.style.use('seaborn-v0_8')
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('YouTube Spam Classification - Model Performance Analysis', fontsize=16, fontweight='bold')
    
    # 1. Accuracy comparison by model and split
    pivot_df = df.pivot(index='Model', columns='Split', values='Accuracy')
    sns.heatmap(pivot_df, annot=True, fmt='.3f', cmap='YlOrRd', ax=axes[0,0])
    axes[0,0].set_title('Accuracy Heatmap by Model and Split')
    axes[0,0].set_ylabel('Model')
    
    # 2. Bar plot of accuracies
    sns.barplot(data=df, x='Model', y='Accuracy', hue='Split', ax=axes[0,1])
    axes[0,1].set_title('Accuracy Comparison Across Models and Splits')
    axes[0,1].set_ylabel('Accuracy')
    axes[0,1].tick_params(axis='x', rotation=45)
    axes[0,1].legend(title='Split', bbox_to_anchor=(1.05, 1), loc='upper left')
    
    # 3. Model performance ranking
    model_avg = df.groupby('Model')['Accuracy'].mean().sort_values(ascending=False)
    sns.barplot(x=model_avg.values, y=model_avg.index, palette='viridis', ax=axes[1,0])
    axes[1,0].set_title('Average Model Performance Ranking')
    axes[1,0].set_xlabel('Average Accuracy')
    
    # 4. Split performance comparison
    split_avg = df.groupby('Split')['Accuracy'].mean().sort_values(ascending=False)
    sns.barplot(x=split_avg.index, y=split_avg.values, palette='plasma', ax=axes[1,1])
    axes[1,1].set_title('Average Performance by Train/Test Split')
    axes[1,1].set_ylabel('Average Accuracy')
    axes[1,1].tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.savefig('NEW-YOUTUBE-SPAM2025/model_performance_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return df

def print_summary_stats():
    """Print summary statistics"""
    results = load_results()
    
    print("YOUTUBE SPAM CLASSIFICATION - PERFORMANCE SUMMARY")
    print("=" * 55)
    
    all_accuracies = []
    best_models = {}
    
    for split, models in results.items():
        print(f"\n{split.replace('_', ' ').upper()}:")
        print("-" * 30)
        
        split_accuracies = []
        best_acc = 0
        best_model = ""
        
        for model, metrics in models.items():
            accuracy = metrics['accuracy']
            all_accuracies.append(accuracy)
            split_accuracies.append(accuracy)
            
            print(f"{model.replace('_', ' ').title():20}: {accuracy:.3f}")
            
            if accuracy > best_acc:
                best_acc = accuracy
                best_model = model
        
        best_models[split] = (best_model, best_acc)
        print(f"{'Best Model':20}: {best_model.replace('_', ' ').title()} ({best_acc:.3f})")
    
    print(f"\n{'='*55}")
    print("OVERALL STATISTICS:")
    print(f"{'='*55}")
    print(f"Total Models Trained: {len(all_accuracies)}")
    print(f"Average Accuracy: {sum(all_accuracies)/len(all_accuracies):.3f}")
    print(f"Best Overall Accuracy: {max(all_accuracies):.3f}")
    print(f"Worst Accuracy: {min(all_accuracies):.3f}")
    print(f"Standard Deviation: {pd.Series(all_accuracies).std():.3f}")
    
    print(f"\n{'='*55}")
    print("BEST MODELS BY SPLIT:")
    print(f"{'='*55}")
    for split, (model, acc) in best_models.items():
        print(f"{split.replace('_', ' ').title():20}: {model.replace('_', ' ').title()} ({acc:.3f})")

if __name__ == "__main__":
    print("Generating performance visualizations...")
    
    try:
        df = create_performance_comparison()
        print("✅ Visualization saved as 'model_performance_analysis.png'")
    except Exception as e:
        print(f"❌ Visualization failed: {e}")
        print("Note: matplotlib may not be available in this environment")
    
    print("\n" + "="*60)
    print_summary_stats()
    print("="*60)
