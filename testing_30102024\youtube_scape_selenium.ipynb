{"cells": [{"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "from selenium import webdriver\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.options import Options\n", "import pandas as pd\n", "import time\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["\n", "# Path ke driver Selenium dan file cookie\n", "# driver_path = '/path/to/chromedriver'  # Ganti dengan path driver <PERSON><PERSON>\n", "cookie_path = 'youtube_cookies.json'\n", "profile_path = 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Google\\\\Chrome\\\\User Data\\\\Default'  # Ganti path ke profil Chrome Anda\n", "\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["\n", "def save_cookies(driver, path):\n", "    \"\"\"Menyimpan cookie dari Selenium WebDriver ke file JSON.\"\"\"\n", "    with open(path, 'w') as file:\n", "        json.dump(driver.get_cookies(), file)\n", "\n", "def load_cookies(driver, path):\n", "    \"\"\"Memuat cookie dari file JSON ke Selenium WebDriver.\"\"\"\n", "    if os.path.exists(path):\n", "        with open(path, 'r') as file:\n", "            cookies = json.load(file)\n", "            for cookie in cookies:\n", "                driver.add_cookie(cookie)\n", "\n", "def login_youtube(driver):\n", "    \"\"\"Login manual ke YouTube dan simpan cookie setelahnya.\"\"\"\n", "    driver.get(\"https://www.youtube.com\")\n", "    input(\"<PERSON><PERSON>an login di YouTube dan tekan Enter setelah selesai...\")  # Menunggu pengguna login manual\n", "    save_cookies(driver, cookie_path)\n", "\n", "def initialize_driver_with_profile(profile_path):\n", "    \"\"\"Inisialisasi driver dengan profil Chrome yang sudah ada.\"\"\"\n", "    chrome_options = Options()\n", "    chrome_options.add_argument(f\"user-data-dir={profile_path}\")\n", "    chrome_options.add_argument(\"--no-sandbox\")\n", "    chrome_options.add_argument(\"--disable-dev-shm-usage\")\n", "    # service = Service(driver_path)\n", "    driver = webdriver.Chrome(options=chrome_options)\n", "    return driver\n", "\n", "def get_youtube_comments(driver, video_url, max_comments=100):\n", "    \"\"\"Mengambil komentar dari video YouTube dengan sesi login.\"\"\"\n", "    driver.get(video_url)\n", "    time.sleep(3)  # Tunggu sampai halaman dimuat\n", "\n", "    comments = []\n", "    count = 0\n", "\n", "    while count < max_comments:\n", "        driver.execute_script(\"window.scrollBy(0, 300);\")  # Scroll untuk memuat lebih banyak komentar\n", "        time.sleep(1)\n", "\n", "        comment_elements = driver.find_elements(By.XPATH, '//*[@id=\"content-text\"]')\n", "        for element in comment_elements[count:max_comments]:\n", "            comment_text = element.text\n", "            if comment_text:\n", "                comments.append(comment_text)\n", "                count += 1\n", "            if count >= max_comments:\n", "                break\n", "\n", "    return comments\n", "\n", "def save_to_csv(comments, filename=\"youtube_comments.csv\"):\n", "    \"\"\"Menyimpan komentar ke file CSV.\"\"\"\n", "    df = pd.DataFrame(comments, columns=[\"Comment\"])\n", "    df.to_csv(filename, index=False)\n", "    print(f\"Komentar berhasil disimpan di {filename}\")\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["\n", "# Inisialisasi WebDriver dengan profil Chrome\n", "driver = initialize_driver_with_profile(profile_path)\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON><PERSON> ber<PERSON>il disimpan di youtube_comments.csv\n"]}], "source": ["\n", "# Cek jika cookie sudah ada; jika tidak, lakukan login manual\n", "if os.path.exists(cookie_path):\n", "    driver.get(\"https://www.youtube.com\")\n", "    load_cookies(driver, cookie_path)\n", "    driver.refresh()  # Refresh untuk menerapkan cookie\n", "else:\n", "    login_youtube(driver)  # Login manual dan simpan cookie\n", "\n", "# <PERSON><PERSON> komentar dari video\n", "video_url = \"https://www.youtube.com/watch?v=i6IOiUi6IYY\"  # Ganti dengan ID video yang diinginkan\n", "comments = get_youtube_comments(driver, video_url, max_comments=100)\n", "save_to_csv(comments)\n", "\n", "# Tutup driver <PERSON><PERSON><PERSON>\n", "driver.quit()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}