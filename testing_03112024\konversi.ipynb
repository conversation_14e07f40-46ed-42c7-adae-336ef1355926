{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved: gas<PERSON><PERSON><PERSON>\\Sheet1.csv\n", "Saved: gas<PERSON><PERSON><PERSON>\\Sheet2.csv\n", "Saved: gas<PERSON><PERSON><PERSON>\\Sheet3.csv\n", "Conversion complete!\n"]}], "source": ["# Import necessary libraries\n", "import pandas as pd\n", "import os\n", "\n", "# Input XLSX file path\n", "xlsx_file_path = \"databonggoljagunggasifikasi.xlsx\"  # Replace with your XLSX file name or path\n", "output_directory = \"gasifikasi\"    # Directory to save the CSV files\n", "\n", "# Create output directory if it doesn't exist\n", "if not os.path.exists(output_directory):\n", "    os.makedirs(output_directory)\n", "\n", "# Load the Excel file\n", "excel_data = pd.ExcelFile(xlsx_file_path)\n", "\n", "# Convert each sheet to a CSV file\n", "for sheet_name in excel_data.sheet_names:\n", "    # Read sheet into DataFrame\n", "    sheet_df = excel_data.parse(sheet_name)\n", "    \n", "    # Generate CSV file name based on sheet name\n", "    csv_file_path = os.path.join(output_directory, f\"{sheet_name}.csv\")\n", "    \n", "    # Save DataFrame to CSV\n", "    sheet_df.to_csv(csv_file_path, index=False)\n", "    print(f\"Saved: {csv_file_path}\")\n", "\n", "print(\"Conversion complete!\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}