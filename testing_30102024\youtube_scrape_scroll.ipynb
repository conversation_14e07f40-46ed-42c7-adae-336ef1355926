{"cells": [{"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["from selenium import webdriver\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "import time\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["\n", "# Path ke driver dan direktori profil Chrome\n", "driver_path = '/path/to/chromedriver'  # Ganti dengan path driver <PERSON><PERSON>\n", "# profile_path = r'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Google\\\\Chrome\\\\User Data\\\\Default'  # Path profil Chrome yang sudah login\n", "# profile_path = 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Google\\\\Chrome\\\\User Data\\\\Default'  # Ganti path ke profil Chrome Anda\n", "profile_path = r'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Default'  # Ganti sesuai path profil Chrome Anda\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'requests' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[12], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m----> 2\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[43mrequests\u001b[49m\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhttps://www.youtube.com\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m      3\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m response\u001b[38;5;241m.\u001b[39mstatus_code \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m200\u001b[39m:\n", "\u001b[1;31mNameError\u001b[0m: name 'requests' is not defined", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[12], line 7\u001b[0m\n\u001b[0;32m      5\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m:\n\u001b[0;32m      6\u001b[0m         \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mKoneksi ke YouTube tidak berhasil.\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m----> 7\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[43mrequests\u001b[49m\u001b[38;5;241m.\u001b[39mConnectionError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m      8\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mGagal menghubungi YouTube:\u001b[39m\u001b[38;5;124m\"\u001b[39m, e)\n", "\u001b[1;31mNameError\u001b[0m: name 'requests' is not defined"]}], "source": ["try:\n", "    response = requests.get(\"https://www.youtube.com\")\n", "    if response.status_code == 200:\n", "        print(\"Koneksi ke YouTube berhasil.\")\n", "    else:\n", "        print(\"Koneksi ke YouTube tidak berhasil.\")\n", "except requests.ConnectionError as e:\n", "    print(\"Gagal menghubungi YouTube:\", e)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "def initialize_driver_with_profile(profile_path):\n", "    \"\"\"Inisialisasi driver dengan profil Chrome yang sudah ada.\"\"\"\n", "    chrome_options = Options()\n", "    chrome_options.add_argument(f\"user-data-dir={profile_path}\")\n", "    chrome_options.add_argument(\"--no-sandbox\")\n", "    chrome_options.add_argument(\"--disable-dev-shm-usage\")\n", "    chrome_options.add_argument(\"--no-first-run\")\n", "    chrome_options.add_argument(\"--remote-debugging-port=9222\")\n", "    chrome_options.add_argument(\"--enable-logging\")\n", "    chrome_options.add_argument(\"--v=1\")  # Level logging dasar\n", "    # service = Service(driver_path)\n", "    driver = webdriver.Chrome(options=chrome_options)\n", "    return driver\n", "\n", "def get_all_youtube_comments(driver, video_url, max_comments=None):\n", "    \"\"\"Mengambil semua komentar dari video YouTube dengan scroll tanpa batas.\"\"\"\n", "    driver.get(video_url)\n", "    time.sleep(3)  # <PERSON><PERSON><PERSON> halaman dimuat\n", "\n", "    comments = []\n", "    last_height = driver.execute_script(\"return document.documentElement.scrollHeight\")\n", "    while True:\n", "        # Scroll ke bawah untuk memuat komentar baru\n", "        driver.execute_script(\"window.scrollTo(0, document.documentElement.scrollHeight);\")\n", "        time.sleep(2)  # <PERSON><PERSON> untuk memuat komentar baru\n", "\n", "        # Ambil elemen komentar\n", "        comment_elements = driver.find_elements(By.XPATH, '//*[@id=\"content-text\"]')\n", "        for element in comment_elements[len(comments):]:  # <PERSON><PERSON> dari komentar baru\n", "            comment_text = element.text\n", "            if comment_text:\n", "                comments.append(comment_text)\n", "                # <PERSON><PERSON> maksimal komentar jika disetel\n", "                if max_comments and len(comments) >= max_comments:\n", "                    return comments\n", "\n", "        # Cek tinggi baru halaman untuk melihat apakah ada yang baru dimuat\n", "        new_height = driver.execute_script(\"return document.documentElement.scrollHeight\")\n", "        if new_height == last_height:  # <PERSON><PERSON><PERSON><PERSON> jika tidak ada lagi yang dimuat\n", "            break\n", "        last_height = new_height\n", "\n", "    return comments\n", "\n", "def save_to_csv(comments, filename=\"youtube_comments.csv\"):\n", "    \"\"\"Menyimpan komentar ke file CSV.\"\"\"\n", "    df = pd.DataFrame(comments, columns=[\"Comment\"])\n", "    df.to_csv(filename, index=False)\n", "    print(f\"Komentar berhasil disimpan di {filename}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON><PERSON> ber<PERSON>il disimpan di youtube_comments_i6IOiUi6IYY.csv\n"]}], "source": ["# Inisialisasi WebDriver dengan profil Chrome\n", "driver = initialize_driver_with_profile(profile_path)\n", "# URL video YouTube dan jumlah komentar maksimal yang di<PERSON> (bisa None untuk semua)\n", "# HIGHLIGHTS | Arsenal vs Liverpool (2-2) | Premier League | <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>\n", "# video_url = \"https://www.youtube.com/watch?v=i6IOiUi6IYY\"  # Ganti dengan ID video yang diinginkan\n", "# URL video YouTube dan jumlah komentar maksimal yang di<PERSON>inkan\n", "# FULL HIGHLIGHT! INDONESIA (2) VS (0) ARAB SAUDI | AFC ASIAN QUALIFIERS\n", "idyoutube=\"4F2oOGDyWeY\"\n", "video_url = f\"https://www.youtube.com/watch?v={idyoutube}\"  # Menggunakan idyoutube sebagai variabel\n", "\n", "comments = get_all_youtube_comments(driver, video_url, max_comments=None)  # None untuk scroll tanpa batas\n", "# save_to_csv(comments)\n", "# Simpan komentar dengan nama file yang mengandung ID video\n", "filename = f\"youtube_comments_{idyoutube}.csv\"\n", "save_to_csv(comments, filename)\n", "\n", "# Tutup driver <PERSON><PERSON><PERSON>\n", "driver.quit()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}