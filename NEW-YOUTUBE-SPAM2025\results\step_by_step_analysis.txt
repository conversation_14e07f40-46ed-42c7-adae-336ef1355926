YOUTUBE SPAM CLASSIFICATION - STEP-BY-STEP RESULTS ANALYSIS
======================================================================
Analysis Generated: 2025-06-30 00:12:13

STEP 1: DATASET OVERVIEW
------------------------------
• Original Dataset: 1,956 YouTube comments
• After Cleaning: 1,896 comments
• Class Distribution: 955 Spam (50.4%) | 941 Ham (49.6%)
• Features: Text content with TF-IDF vectorization
• Target: Binary classification (0=Ham, 1=Spam)

STEP 2: TRAINING CONFIGURATION
------------------------------
• Models Trained: 4 algorithms × 3 splits = 12 total models
• Algorithms: Naive <PERSON>, Logistic Regression, Random Forest, SVM
• Train/Test Splits: 75/25, 70/30, 65/35
• Feature Engineering: TF-IDF with 5,000 features, unigrams + bigrams
• Cross-validation: Stratified splits to maintain class balance

STEP 3: TRAIN 65 TEST 35 RESULTS
------------------------------
Training Set: 1232 samples
Test Set: 664 samples

Model Performance (sorted by accuracy):
1. Svm
   • Accuracy: 0.923 (92.3%)
   • Spam Precision: 0.946
   • Spam Recall: 0.898
   • Spam F1-Score: 0.922
2. Logistic Regression
   • Accuracy: 0.914 (91.4%)
   • Spam Precision: 0.954
   • Spam Recall: 0.871
   • Spam F1-Score: 0.911
3. Random Forest
   • Accuracy: 0.907 (90.7%)
   • Spam Precision: 0.953
   • Spam Recall: 0.856
   • Spam F1-Score: 0.902
4. Naive Bayes
   • Accuracy: 0.881 (88.1%)
   • Spam Precision: 0.863
   • Spam Recall: 0.907
   • Spam F1-Score: 0.885

*** BEST MODEL: Svm ***
   Accuracy: 0.923 (92.3%)

STEP 4: TRAIN 70 TEST 30 RESULTS
------------------------------
Training Set: 1327 samples
Test Set: 569 samples

Model Performance (sorted by accuracy):
1. Random Forest
   • Accuracy: 0.930 (93.0%)
   • Spam Precision: 0.970
   • Spam Recall: 0.889
   • Spam F1-Score: 0.927
2. Svm
   • Accuracy: 0.930 (93.0%)
   • Spam Precision: 0.956
   • Spam Recall: 0.902
   • Spam F1-Score: 0.928
3. Logistic Regression
   • Accuracy: 0.923 (92.3%)
   • Spam Precision: 0.958
   • Spam Recall: 0.885
   • Spam F1-Score: 0.920
4. Naive Bayes
   • Accuracy: 0.882 (88.2%)
   • Spam Precision: 0.862
   • Spam Recall: 0.913
   • Spam F1-Score: 0.887

*** BEST MODEL: Random Forest ***
   Accuracy: 0.930 (93.0%)

STEP 5: TRAIN 75 TEST 25 RESULTS
------------------------------
Training Set: 1422 samples
Test Set: 474 samples

Model Performance (sorted by accuracy):
1. Svm
   • Accuracy: 0.930 (93.0%)
   • Spam Precision: 0.960
   • Spam Recall: 0.900
   • Spam F1-Score: 0.929
2. Logistic Regression
   • Accuracy: 0.924 (92.4%)
   • Spam Precision: 0.959
   • Spam Recall: 0.887
   • Spam F1-Score: 0.922
3. Random Forest
   • Accuracy: 0.920 (92.0%)
   • Spam Precision: 0.967
   • Spam Recall: 0.870
   • Spam F1-Score: 0.916
4. Naive Bayes
   • Accuracy: 0.884 (88.4%)
   • Spam Precision: 0.859
   • Spam Recall: 0.921
   • Spam F1-Score: 0.889

*** BEST MODEL: Svm ***
   Accuracy: 0.930 (93.0%)

STEP 6: OVERALL PERFORMANCE ANALYSIS
------------------------------
• Total Models Trained: 12
• Best Overall Performance: 0.930 (93.0%)
  Model: Svm
  Split: Train 75 Test 25
• Worst Performance: 0.881 (88.1%)
• Average Accuracy: 0.912 (91.2%)

MODEL RANKING (by average accuracy):
1. Svm: 0.928 (92.8%)
2. Logistic Regression: 0.920 (92.0%)
3. Random Forest: 0.919 (91.9%)
4. Naive Bayes: 0.882 (88.2%)

SPLIT RANKING (by average accuracy):
1. Train 70 Test 30: 0.916 (91.6%)
2. Train 75 Test 25: 0.915 (91.5%)
3. Train 65 Test 35: 0.906 (90.6%)

======================================================================
ANALYSIS COMPLETE
======================================================================