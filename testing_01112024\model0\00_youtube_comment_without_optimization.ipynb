{"cells": [{"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["import re\n", "import unicodedata\n", "from nltk.corpus import stopwords\n", "from nltk.tokenize import word_tokenize\n", "from nltk.stem import WordNetLemmatizer\n", "import nltk\n", "import pandas as pd\n", "from sklearn.model_selection import train_test_split, cross_val_predict, StratifiedKFold\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.metrics import precision_score, recall_score, f1_score, confusion_matrix, accuracy_score\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.naive_bayes import MultinomialNB\n", "from sklearn.svm import SVC\n", "from xgboost import XGBClassifier\n", "import joblib\n", "import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training Data Size: 1956\n", "Training Data Distribution:\n", "CLASS\n", "1    1005\n", "0     951\n", "Name: count, dtype: int64\n"]}], "source": ["\n", "# Load your training dataset\n", "file_path = '../dataset/youtube_spam.csv'\n", "train_data = pd.read_csv(file_path)\n", "train_texts = train_data['CONTENT']\n", "train_labels = train_data['CLASS']\n", "\n", "# Display training data size and distribution\n", "print(f'Training Data Size: {len(train_data)}')\n", "print('Training Data Distribution:')\n", "print(train_labels.value_counts())"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [], "source": ["def text_preprocessing(text):\n", "    # Lowercase all sentences\n", "    text = text.lower()\n", "    \n", "    # Remove numbers\n", "    text = re.sub(r'[-+]?[0-9]+', ' ', text)\n", "    \n", "    # Remove URLs\n", "    text = re.sub(r'https?://\\S+|www\\.\\S+', ' ', text)\n", "    \n", "    # Remove email addresses\n", "    text = re.sub(r'\\S*@\\S*\\s?', ' ', text)\n", "    \n", "    # Remove punctuation and other symbols\n", "    text = re.sub(r'[!$%^&*@#()_+|~=`{}\\[\\]%\\-:\";\\'<>?,.\\/]', ' ', text)\n", "\n", "    # Correct duplication of three or more characters in a row (e.g., \"yukkk\" -> \"yuk\")\n", "    text = re.sub(r'([a-zA-Z])\\1\\1+', r'\\1', text)\n", "    \n", "    # Remove multiple whitespace\n", "    text = re.sub(r' +', ' ', text)\n", "    \n", "    # Remove leading and trailing whitespace\n", "    text = text.strip()\n", "    \n", "    # Remove non-ASCII characters\n", "    text = unicodedata.normalize('NFKD', text).encode('ascii', 'ignore').decode('utf-8', 'ignore')\n", "    \n", "    # Tokenize words\n", "    word_tokens = word_tokenize(text)\n", "    \n", "    # Define English stopwords removal\n", "    stop_words = set(stopwords.words('english'))\n", "    clean_words = [word for word in word_tokens if word not in stop_words]\n", "    \n", "    # Join tokens back into a clean string\n", "    clean_words = ' '.join(clean_words)\n", "    \n", "    return clean_words\n"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [], "source": ["\n", "# Lemmatization function\n", "lemmatizer = WordNetLemmatizer()\n", "\n", "def lemmatize_text(text):\n", "    words = text.split()\n", "    words = [lemmatizer.lemmatize(word) for word in words]\n", "    return ' '.join(words)\n", "\n", "# Apply preprocessing to the training dataset\n", "train_texts = train_data['CONTENT'].apply(text_preprocessing).apply(lemmatize_text)\n", "train_labels = train_data['CLASS']\n", "\n", "# Convert the text data into TF-IDF feature vectors\n", "tfidf_vectorizer = TfidfVectorizer(stop_words='english', max_features=5000)\n", "X_train = tfidf_vectorizer.fit_transform(train_texts)\n"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [], "source": ["\n", "# Define models for training, including SVM with different kernels\n", "models = {\n", "    'RandomForest': RandomForestClassifier(n_estimators=100, max_depth=10, min_samples_split=2),\n", "    'NaiveBayes': MultinomialNB(alpha=0.5),\n", "    'XGBoost': XGBClassifier(use_label_encoder=False, eval_metric='logloss', n_estimators=100, max_depth=6, learning_rate=0.1),\n", "    'SVM_Linear': SVC(kernel='linear', probability=True, C=1),\n", "    'SVM_RBF': SVC(kernel='rbf', probability=True, C=1, gamma='scale'),\n", "    'SVM_Poly': SVC(kernel='poly', probability=True, C=1, degree=3),\n", "    'SVM_Sigmoid': SVC(kernel='sigmoid', probability=True, C=1)\n", "}\n"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Training and Cross-Validating RandomForest...\n", "Accuracy: 0.89\n", "Precision: 0.90\n", "Recall: 0.89\n", "F1 Score: 0.89\n", "Confusion Matrix:\n", "[[923  28]\n", " [195 810]]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Training and Cross-Validating NaiveBayes...\n", "Accuracy: 0.89\n", "Precision: 0.89\n", "Recall: 0.89\n", "F1 Score: 0.89\n", "Confusion Matrix:\n", "[[862  89]\n", " [119 886]]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Training and Cross-Validating XGBoost...\n", "Accuracy: 0.90\n", "Precision: 0.91\n", "Recall: 0.90\n", "F1 Score: 0.90\n", "Confusion Matrix:\n", "[[924  27]\n", " [161 844]]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Training and Cross-Validating SVM_Linear...\n", "Accuracy: 0.91\n", "Precision: 0.91\n", "Recall: 0.91\n", "F1 Score: 0.91\n", "Confusion Matrix:\n", "[[905  46]\n", " [134 871]]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Training and Cross-Validating SVM_RBF...\n", "Accuracy: 0.91\n", "Precision: 0.92\n", "Recall: 0.91\n", "F1 Score: 0.91\n", "Confusion Matrix:\n", "[[906  45]\n", " [126 879]]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Training and Cross-Validating SVM_Poly...\n", "Accuracy: 0.87\n", "Precision: 0.87\n", "Recall: 0.87\n", "F1 Score: 0.87\n", "Confusion Matrix:\n", "[[766 185]\n", " [ 70 935]]\n"]}, {"data": {"image/png": "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************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Training and Cross-Validating SVM_Sigmoid...\n", "Accuracy: 0.90\n", "Precision: 0.90\n", "Recall: 0.90\n", "F1 Score: 0.90\n", "Confusion Matrix:\n", "[[900  51]\n", " [147 858]]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Cross-validation results saved to cross_validation_results.csv\n"]}], "source": ["import seaborn as sns\n", "# # Train models and save them\n", "# for model_name, model in models.items():\n", "#     print(f'\\nTraining {model_name}...')\n", "#     model.fit(X_train, train_labels)\n", "#     # Save the trained model\n", "#     joblib.dump(model, f'{model_name}_model.pkl')\n", "\n", "# # Save the TF-IDF vectorizer\n", "# joblib.dump(tfidf_vectorizer, 'tfidf_vectorizer.pkl')\n", "\n", "# Cross-validation with predictions for each model\n", "cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)\n", "results = []\n", "for model_name, model in models.items():\n", "    print(f'\\nTraining and Cross-Validating {model_name}...')\n", "    # Apply cross-validation\n", "    predictions = cross_val_predict(model, X_train, train_labels, cv=cv)\n", "    \n", "    # Evaluate the model\n", "    accuracy = accuracy_score(train_labels, predictions)\n", "    precision = precision_score(train_labels, predictions, average='weighted')\n", "    recall = recall_score(train_labels, predictions, average='weighted')\n", "    f1 = f1_score(train_labels, predictions, average='weighted')\n", "    conf_matrix = confusion_matrix(train_labels, predictions)\n", "    \n", "    print(f'Accuracy: {accuracy:.2f}')\n", "    print(f'Precision: {precision:.2f}')\n", "    print(f'Recall: {recall:.2f}')\n", "    print(f'F1 Score: {f1:.2f}')\n", "    print(f'Confusion Matrix:\\n{conf_matrix}')\n", "\n", "    results.append({\n", "        'Model': model_name,\n", "        'Accuracy': accuracy,\n", "        'Precision': precision,\n", "        'Recall': recall,\n", "        'F1 Score': f1\n", "    })\n", "     # Plot confusion matrix\n", "    plt.figure(figsize=(8, 6))\n", "    sns.heatmap(conf_matrix, annot=True, fmt='d', cmap='Blues', xticklabels=['Non-Spam', 'Spam'], yticklabels=['Non-Spam', 'Spam'])\n", "    plt.xlabel('Predicted')\n", "    plt.ylabel('Actual')\n", "    plt.title(f'Confusion Matrix for {model_name}')\n", "    plt.show()\n", "    \n", "    # Save the trained model for each test size\n", "    # joblib.dump(model, f'{model_name}_model_test_size_{test_size}.pkl')\n", "    # Train the model on the entire dataset and save it\n", "    model.fit(X_train, train_labels)\n", "    joblib.dump(model, f'{model_name}_model_nonoptmize_.pkl')\n", "\n", "# Save the TF-IDF vectorizer\n", "joblib.dump(tfidf_vectorizer, 'tfidf_vectorizer_nonoptmize_.pkl')\n", "\n", "# Save cross-validation results to CSV\n", "results_df = pd.DataFrame(results)\n", "results_df.to_csv('cross_validation_results_nonoptmize_testsize0.2.csv', index=False)\n", "print('Cross-validation results saved to cross_validation_results.csv')"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset with cleaned comments saved to youtube_comments_with_cleaned_testsize0.2.csv\n", "\n", "Loading RandomForest model and making predictions...\n", "\n", "Loading NaiveBayes model and making predictions...\n", "\n", "Loading XGBoost model and making predictions...\n", "\n", "Loading SVM_Linear model and making predictions...\n", "\n", "Loading SVM_RBF model and making predictions...\n", "\n", "Loading SVM_Poly model and making predictions...\n", "\n", "Loading SVM_Sigmoid model and making predictions...\n"]}], "source": ["\n", "# Load and preprocess the testing dataset\n", "test_data_path = '../youtube_comments_i6IOiUi6IYY.xlsx'\n", "test_data = pd.read_excel(test_data_path)\n", "test_texts = test_data['Comment'].apply(text_preprocessing).apply(lemmatize_text)\n", "\n", "# Add cleaned comments to the original dataset\n", "test_data['cleaned_comment'] = test_texts\n", "\n", "# Save dataset with cleaned comments to CSV\n", "test_data.to_csv('youtube_comments_with_cleaned_nonoptmize_testsize0.2.csv', index=False)\n", "print('Dataset with cleaned comments saved to youtube_comments_with_cleaned_testsize0.2.csv')\n", "\n", "# Load the TF-IDF vectorizer and transform the test data\n", "loaded_vectorizer = joblib.load('tfidf_vectorizer_nonoptmize_.pkl')\n", "X_test = loaded_vectorizer.transform(test_texts)\n", "\n", "# Load models and make predictions\n", "for model_name in models.keys():\n", "    print(f'\\nLoading {model_name} model and making predictions...')\n", "    loaded_model = joblib.load(f'{model_name}_model_nonoptmize_.pkl')\n", "    predictions = loaded_model.predict(X_test)\n", "    test_data[f'{model_name}_Prediction'] = predictions\n"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 600x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Predictions saved to youtube_comments_predictions_nonoptmize_.xlsx\n"]}], "source": ["# Visualize the distribution of predictions for each model\n", "for model_name in models.keys():\n", "    plt.figure(figsize=(6, 4))\n", "    test_data[f'{model_name}_Prediction'].value_counts().plot(kind='bar', color=['skyblue', 'lightgreen'])\n", "    plt.xlabel('Class')\n", "    plt.ylabel('Count')\n", "    plt.title(f'Distribution of {model_name} Predictions')\n", "    plt.xticks(rotation=0)\n", "    plt.show()\n", "\n", "# Save the predictions to an Excel file\n", "test_data.to_excel('youtube_comments_predictions_nonoptmize_.xlsx', index=False)\n", "print('Predictions saved to youtube_comments_predictions_nonoptmize_.xlsx')\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}