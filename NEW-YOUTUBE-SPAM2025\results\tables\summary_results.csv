<PERSON>,<PERSON>,Accuracy,Spam_Precision,<PERSON><PERSON>_<PERSON>call,Spam_F1_Score
Train 75 Test 25,Svm,0.930379746835443,0.9598214285714286,0.899581589958159,0.9287257019438445
Train 70 Test 30,<PERSON>,0.929701230228471,0.9695817490494296,0.8885017421602788,0.9272727272727272
Train 70 Test 30,Svm,0.929701230228471,0.955719557195572,0.9024390243902439,0.9283154121863799
Train 75 Test 25,Logistic Regression,0.9240506329113924,0.9592760180995475,0.8870292887029289,0.9217391304347826
Train 65 Test 35,Svm,0.9231927710843374,0.9463722397476341,0.8982035928143712,0.9216589861751152
Train 70 Test 30,<PERSON><PERSON><PERSON> Regression,0.9226713532513181,0.9584905660377359,0.8850174216027874,0.9202898550724637
Train 75 Test 25,Random <PERSON>,0.919831223628692,0.9674418604651163,0.8702928870292888,0.9162995594713657
Train 65 Test 35,Logistic Regression,0.9141566265060241,0.9540983606557377,0.8712574850299402,0.9107981220657277
Train 65 Test 35,Random Forest,0.9066265060240963,0.9533333333333334,0.8562874251497006,0.9022082018927445
Train 75 Test 25,Naive Bayes,0.8839662447257384,0.859375,0.9205020920502092,0.8888888888888888
Train 70 Test 30,Naive Bayes,0.8822495606326889,0.8618421052631579,0.9128919860627178,0.8866328257191202
Train 65 Test 35,Naive Bayes,0.8810240963855421,0.8632478632478633,0.907185628742515,0.8846715328467153
