#!/usr/bin/env python3
"""
YouTube Spam Classification - Machine Learning Training Pipeline
================================================================

This script trains multiple machine learning models for spam/ham classification
on YouTube comments with different train/test splits (75/25, 70/30, 65/35).

Author: AI Assistant
Date: 2025-06-29
"""

import pandas as pd
import numpy as np
import pickle
import json
import os
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.naive_bayes import MultinomialNB
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.pipeline import Pipeline
import re
import string
import warnings
warnings.filterwarnings('ignore')

class YouTubeSpamClassifier:
    """YouTube Spam Classification Pipeline"""
    
    def __init__(self, data_path, output_dir):
        self.data_path = data_path
        self.output_dir = output_dir
        self.models = {}
        self.results = {}
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
    def load_data(self):
        """Load and initial preprocessing of the dataset"""
        print("Loading dataset...")
        self.df = pd.read_csv(self.data_path)
        print(f"Dataset shape: {self.df.shape}")
        print(f"Class distribution:\n{self.df['CLASS'].value_counts()}")
        
        # Remove any missing values
        self.df = self.df.dropna(subset=['CONTENT', 'CLASS'])
        print(f"After removing NaN values: {self.df.shape}")
        
    def preprocess_text(self, text):
        """Clean and preprocess text data"""
        if pd.isna(text):
            return ""
        
        # Convert to lowercase
        text = str(text).lower()
        
        # Remove URLs
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        text = re.sub(r'www\.(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        
        # Remove email addresses
        text = re.sub(r'\S+@\S+', '', text)
        
        # Remove special characters and digits
        text = re.sub(r'[^a-zA-Z\s]', '', text)
        
        # Remove extra whitespace
        text = ' '.join(text.split())
        
        return text
    
    def prepare_features(self):
        """Prepare features for machine learning"""
        print("Preprocessing text data...")
        self.df['cleaned_content'] = self.df['CONTENT'].apply(self.preprocess_text)
        
        # Remove empty content after cleaning
        self.df = self.df[self.df['cleaned_content'].str.len() > 0]
        print(f"After text cleaning: {self.df.shape}")
        
        self.X = self.df['cleaned_content']
        self.y = self.df['CLASS']
        
    def create_models(self):
        """Create machine learning models with TF-IDF vectorization"""
        models = {
            'naive_bayes': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=5000, stop_words='english', ngram_range=(1, 2))),
                ('classifier', MultinomialNB())
            ]),
            'logistic_regression': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=5000, stop_words='english', ngram_range=(1, 2))),
                ('classifier', LogisticRegression(random_state=42, max_iter=1000))
            ]),
            'random_forest': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=5000, stop_words='english', ngram_range=(1, 2))),
                ('classifier', RandomForestClassifier(n_estimators=100, random_state=42))
            ]),
            'svm': Pipeline([
                ('tfidf', TfidfVectorizer(max_features=5000, stop_words='english', ngram_range=(1, 2))),
                ('classifier', SVC(kernel='linear', random_state=42))
            ])
        }
        return models
    
    def train_and_evaluate(self, test_sizes=[0.25, 0.30, 0.35]):
        """Train models with different train/test splits"""
        models = self.create_models()
        
        for test_size in test_sizes:
            train_size = 1 - test_size
            split_name = f"train_{int(train_size*100)}_test_{int(test_size*100)}"
            print(f"\n{'='*60}")
            print(f"Training with {int(train_size*100)}% train / {int(test_size*100)}% test split")
            print(f"{'='*60}")
            
            # Split the data
            X_train, X_test, y_train, y_test = train_test_split(
                self.X, self.y, test_size=test_size, random_state=42, stratify=self.y
            )
            
            print(f"Train set size: {len(X_train)}")
            print(f"Test set size: {len(X_test)}")
            
            split_results = {}
            
            for model_name, model in models.items():
                print(f"\nTraining {model_name}...")
                
                # Train the model
                model.fit(X_train, y_train)
                
                # Make predictions
                y_pred = model.predict(X_test)
                
                # Calculate metrics
                accuracy = accuracy_score(y_test, y_pred)
                report = classification_report(y_test, y_pred, output_dict=True)
                conf_matrix = confusion_matrix(y_test, y_pred)
                
                print(f"Accuracy: {accuracy:.4f}")
                
                # Store results
                split_results[model_name] = {
                    'accuracy': accuracy,
                    'classification_report': report,
                    'confusion_matrix': conf_matrix.tolist(),
                    'train_size': len(X_train),
                    'test_size': len(X_test)
                }
                
                # Save the trained model
                model_filename = f"{split_name}_{model_name}_model.pkl"
                model_path = os.path.join(self.output_dir, model_filename)
                with open(model_path, 'wb') as f:
                    pickle.dump(model, f)
                print(f"Model saved: {model_filename}")
            
            self.results[split_name] = split_results
            
            # Save split results
            results_filename = f"{split_name}_results.json"
            results_path = os.path.join(self.output_dir, results_filename)
            with open(results_path, 'w') as f:
                json.dump(split_results, f, indent=2, default=str)
            print(f"Results saved: {results_filename}")
    
    def save_summary_report(self):
        """Save a comprehensive summary report"""
        print(f"\n{'='*60}")
        print("GENERATING SUMMARY REPORT")
        print(f"{'='*60}")
        
        summary = {
            'dataset_info': {
                'total_samples': len(self.df),
                'features': ['CONTENT'],
                'target': 'CLASS',
                'class_distribution': self.df['CLASS'].value_counts().to_dict()
            },
            'training_timestamp': datetime.now().isoformat(),
            'splits_trained': list(self.results.keys()),
            'models_trained': ['naive_bayes', 'logistic_regression', 'random_forest', 'svm'],
            'results': self.results
        }
        
        # Save summary
        summary_path = os.path.join(self.output_dir, 'training_summary.json')
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        # Create human-readable report
        report_lines = []
        report_lines.append("YOUTUBE SPAM CLASSIFICATION - TRAINING REPORT")
        report_lines.append("=" * 50)
        report_lines.append(f"Training Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"Dataset: {self.data_path}")
        report_lines.append(f"Total Samples: {len(self.df)}")
        report_lines.append(f"Class Distribution: {dict(self.df['CLASS'].value_counts())}")
        report_lines.append("")
        
        for split_name, split_results in self.results.items():
            report_lines.append(f"SPLIT: {split_name.upper()}")
            report_lines.append("-" * 30)
            
            for model_name, metrics in split_results.items():
                report_lines.append(f"{model_name.upper()}:")
                report_lines.append(f"  Accuracy: {metrics['accuracy']:.4f}")
                report_lines.append(f"  Train Size: {metrics['train_size']}")
                report_lines.append(f"  Test Size: {metrics['test_size']}")
                report_lines.append("")
            report_lines.append("")
        
        report_path = os.path.join(self.output_dir, 'training_report.txt')
        with open(report_path, 'w') as f:
            f.write('\n'.join(report_lines))
        
        print(f"Summary saved: training_summary.json")
        print(f"Report saved: training_report.txt")

def main():
    """Main training pipeline"""
    print("YOUTUBE SPAM CLASSIFICATION - ML TRAINING PIPELINE")
    print("=" * 55)
    
    # Configuration
    data_path = "dataset/youtube_spam.csv"
    output_dir = "NEW-YOUTUBE-SPAM2025/dataset"
    
    # Initialize classifier
    classifier = YouTubeSpamClassifier(data_path, output_dir)
    
    # Execute training pipeline
    classifier.load_data()
    classifier.prepare_features()
    classifier.train_and_evaluate(test_sizes=[0.25, 0.30, 0.35])
    classifier.save_summary_report()
    
    print(f"\n{'='*60}")
    print("TRAINING COMPLETED SUCCESSFULLY!")
    print(f"All models and results saved to: {output_dir}")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
