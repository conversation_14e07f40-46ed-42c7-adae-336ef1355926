{"cells": [{"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import requests\n", "import pandas as pd\n", "import re\n", "import json\n", "import time\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# URL dan header untuk permintaan HTTP\n", "USER_AGENT = \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36\"\n", "HEADERS = {\n", "    \"User-Agent\": USER_AGENT,\n", "}"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def get_initial_data(video_url):\n", "    \"\"\"Mengambil data awal dari HTML video untuk mendapatkan token komentar.\"\"\"\n", "    response = requests.get(video_url, headers=HEADERS)\n", "    if response.status_code != 200:\n", "        raise Exception(\"<PERSON><PERSON> mengakses halaman video.\")\n", "    return response.text\n", "\n", "def extract_comments_data(html):\n", "    \"\"\"Mengambil token awal dari halaman HTML untuk permintaan komentar pertama.\"\"\"\n", "    try:\n", "        token = re.search(r'\"continuation\":\"(.*?)\"', html).group(1)\n", "        session_id = re.search(r'\"INNERTUBE_CONTEXT_CLIENT_VERSION\":\"(.*?)\"', html).group(1)\n", "    except AttributeError:\n", "        raise Exception(\"Gagal menemukan token atau session ID.\")\n", "    return token, session_id\n", "\n", "def get_comments(video_url, max_comments=100):\n", "    \"\"\"Mengambil komentar dari video YouTube hingga mencapai jumlah maksimal atau habis.\"\"\"\n", "    html = get_initial_data(video_url)\n", "    token, session_id = extract_comments_data(html)\n", "\n", "    comments, count = [], 0\n", "    while token and count < max_comments:\n", "        ajax_url = f\"https://www.youtube.com/youtubei/v1/next?key={session_id}\"\n", "        params = {\n", "            \"continuation\": token,\n", "            \"context\": {\n", "                \"client\": {\n", "                    \"clientName\": \"WEB\",\n", "                    \"clientVersion\": session_id,\n", "                }\n", "            }\n", "        }\n", "        \n", "        response = requests.post(ajax_url, headers=HEADERS, json=params)\n", "        if response.status_code != 200:\n", "            break\n", "\n", "        data = response.json()\n", "        items = data.get(\"onResponseReceivedEndpoints\", [])[0].get(\"appendContinuationItemsAction\", {}).get(\"continuationItems\", [])\n", "        \n", "        for item in items:\n", "            comment = item.get(\"commentThreadRenderer\", {}).get(\"comment\", {}).get(\"commentRenderer\", {}).get(\"contentText\", {}).get(\"runs\", [{}])[0].get(\"text\")\n", "            if comment:\n", "                comments.append(comment)\n", "                count += 1\n", "            if count >= max_comments:\n", "                break\n", "\n", "        # Cek apakah ada token kelanjutan untuk mengambil komentar lebih lanjut\n", "        token = next(\n", "            (i[\"continuationItemRenderer\"][\"continuationEndpoint\"][\"continuationCommand\"][\"token\"]\n", "             for i in items if \"continuationItemRenderer\" in i),\n", "            None\n", "        )\n", "        time.sleep(0.5)  # <PERSON><PERSON> untuk mencegah batasan akses YouTube\n", "\n", "    return comments\n", "\n", "def save_to_csv(comments, filename=\"youtube_comments.csv\"):\n", "    \"\"\"Menyimpan komentar ke file CSV.\"\"\"\n", "    df = pd.DataFrame(comments, columns=[\"Comment\"])\n", "    df.to_csv(filename, index=False)\n", "    print(f\"Komentar berhasil disimpan di {filename}\")\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"ename": "Exception", "evalue": "<PERSON><PERSON> token atau session ID.", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[10], line 11\u001b[0m, in \u001b[0;36mextract_comments_data\u001b[1;34m(html)\u001b[0m\n\u001b[0;32m     10\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m---> 11\u001b[0m     token \u001b[38;5;241m=\u001b[39m \u001b[43mre\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msearch\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43mr\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcontinuation\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m:\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m(.*?)\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mhtml\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgroup\u001b[49m(\u001b[38;5;241m1\u001b[39m)\n\u001b[0;32m     12\u001b[0m     session_id \u001b[38;5;241m=\u001b[39m re\u001b[38;5;241m.\u001b[39msearch(\u001b[38;5;124mr\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mINNERTUBE_CONTEXT_CLIENT_VERSION\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m:\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m(.*?)\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m'\u001b[39m, html)\u001b[38;5;241m.\u001b[39mgroup(\u001b[38;5;241m1\u001b[39m)\n", "\u001b[1;31mAttributeError\u001b[0m: 'NoneType' object has no attribute 'group'", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[1;31mException\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[11], line 3\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# <PERSON><PERSON><PERSON> pen<PERSON>\u001b[39;00m\n\u001b[0;32m      2\u001b[0m video_url \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhttps://www.youtube.com/watch?v=i6IOiUi6IYY\u001b[39m\u001b[38;5;124m\"\u001b[39m  \u001b[38;5;66;03m# Ganti VIDEO_ID dengan ID video yang di<PERSON><PERSON>an\u001b[39;00m\n\u001b[1;32m----> 3\u001b[0m comments \u001b[38;5;241m=\u001b[39m \u001b[43mget_comments\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvideo_url\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_comments\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m100\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m      4\u001b[0m save_to_csv(comments)\n", "Cell \u001b[1;32mIn[10], line 20\u001b[0m, in \u001b[0;36mget_comments\u001b[1;34m(video_url, max_comments)\u001b[0m\n\u001b[0;32m     18\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Mengambil komentar dari video YouTube hingga mencapai jumlah maksimal atau habis.\"\"\"\u001b[39;00m\n\u001b[0;32m     19\u001b[0m html \u001b[38;5;241m=\u001b[39m get_initial_data(video_url)\n\u001b[1;32m---> 20\u001b[0m token, session_id \u001b[38;5;241m=\u001b[39m \u001b[43mextract_comments_data\u001b[49m\u001b[43m(\u001b[49m\u001b[43mhtml\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     22\u001b[0m comments, count \u001b[38;5;241m=\u001b[39m [], \u001b[38;5;241m0\u001b[39m\n\u001b[0;32m     23\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m token \u001b[38;5;129;01mand\u001b[39;00m count \u001b[38;5;241m<\u001b[39m max_comments:\n", "Cell \u001b[1;32mIn[10], line 14\u001b[0m, in \u001b[0;36mextract_comments_data\u001b[1;34m(html)\u001b[0m\n\u001b[0;32m     12\u001b[0m     session_id \u001b[38;5;241m=\u001b[39m re\u001b[38;5;241m.\u001b[39msearch(\u001b[38;5;124mr\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mINNERTUBE_CONTEXT_CLIENT_VERSION\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m:\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m(.*?)\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m'\u001b[39m, html)\u001b[38;5;241m.\u001b[39mgroup(\u001b[38;5;241m1\u001b[39m)\n\u001b[0;32m     13\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mAttributeError\u001b[39;00m:\n\u001b[1;32m---> 14\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mGagal menemukan token atau session ID.\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     15\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m token, session_id\n", "\u001b[1;31mException\u001b[0m: <PERSON><PERSON> token atau session ID."]}], "source": ["# <PERSON><PERSON><PERSON> pengg<PERSON>an\n", "video_url = \"https://www.youtube.com/watch?v=i6IOiUi6IYY\"  # Ganti VIDEO_ID dengan ID video yang diinginkan\n", "comments = get_comments(video_url, max_comments=100)\n", "save_to_csv(comments)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}