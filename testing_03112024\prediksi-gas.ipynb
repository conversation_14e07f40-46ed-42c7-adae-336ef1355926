{"cells": [{"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["         DATE                CLOCK  METHANE    CO  HYDROGEN  SUHU1  SUHU2  \\\n", "0  2023-10-19  1900-01-01 11:53:47      0.0  0.03      0.01    NaN  36.25   \n", "1  2023-10-19  1900-01-01 11:53:55      0.0  0.03      0.01    NaN  35.75   \n", "2  2023-10-19  1900-01-01 11:54:03      0.0  0.03      0.01    NaN  36.00   \n", "3  2023-10-19  1900-01-01 11:54:11      0.0  0.03      0.01    NaN  35.75   \n", "4  2023-10-19  1900-01-01 11:54:19      0.0  0.03      0.01    NaN  36.50   \n", "\n", "   SUHU3  SUHU4  \n", "0  36.50  36.50  \n", "1  36.50  35.75  \n", "2  36.25  36.50  \n", "3  35.75  36.50  \n", "4  36.25  36.50  \n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DATE</th>\n", "      <th>CLOCK</th>\n", "      <th>METHANE</th>\n", "      <th>CO</th>\n", "      <th>HYDROGEN</th>\n", "      <th>SUHU1</th>\n", "      <th>SUHU2</th>\n", "      <th>SUHU3</th>\n", "      <th>SUHU4</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-10-19</td>\n", "      <td>1900-01-01 11:53:47</td>\n", "      <td>0.00</td>\n", "      <td>0.03</td>\n", "      <td>0.01</td>\n", "      <td>NaN</td>\n", "      <td>36.25</td>\n", "      <td>36.50</td>\n", "      <td>36.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-10-19</td>\n", "      <td>1900-01-01 11:53:55</td>\n", "      <td>0.00</td>\n", "      <td>0.03</td>\n", "      <td>0.01</td>\n", "      <td>NaN</td>\n", "      <td>35.75</td>\n", "      <td>36.50</td>\n", "      <td>35.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-10-19</td>\n", "      <td>1900-01-01 11:54:03</td>\n", "      <td>0.00</td>\n", "      <td>0.03</td>\n", "      <td>0.01</td>\n", "      <td>NaN</td>\n", "      <td>36.00</td>\n", "      <td>36.25</td>\n", "      <td>36.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-10-19</td>\n", "      <td>1900-01-01 11:54:11</td>\n", "      <td>0.00</td>\n", "      <td>0.03</td>\n", "      <td>0.01</td>\n", "      <td>NaN</td>\n", "      <td>35.75</td>\n", "      <td>35.75</td>\n", "      <td>36.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-10-19</td>\n", "      <td>1900-01-01 11:54:19</td>\n", "      <td>0.00</td>\n", "      <td>0.03</td>\n", "      <td>0.01</td>\n", "      <td>NaN</td>\n", "      <td>36.50</td>\n", "      <td>36.25</td>\n", "      <td>36.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1034</th>\n", "      <td>2023-10-19</td>\n", "      <td>1900-01-01 14:13:35</td>\n", "      <td>0.03</td>\n", "      <td>0.27</td>\n", "      <td>6.57</td>\n", "      <td>NaN</td>\n", "      <td>179.50</td>\n", "      <td>209.25</td>\n", "      <td>237.25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1035</th>\n", "      <td>2023-10-19</td>\n", "      <td>1900-01-01 14:13:35</td>\n", "      <td>0.03</td>\n", "      <td>0.28</td>\n", "      <td>6.69</td>\n", "      <td>NaN</td>\n", "      <td>179.25</td>\n", "      <td>207.25</td>\n", "      <td>235.25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1036</th>\n", "      <td>2023-10-19</td>\n", "      <td>1900-01-01 14:13:36</td>\n", "      <td>0.03</td>\n", "      <td>0.29</td>\n", "      <td>6.57</td>\n", "      <td>NaN</td>\n", "      <td>178.00</td>\n", "      <td>205.00</td>\n", "      <td>231.25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1037</th>\n", "      <td>2023-10-19</td>\n", "      <td>1900-01-01 14:13:37</td>\n", "      <td>0.02</td>\n", "      <td>0.30</td>\n", "      <td>6.15</td>\n", "      <td>NaN</td>\n", "      <td>177.50</td>\n", "      <td>202.75</td>\n", "      <td>228.25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1038</th>\n", "      <td>2023-10-19</td>\n", "      <td>1900-01-01 14:13:38</td>\n", "      <td>0.03</td>\n", "      <td>0.30</td>\n", "      <td>6.39</td>\n", "      <td>NaN</td>\n", "      <td>176.50</td>\n", "      <td>201.25</td>\n", "      <td>224.75</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1039 rows × 9 columns</p>\n", "</div>"], "text/plain": ["            DATE                CLOCK  METHANE    CO  HYDROGEN  SUHU1   SUHU2  \\\n", "0     2023-10-19  1900-01-01 11:53:47     0.00  0.03      0.01    NaN   36.25   \n", "1     2023-10-19  1900-01-01 11:53:55     0.00  0.03      0.01    NaN   35.75   \n", "2     2023-10-19  1900-01-01 11:54:03     0.00  0.03      0.01    NaN   36.00   \n", "3     2023-10-19  1900-01-01 11:54:11     0.00  0.03      0.01    NaN   35.75   \n", "4     2023-10-19  1900-01-01 11:54:19     0.00  0.03      0.01    NaN   36.50   \n", "...          ...                  ...      ...   ...       ...    ...     ...   \n", "1034  2023-10-19  1900-01-01 14:13:35     0.03  0.27      6.57    NaN  179.50   \n", "1035  2023-10-19  1900-01-01 14:13:35     0.03  0.28      6.69    NaN  179.25   \n", "1036  2023-10-19  1900-01-01 14:13:36     0.03  0.29      6.57    NaN  178.00   \n", "1037  2023-10-19  1900-01-01 14:13:37     0.02  0.30      6.15    NaN  177.50   \n", "1038  2023-10-19  1900-01-01 14:13:38     0.03  0.30      6.39    NaN  176.50   \n", "\n", "       SUHU3   SUHU4  \n", "0      36.50   36.50  \n", "1      36.50   35.75  \n", "2      36.25   36.50  \n", "3      35.75   36.50  \n", "4      36.25   36.50  \n", "...      ...     ...  \n", "1034  209.25  237.25  \n", "1035  207.25  235.25  \n", "1036  205.00  231.25  \n", "1037  202.75  228.25  \n", "1038  201.25  224.75  \n", "\n", "[1039 rows x 9 columns]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "\n", "# Load the new CSV file\n", "data_path = 'gasifikasibagus.csv'\n", "try:\n", "    sheet1_df = pd.read_csv(data_path, delimiter=',', on_bad_lines='skip')\n", "except pd.errors.<PERSON><PERSON>r<PERSON><PERSON>r as e:\n", "    print(f\"Error loading CSV: {e}\")\n", "    raise SystemExit(e)\n", "\n", "# Remove extra spaces from column names for easier access\n", "sheet1_df.columns = sheet1_df.columns.str.strip()\n", "\n", "# Inspect the first few rows to understand the data structure\n", "print(sheet1_df.head())\n", "sheet1_df\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Methane R^2: nan, Methane MSE: 1314.9459453786144\n", "CO R^2: nan, CO MSE: 28.790956741862285\n", "Hydrogen R^2: nan, Hydrogen MSE: 12.464908182528944\n", "Predicted Methane (ppm): 324.3433711790391\n", "Predicted CO (ppm): 77.96267248908296\n", "Predicted Hydrogen (ppm): 36.78719650655022\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Python312\\Lib\\site-packages\\sklearn\\metrics\\_regression.py:1211: UndefinedMetricWarning: R^2 score is not well-defined with less than two samples.\n", "  warnings.warn(msg, UndefinedMetricWarning)\n", "c:\\Python312\\Lib\\site-packages\\sklearn\\metrics\\_regression.py:1211: UndefinedMetricWarning: R^2 score is not well-defined with less than two samples.\n", "  warnings.warn(msg, UndefinedMetricWarning)\n", "c:\\Python312\\Lib\\site-packages\\sklearn\\metrics\\_regression.py:1211: UndefinedMetricWarning: R^2 score is not well-defined with less than two samples.\n", "  warnings.warn(msg, UndefinedMetricWarning)\n", "c:\\Python312\\Lib\\site-packages\\sklearn\\base.py:493: UserWarning: X does not have valid feature names, but LinearRegression was fitted with feature names\n", "  warnings.warn(\n", "c:\\Python312\\Lib\\site-packages\\sklearn\\base.py:493: UserWarning: X does not have valid feature names, but LinearRegression was fitted with feature names\n", "  warnings.warn(\n", "c:\\Python312\\Lib\\site-packages\\sklearn\\base.py:493: UserWarning: X does not have valid feature names, but LinearRegression was fitted with feature names\n", "  warnings.warn(\n"]}, {"ename": "KeyError", "evalue": "'Methane'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python312\\site-packages\\pandas\\core\\indexes\\base.py:3805\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3804\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 3805\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_engine\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcasted_key\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   3806\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01m<PERSON>ey<PERSON><PERSON>r\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[1;32mindex.pyx:167\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mindex.pyx:196\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\\\_libs\\\\hashtable_class_helper.pxi:7081\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\\\_libs\\\\hashtable_class_helper.pxi:7089\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'Methane'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[25], line 57\u001b[0m\n\u001b[0;32m     55\u001b[0m \u001b[38;5;66;03m# Plotting the trends of gas components over the index (assuming no datetime column is available)\u001b[39;00m\n\u001b[0;32m     56\u001b[0m plt\u001b[38;5;241m.\u001b[39mfigure(figsize\u001b[38;5;241m=\u001b[39m(\u001b[38;5;241m14\u001b[39m, \u001b[38;5;241m8\u001b[39m))\n\u001b[1;32m---> 57\u001b[0m plt\u001b[38;5;241m.\u001b[39mplot(sheet1_df\u001b[38;5;241m.\u001b[39mindex, \u001b[43msheet1_df\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mMethane\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m, label\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mMethane\u001b[39m\u001b[38;5;124m'\u001b[39m, color\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mblue\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m     58\u001b[0m plt\u001b[38;5;241m.\u001b[39mplot(sheet1_df\u001b[38;5;241m.\u001b[39mindex, sheet1_df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mCO\u001b[39m\u001b[38;5;124m'\u001b[39m], label\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mCO\u001b[39m\u001b[38;5;124m'\u001b[39m, color\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mgreen\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m     59\u001b[0m plt\u001b[38;5;241m.\u001b[39mplot(sheet1_df\u001b[38;5;241m.\u001b[39mindex, sheet1_df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mHydrogen\u001b[39m\u001b[38;5;124m'\u001b[39m], label\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mHydrogen\u001b[39m\u001b[38;5;124m'\u001b[39m, color\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mred\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python312\\site-packages\\pandas\\core\\frame.py:4102\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   4100\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mnlevels \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m   4101\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_multilevel(key)\n\u001b[1;32m-> 4102\u001b[0m indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   4103\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_integer(indexer):\n\u001b[0;32m   4104\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m [indexer]\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python312\\site-packages\\pandas\\core\\indexes\\base.py:3812\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3807\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(casted_key, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m (\n\u001b[0;32m   3808\u001b[0m         \u001b[38;5;28misinstance\u001b[39m(casted_key, abc\u001b[38;5;241m.\u001b[39mIterable)\n\u001b[0;32m   3809\u001b[0m         \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28many\u001b[39m(\u001b[38;5;28misinstance\u001b[39m(x, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m casted_key)\n\u001b[0;32m   3810\u001b[0m     ):\n\u001b[0;32m   3811\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m InvalidIndexError(key)\n\u001b[1;32m-> 3812\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(key) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01merr\u001b[39;00m\n\u001b[0;32m   3813\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m:\n\u001b[0;32m   3814\u001b[0m     \u001b[38;5;66;03m# If we have a listlike key, _check_indexing_error will raise\u001b[39;00m\n\u001b[0;32m   3815\u001b[0m     \u001b[38;5;66;03m#  InvalidIndexError. Otherwise we fall through and re-raise\u001b[39;00m\n\u001b[0;32m   3816\u001b[0m     \u001b[38;5;66;03m#  the TypeError.\u001b[39;00m\n\u001b[0;32m   3817\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_indexing_error(key)\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'Methane'"]}, {"data": {"text/plain": ["<Figure size 1400x800 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "# Remove rows with missing values\n", "sheet1_df.dropna(inplace=True)\n", "\n", "# Define features (temperature measurements) and target (gas components)\n", "try:\n", "    X = sheet1_df[['SUHU1', 'SUHU2', 'SUHU3', 'SUHU4']]    \n", "    y_methane = sheet1_df['METHANE']\n", "    y_co = sheet1_df['CO']\n", "    y_hydrogen = sheet1_df['HYDROGEN']\n", "except KeyError as e:\n", "    print(f\"KeyError: {e}. Ensure temperature and gas component columns are present in the CSV.\")\n", "    raise SystemExit(e)\n", "\n", "# Split the dataset into training and testing sets (80% train, 20% test)\n", "X_train, X_test, y_methane_train, y_methane_test = train_test_split(X, y_methane, test_size=0.2, random_state=42)\n", "X_train, X_test, y_co_train, y_co_test = train_test_split(X, y_co, test_size=0.2, random_state=42)\n", "X_train, X_test, y_hydrogen_train, y_hydrogen_test = train_test_split(X, y_hydrogen, test_size=0.2, random_state=42)\n", "\n", "# Create and train the linear regression models for each gas component\n", "methane_model = LinearRegression().fit(X_train, y_methane_train)\n", "co_model = LinearRegression().fit(X_train, y_co_train)\n", "hydrogen_model = LinearRegression().fit(X_train, y_hydrogen_train)\n", "\n", "# Predict gas compositions on the test set\n", "y_methane_pred = methane_model.predict(X_test)\n", "y_co_pred = co_model.predict(X_test)\n", "y_hydrogen_pred = hydrogen_model.predict(X_test)\n", "\n", "# Evaluate the models using R^2 and Mean Squared Error\n", "methane_r2 = r2_score(y_methane_test, y_methane_pred)\n", "methane_mse = mean_squared_error(y_methane_test, y_methane_pred)\n", "\n", "co_r2 = r2_score(y_co_test, y_co_pred)\n", "co_mse = mean_squared_error(y_co_test, y_co_pred)\n", "\n", "hydrogen_r2 = r2_score(y_hydrogen_test, y_hydrogen_pred)\n", "hydrogen_mse = mean_squared_error(y_hydrogen_test, y_hydrogen_pred)\n", "\n", "# Display evaluation results\n", "print(f\"Methane R^2: {methane_r2}, Methane MSE: {methane_mse}\")\n", "print(f\"CO R^2: {co_r2}, CO MSE: {co_mse}\")\n", "print(f\"Hydrogen R^2: {hydrogen_r2}, Hydrogen MSE: {hydrogen_mse}\")\n", "\n", "# Predict gas compositions for an average temperature of 300 degrees Celsius\n", "average_temp = np.array([[300, 300, 300, 300]])\n", "\n", "predicted_methane = methane_model.predict(average_temp)\n", "predicted_co = co_model.predict(average_temp)\n", "predicted_hydrogen = hydrogen_model.predict(average_temp)\n", "\n", "print(f\"Predicted Methane (ppm): {predicted_methane[0]}\")\n", "print(f\"Predicted CO (ppm): {predicted_co[0]}\")\n", "print(f\"Predicted Hydrogen (ppm): {predicted_hydrogen[0]}\")\n", "\n", "# Plotting the trends of gas components over the index (assuming no datetime column is available)\n", "plt.figure(figsize=(14, 8))\n", "plt.plot(sheet1_df.index, sheet1_df['Methane'], label='Methane', color='blue')\n", "plt.plot(sheet1_df.index, sheet1_df['CO'], label='CO', color='green')\n", "plt.plot(sheet1_df.index, sheet1_df['Hydrogen'], label='Hydrogen', color='red')\n", "plt.xlabel('Index')\n", "plt.ylabel('Gas Concentration')\n", "plt.title('Trends of Gas Components')\n", "plt.legend()\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.show()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}