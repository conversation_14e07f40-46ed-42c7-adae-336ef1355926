{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.ensemble import RandomForestClassifier\n", "\n", "# Train Random Forest Classifier on the dataset\n", "rf_model = RandomForestClassifier(n_estimators=100, random_state=42)\n", "rf_model.fit(X_train, y_train)\n", "\n", "# Extract feature importance\n", "feature_importance = rf_model.feature_importances_\n", "\n", "# Get feature names from TF-IDF\n", "feature_names = tfidf.get_feature_names_out()\n", "\n", "# Sort and select top 20 features\n", "sorted_idx = np.argsort(feature_importance)[-20:]\n", "top_features = feature_importance[sorted_idx]\n", "top_feature_names = feature_names[sorted_idx]\n", "\n", "# Plot feature importance\n", "plt.figure(figsize=(10, 8))\n", "plt.barh(range(len(sorted_idx)), top_features, align='center')\n", "plt.yticks(range(len(sorted_idx)), top_feature_names)\n", "plt.title(\"Top 20 Feature Importance (Random Forest)\")\n", "plt.xlabel(\"Importance Score\")\n", "plt.ylabel(\"Feature\")\n", "plt.show()\n", "\n", "# Display top features and their importance\n", "list(zip(top_feature_names, top_features))\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}