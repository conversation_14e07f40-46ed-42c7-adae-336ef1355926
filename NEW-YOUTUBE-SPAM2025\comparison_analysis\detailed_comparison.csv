Type,Split,Model,Accuracy,<PERSON>_<PERSON>ze,<PERSON>_Size,<PERSON><PERSON>_Precision,<PERSON><PERSON>_<PERSON>call,Spam_F1_Score
Original,Train 75 Test 25,<PERSON><PERSON>,0.8839662447257384,1422,474,0.859375,0.9205020920502092,0.8888888888888888
Original,Train 75 Test 25,Logis<PERSON> Regression,0.9240506329113924,1422,474,0.9592760180995475,0.8870292887029289,0.9217391304347826
Original,Train 75 Test 25,<PERSON>,0.919831223628692,1422,474,0.9674418604651163,0.8702928870292888,0.9162995594713657
Original,Train 75 Test 25,Svm,0.930379746835443,1422,474,0.9598214285714286,0.899581589958159,0.9287257019438445
Original,Train 70 Test 30,<PERSON><PERSON>,0.8822495606326889,1327,569,0.8618421052631579,0.9128919860627178,0.8866328257191202
Original,Train 70 Test 30,<PERSON><PERSON><PERSON> Reg<PERSON>,0.9226713532513181,1327,569,0.9584905660377359,0.8850174216027874,0.9202898550724637
Original,Train 70 Test 30,Random Forest,0.929701230228471,1327,569,0.9695817490494296,0.8885017421602788,0.9272727272727272
Original,Train 70 Test 30,Svm,0.929701230228471,1327,569,0.955719557195572,0.9024390243902439,0.9283154121863799
Original,Train 65 Test 35,Naive Bayes,0.8810240963855421,1232,664,0.8632478632478633,0.907185628742515,0.8846715328467153
Original,Train 65 Test 35,Logistic Regression,0.9141566265060241,1232,664,0.9540983606557377,0.8712574850299402,0.9107981220657277
Original,Train 65 Test 35,Random Forest,0.9066265060240963,1232,664,0.9533333333333334,0.8562874251497006,0.9022082018927445
Original,Train 65 Test 35,Svm,0.9231927710843374,1232,664,0.9463722397476341,0.8982035928143712,0.9216589861751152
Reversed,Train 25 Test 75,Naive Bayes,0.8684950773558369,474,1422,0.8327044025157233,0.9245810055865922,0.8762409000661814
Reversed,Train 25 Test 75,Logistic Regression,0.9050632911392406,474,1422,0.9355322338830585,0.8715083798882681,0.9023861171366594
Reversed,Train 25 Test 75,Random Forest,0.8895921237693389,474,1422,0.9912126537785588,0.7877094972067039,0.8778210116731517
Reversed,Train 25 Test 75,Svm,0.9092827004219409,474,1422,0.9335302806499262,0.88268156424581,0.9073941134242641
Reversed,Train 30 Test 70,Naive Bayes,0.875,568,1328,0.8449931412894376,0.9207772795216741,0.8812589413447782
Reversed,Train 30 Test 70,Logistic Regression,0.9141566265060241,568,1328,0.9468599033816425,0.8789237668161435,0.9116279069767442
Reversed,Train 30 Test 70,Random Forest,0.8900602409638554,568,1328,0.9887850467289719,0.7907324364723468,0.8787375415282392
Reversed,Train 30 Test 70,Svm,0.9118975903614458,568,1328,0.9353312302839116,0.8863976083707026,0.9102072141212586
Reversed,Train 35 Test 65,Naive Bayes,0.8775344687753447,663,1233,0.8476331360946746,0.9227053140096618,0.8835774865073246
Reversed,Train 35 Test 65,Logistic Regression,0.910786699107867,663,1233,0.9443478260869566,0.8743961352657005,0.9080267558528428
Reversed,Train 35 Test 65,Random Forest,0.8978102189781022,663,1233,0.9881656804733728,0.8067632850241546,0.8882978723404256
Reversed,Train 35 Test 65,Svm,0.9172749391727494,663,1233,0.9405772495755518,0.8921095008051529,0.915702479338843
