{"cells": [{"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["from selenium import webdriver\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "import pandas as pd\n", "import time\n", "from datetime import datetime\n", "import pytz\n", "\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# Get today's date\n", "today_date = datetime.now().strftime(\"%Y-%m-%d\")  # Format: YYYY-MM-DD\n", "# Define the timezone\n", "timezone = pytz.timezone('Asia/Jakarta')\n", "\n", "# Get the current time in the specified timezone\n", "now = datetime.now(timezone)\n", "# Convert to string in the desired format\n", "now_string = now.strftime(\"%Y%m%d_%H%M%S\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# ID video YouTube dan URL\n", "youtubeid = \"4F2oOGDyWeY\"\n", "video_url = f\"https://www.youtube.com/watch?v={youtubeid}\""]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["\n", "profile_path = r'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Default'  # Ganti dengan path profil Chrome Anda\n", "\n", "def initialize_driver_with_profile(profile_path):\n", "    \"\"\"Inisialisasi driver dengan profil Chrome yang sudah ada.\"\"\"\n", "    chrome_options = Options()\n", "    chrome_options.add_argument(f\"user-data-dir={profile_path}\")\n", "    chrome_options.add_argument(\"--no-sandbox\")\n", "    chrome_options.add_argument(\"--disable-dev-shm-usage\")\n", "    chrome_options.add_argument(\"--no-first-run\")\n", "    chrome_options.add_argument(\"--remote-debugging-port=9222\")\n", "    chrome_options.add_argument(\"--enable-logging\")\n", "    chrome_options.add_argument(\"--v=1\")  # Level logging dasar\n", "    # service = Service(driver_path)\n", "    # driver = webdriver.Chrome(service=service, options=chrome_options)\n", "    driver = webdriver.Chrome(options=chrome_options)\n", "    return driver\n", "\n", "def detect_and_skip_ad(driver):\n", "    \"\"\"<PERSON>iksa iklan dan klik tombol 'Skip Ad' saat tersedia.\"\"\"\n", "    ad_present = False\n", "    try:\n", "        # Cek jika elemen iklan atau teks sponsor ada\n", "        WebDriverWait(driver, 15).until(\n", "            EC.presence_of_any_elements_located([\n", "                (By.CLASS_NAME, \"video-ads.ytp-ad-module\"),               # Div dengan kelas iklan\n", "                (By.<PERSON>LA<PERSON>_NAME, \"ytp-ad-player-overlay-layout_\")          # Overlay iklan\n", "            ])\n", "        )\n", "        ad_present = True\n", "        print(\"Iklan terdeteksi.\")\n", "\n", "        # <PERSON><PERSON> iklan te<PERSON>, tunggu tombol \"<PERSON><PERSON>\" dan klik jika ada\n", "        skip_button = WebDriverWait(driver, 30).until(\n", "            EC.element_to_be_clickable((By.CLASS_NAME, \"ytp-skip-ad-button\"))\n", "        )\n", "        skip_button.click()\n", "        print(\"Tombol 'Skip Ad' diklik.\")\n", "        \n", "    except Exception as e:\n", "        print(\"Tidak ada iklan atau tombol 'Skip Ad' tidak ditemukan.\")\n", "        print(f\"Error: {e}\")\n", "\n", "    return ad_present\n", "\n", "def get_all_youtube_comments(driver, max_comments=None):\n", "    \"\"\"<PERSON><PERSON>bil semua komentar dari video YouTube dengan scroll tanpa batas, termasuk penulis, waktu, suka, dan isi komentar.\"\"\"\n", "    comments_data = []\n", "    last_height = driver.execute_script(\"return document.documentElement.scrollHeight\")\n", "    \n", "    while True:\n", "        # Scroll ke bawah untuk memuat komentar baru\n", "        driver.execute_script(\"window.scrollTo(0, document.documentElement.scrollHeight);\")\n", "        time.sleep(2)  # <PERSON><PERSON> untuk memuat komentar baru\n", "\n", "        # Ambil elemen penuli<PERSON>, waktu, suka, dan komentar\n", "        comment_elements = driver.find_elements(By.XPATH, '//*[@id=\"content-text\"]')\n", "        author_elements = driver.find_elements(By.XPATH, '//*[@id=\"author-text\"]')\n", "        time_elements = driver.find_elements(By.XPATH, '//*[@id=\"published-time-text\"]')\n", "        like_elements = driver.find_elements(By.XPATH, '//*[@id=\"vote-count-middle\"]')\n", "        \n", "        # Loop melalui elemen dan tambahkan data ke dalam list\n", "        for i in range(len(comments_data), len(comment_elements)):\n", "            comment_text = comment_elements[i].text\n", "            author_name = author_elements[i].text\n", "            comment_time = time_elements[i].text\n", "            likes = like_elements[i].text if i < len(like_elements) else \"0\"  # <PERSON><PERSON> ni<PERSON> default \"0\" jika tidak ada likes\n", "            \n", "            if comment_text and author_name and comment_time:\n", "                comments_data.append({\n", "                    \"Author\": author_name,\n", "                    \"Timestamp\": comment_time,\n", "                    \"Likes\": likes,\n", "                    \"Comment\": comment_text\n", "                })\n", "\n", "                # <PERSON><PERSON> maksimal komentar jika disetel\n", "                if max_comments and len(comments_data) >= max_comments:\n", "                    return comments_data\n", "\n", "        # Cek tinggi baru halaman untuk melihat apakah ada yang baru dimuat\n", "        new_height = driver.execute_script(\"return document.documentElement.scrollHeight\")\n", "        if new_height == last_height:  # <PERSON><PERSON><PERSON><PERSON> jika tidak ada lagi yang dimuat\n", "            break\n", "        last_height = new_height\n", "\n", "    return comments_data\n", "\n", "def save_to_xlsx(comments_data, filename):\n", "    \"\"\"Menyimpan komentar ke file XLSX.\"\"\"\n", "    df = pd.DataFrame(comments_data)\n", "    df.to_excel(filename, index=False)\n", "    print(f\"Komentar berhasil disimpan di {filename}\")\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["def save_to_csv(comments_data, filename):\n", "    \"\"\"Menyimpan komentar ke file csv.\"\"\"\n", "    df = pd.DataFrame(comments_data)\n", "    df.to_csv(filename, index=False)\n", "    print(f\"Komentar berhasil disimpan di {filename}\")\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tidak ada iklan atau tombol 'Skip Ad' tidak ditemukan.\n", "Error: module 'selenium.webdriver.support.expected_conditions' has no attribute 'presence_of_any_elements_located'\n", "Komentar berhasil disimpan di youtube_comments_4F2oOGDyWeY_20241120_103918.xlsx\n"]}], "source": ["\n", "# Inisialisasi WebDriver dengan profil Chrome\n", "driver = initialize_driver_with_profile(profile_path)\n", "\n", "# Akses URL video YouTube\n", "driver.get(video_url)\n", "time.sleep(5)  # <PERSON><PERSON><PERSON> halaman dimuat\n", "\n", "# <PERSON><PERSON><PERSON> iklan dan lewati jika ada\n", "ad_present = detect_and_skip_ad(driver)\n", "\n", "# Jika tidak ada iklan atau iklan sudah di<PERSON>, mulai mengambil komentar\n", "if not ad_present or ad_present:\n", "    comments_data = get_all_youtube_comments(driver, max_comments=None)  # None untuk scroll tanpa batas\n", "    save_to_xlsx(comments_data, filename=f\"youtube_comments_{youtubeid}_{now_string}.xlsx\")\n", "\n", "# Tutup driver <PERSON><PERSON><PERSON>\n", "driver.quit()\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Author</th>\n", "      <th>Timestamp</th>\n", "      <th>Likes</th>\n", "      <th>Comment</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>@ulaks<PERSON>samsuri</td>\n", "      <td>4 hours ago</td>\n", "      <td>271</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> ,kami di pelosok kampung bis...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>@Suji-o3g</td>\n", "      <td>11 hours ago</td>\n", "      <td>2.5K</td>\n", "      <td><PERSON><PERSON>,tdk lang<PERSON>,tunggu mo...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>@Rhiiyooo619</td>\n", "      <td>3 hours ago (edited)</td>\n", "      <td>60</td>\n", "      <td>ALHAMDULILAH ️🇮🇩\\n\\n<PERSON><PERSON><PERSON><PERSON> \"<PERSON><PERSON>\" <PERSON>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>@suryabuana-s6d</td>\n", "      <td>11 hours ago</td>\n", "      <td>980</td>\n", "      <td>gitu donk RCTI after Match disiarkan sampai se...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>@djo<PERSON><PERSON></td>\n", "      <td>9 hours ago</td>\n", "      <td>133</td>\n", "      <td><PERSON><PERSON> ka<PERSON>h buat ketum <PERSON>, trima ksh...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1207</th>\n", "      <td>@suryachannel4862</td>\n", "      <td>3 hours ago</td>\n", "      <td></td>\n", "      <td>90+7 jadi 100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1208</th>\n", "      <td>@dctplaylist2706</td>\n", "      <td>2 hours ago</td>\n", "      <td></td>\n", "      <td>REPORTER KATROK.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1209</th>\n", "      <td>@balang7867</td>\n", "      <td>1 hour ago</td>\n", "      <td></td>\n", "      <td><PERSON><PERSON> <PERSON> pikir pikir saudi sekelas Filipina ya...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1210</th>\n", "      <td>@johnmonyeng2836</td>\n", "      <td>1 hour ago</td>\n", "      <td></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1211</th>\n", "      <td>@keadilanhukumindonesia</td>\n", "      <td>2 hours ago</td>\n", "      <td></td>\n", "      <td>Dipaksain yg main bukan warga asli indonesia. ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1212 rows × 4 columns</p>\n", "</div>"], "text/plain": ["                       Author             Timestamp Likes  \\\n", "0            @ulaksurisamsuri           4 hours ago   271   \n", "1                   @Suji-o3g          11 hours ago  2.5K   \n", "2                @Rhiiyooo619  3 hours ago (edited)    60   \n", "3             @suryabuana-s6d          11 hours ago   980   \n", "4                 @djorantaka           9 hours ago   133   \n", "...                       ...                   ...   ...   \n", "1207        @suryachannel4862           3 hours ago         \n", "1208         @dctplaylist2706           2 hours ago         \n", "1209              @balang7867            1 hour ago         \n", "1210         @johnmonyeng2836            1 hour ago         \n", "1211  @keadilanhukumindonesia           2 hours ago         \n", "\n", "                                                Comment  \n", "0     Terima kasih RCTI ,kami di pelosok kampung bis...  \n", "1     <PERSON><PERSON>,tdk lang<PERSON>,tunggu mo...  \n", "2     ALHAMDULILAH ️🇮🇩\\n\\n<PERSON><PERSON><PERSON><PERSON> \"<PERSON><PERSON>\" <PERSON>...  \n", "3     gitu donk RCTI after Match disiarkan sampai se...  \n", "4     <PERSON><PERSON> ka<PERSON>h buat ketum Eric<PERSON>, trima ksh...  \n", "...                                                 ...  \n", "1207                                      90+7 jadi 100  \n", "1208                                  REPORTER KATROK.   \n", "1209  Kalau di pikir pikir saudi se<PERSON>as Filipina ya...  \n", "1210                                             Curang  \n", "1211  Dipaksain yg main bukan warga asli indonesia. ...  \n", "\n", "[1212 rows x 4 columns]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.DataFrame(comments_data)\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Komentar berhasil disimpan di youtube_comments_4F2oOGDyWeY_20241120_103918.xlsx\n", "Ko<PERSON>ar berhasil disimpan di youtube_comments_4F2oOGDyWeY_20241120_103918.csv\n"]}], "source": ["save_to_xlsx(df, filename=f\"youtube_comments_{youtubeid}_{now_string}.xlsx\")\n", "save_to_csv(df, filename=f\"youtube_comments_{youtubeid}_{now_string}.csv\")\n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["df.to_excel(f\"youtube_comments_{youtubeid}_{now_string}.xlsx\", index=False)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}