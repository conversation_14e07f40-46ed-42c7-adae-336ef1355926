YOUTUBE SPAM CLASSIFICATION - REVERSED SPLIT TRAINING REPORT
============================================================
Training Date: 2025-06-30 00:18:22
Training Type: REVERSED SPLITS (Small Train / Large Test)
Dataset: dataset/youtube_spam.csv
Total Samples: 1896
Class Distribution: {1: 955, 0: 941}

SPLIT: TRAIN_25_TEST_75_REVERSED
----------------------------------------
NAIVE_BAYES:
  Accuracy: 0.8685
  Train Size: 474
  Test Size: 1422
  Train/Test Ratio: 0.33

LOGISTIC_REGRESSION:
  Accuracy: 0.9051
  Train Size: 474
  Test Size: 1422
  Train/Test Ratio: 0.33

RANDOM_FOREST:
  Accuracy: 0.8896
  Train Size: 474
  Test Size: 1422
  Train/Test Ratio: 0.33

SVM:
  Accuracy: 0.9093
  Train Size: 474
  Test Size: 1422
  Train/Test Ratio: 0.33


SPLIT: TRAIN_30_TEST_70_REVERSED
----------------------------------------
NAIVE_BAYES:
  Accuracy: 0.8750
  Train Size: 568
  Test Size: 1328
  Train/Test Ratio: 0.43

LOGISTIC_REGRESSION:
  Accuracy: 0.9142
  Train Size: 568
  Test Size: 1328
  Train/Test Ratio: 0.43

RANDOM_FOREST:
  Accuracy: 0.8901
  Train Size: 568
  Test Size: 1328
  Train/Test Ratio: 0.43

SVM:
  Accuracy: 0.9119
  Train Size: 568
  Test Size: 1328
  Train/Test Ratio: 0.43


SPLIT: TRAIN_35_TEST_65_REVERSED
----------------------------------------
NAIVE_BAYES:
  Accuracy: 0.8775
  Train Size: 663
  Test Size: 1233
  Train/Test Ratio: 0.54

LOGISTIC_REGRESSION:
  Accuracy: 0.9108
  Train Size: 663
  Test Size: 1233
  Train/Test Ratio: 0.54

RANDOM_FOREST:
  Accuracy: 0.8978
  Train Size: 663
  Test Size: 1233
  Train/Test Ratio: 0.54

SVM:
  Accuracy: 0.9173
  Train Size: 663
  Test Size: 1233
  Train/Test Ratio: 0.54

