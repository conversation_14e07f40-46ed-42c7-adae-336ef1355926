{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import re\n", "\n", "# Load dataset\n", "file_path = 'youtube_comments_i6IOiUi6IYY.xlsx'\n", "data = pd.read_excel(file_path)\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["\n", "# Daftar kata kunci SPAM\n", "spam_keywords = ['gratis', 'ikuti', 'menang', 'klik di sini', 'subscribe', 'channel', 'giveaway', 'follow', \n", "                 'click', 'link', 'free', 'money', 'prize', 'winner']\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["\n", "# Fungsi deteksi SPAM berdasarkan aturan yang disebutkan\n", "def detect_spam(comment):\n", "    # Ubah komentar menjadi lowercase untuk deteksi yang case-insensitive\n", "    comment = str(comment).lower()\n", "\n", "    # 1. <PERSON><PERSON><PERSON> kata kunci SPAM\n", "    if any(keyword in comment for keyword in spam_keywords):\n", "        return 'SPAM'\n", "    \n", "    # 2. <PERSON><PERSON><PERSON> (http atau https)\n", "    if re.search(r'http[s]?://', comment):\n", "        return 'SPAM'\n", "    \n", "    # 3. <PERSON><PERSON><PERSON> pen<PERSON>an karakter atau frasa (contoh: \"!!!\" atau \"!!!!!\")\n", "    if re.search(r'(.)\\1{2,}', comment):\n", "        return 'SPAM'\n", "    \n", "    # Jika tidak memenuhi kriteria SPAM, beri label NOT SPAM\n", "    return 'NOT SPAM'\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File dengan label SPAM telah disimpan sebagai youtube_comments_labeled_rule_based_advanced.xlsx\n"]}], "source": ["\n", "# <PERSON><PERSON>kan fungsi pada kolom \"Comment\"\n", "data['SPAM'] = data['Comment'].apply(detect_spam)\n", "\n", "# Simpan hasil ke file baru\n", "output_path = 'youtube_comments_labeled_rule_based_advanced.xlsx'\n", "data.to_excel(output_path, index=False)\n", "\n", "print(f\"File dengan label SPAM telah disimpan sebagai {output_path}\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}