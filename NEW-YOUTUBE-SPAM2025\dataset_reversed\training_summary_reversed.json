{"dataset_info": {"total_samples": 1896, "features": ["CONTENT"], "target": "CLASS", "class_distribution": {"1": 955, "0": 941}}, "training_timestamp": "2025-06-30T00:18:22.455102", "training_type": "REVERSED_SPLITS", "splits_trained": ["train_25_test_75_REVERSED", "train_30_test_70_REVERSED", "train_35_test_65_REVERSED"], "models_trained": ["naive_bayes", "logistic_regression", "random_forest", "svm"], "results": {"train_25_test_75_REVERSED": {"naive_bayes": {"accuracy": 0.8684950773558369, "classification_report": {"0": {"precision": 0.9138755980861244, "recall": 0.8116147308781869, "f1-score": 0.859714928732183, "support": 706.0}, "1": {"precision": 0.8327044025157233, "recall": 0.9245810055865922, "f1-score": 0.8762409000661814, "support": 716.0}, "accuracy": 0.8684950773558369, "macro avg": {"precision": 0.8732900003009239, "recall": 0.8680978682323895, "f1-score": 0.8679779143991821, "support": 1422.0}, "weighted avg": {"precision": 0.8730045882208591, "recall": 0.8684950773558369, "f1-score": 0.8680360225965591, "support": 1422.0}}, "confusion_matrix": [[573, 133], [54, 662]], "train_size": 474, "test_size": 1422, "train_test_ratio": 0.3333333333333333}, "logistic_regression": {"accuracy": 0.9050632911392406, "classification_report": {"0": {"precision": 0.8781456953642384, "recall": 0.9390934844192634, "f1-score": 0.9075975359342916, "support": 706.0}, "1": {"precision": 0.9355322338830585, "recall": 0.8715083798882681, "f1-score": 0.9023861171366594, "support": 716.0}, "accuracy": 0.9050632911392406, "macro avg": {"precision": 0.9068389646236484, "recall": 0.9053009321537657, "f1-score": 0.9049918265354755, "support": 1422.0}, "weighted avg": {"precision": 0.907040745701422, "recall": 0.9050632911392406, "f1-score": 0.9049735022780999, "support": 1422.0}}, "confusion_matrix": [[663, 43], [92, 624]], "train_size": 474, "test_size": 1422, "train_test_ratio": 0.3333333333333333}, "random_forest": {"accuracy": 0.8895921237693389, "classification_report": {"0": {"precision": 0.8218053927315357, "recall": 0.9929178470254958, "f1-score": 0.8992944194996793, "support": 706.0}, "1": {"precision": 0.9912126537785588, "recall": 0.7877094972067039, "f1-score": 0.8778210116731517, "support": 716.0}, "accuracy": 0.8895921237693389, "macro avg": {"precision": 0.9065090232550472, "recall": 0.8903136721160998, "f1-score": 0.8885577155864155, "support": 1422.0}, "weighted avg": {"precision": 0.9071046887298962, "recall": 0.8895921237693389, "f1-score": 0.8884822113394867, "support": 1422.0}}, "confusion_matrix": [[701, 5], [152, 564]], "train_size": 474, "test_size": 1422, "train_test_ratio": 0.3333333333333333}, "svm": {"accuracy": 0.9092827004219409, "classification_report": {"0": {"precision": 0.887248322147651, "recall": 0.9362606232294618, "f1-score": 0.9110957960027567, "support": 706.0}, "1": {"precision": 0.9335302806499262, "recall": 0.88268156424581, "f1-score": 0.9073941134242641, "support": 716.0}, "accuracy": 0.9092827004219409, "macro avg": {"precision": 0.9103893013987886, "recall": 0.9094710937376359, "f1-score": 0.9092449547135104, "support": 1422.0}, "weighted avg": {"precision": 0.9105520368365604, "recall": 0.9092827004219409, "f1-score": 0.9092319389519826, "support": 1422.0}}, "confusion_matrix": [[661, 45], [84, 632]], "train_size": 474, "test_size": 1422, "train_test_ratio": 0.3333333333333333}}, "train_30_test_70_REVERSED": {"naive_bayes": {"accuracy": 0.875, "classification_report": {"0": {"precision": 0.9115191986644408, "recall": 0.8285280728376327, "f1-score": 0.8680445151033387, "support": 659.0}, "1": {"precision": 0.8449931412894376, "recall": 0.9207772795216741, "f1-score": 0.8812589413447782, "support": 669.0}, "accuracy": 0.875, "macro avg": {"precision": 0.8782561699769391, "recall": 0.8746526761796534, "f1-score": 0.8746517282240585, "support": 1328.0}, "weighted avg": {"precision": 0.8780056953633284, "recall": 0.875, "f1-score": 0.8747014813349072, "support": 1328.0}}, "confusion_matrix": [[546, 113], [53, 616]], "train_size": 568, "test_size": 1328, "train_test_ratio": 0.42771084337349397}, "logistic_regression": {"accuracy": 0.9141566265060241, "classification_report": {"0": {"precision": 0.8854314002828855, "recall": 0.9499241274658573, "f1-score": 0.9165446559297218, "support": 659.0}, "1": {"precision": 0.9468599033816425, "recall": 0.8789237668161435, "f1-score": 0.9116279069767442, "support": 669.0}, "accuracy": 0.9141566265060241, "macro avg": {"precision": 0.9161456518322639, "recall": 0.9144239471410004, "f1-score": 0.914086281453233, "support": 1328.0}, "weighted avg": {"precision": 0.9163769338469431, "recall": 0.9141566265060241, "f1-score": 0.9140677695972355, "support": 1328.0}}, "confusion_matrix": [[626, 33], [81, 588]], "train_size": 568, "test_size": 1328, "train_test_ratio": 0.42771084337349397}, "random_forest": {"accuracy": 0.8900602409638554, "classification_report": {"0": {"precision": 0.8234552332912989, "recall": 0.9908952959028832, "f1-score": 0.8994490358126722, "support": 659.0}, "1": {"precision": 0.9887850467289719, "recall": 0.7907324364723468, "f1-score": 0.8787375415282392, "support": 669.0}, "accuracy": 0.8900602409638554, "macro avg": {"precision": 0.9061201400101354, "recall": 0.890813866187615, "f1-score": 0.8890932886704557, "support": 1328.0}, "weighted avg": {"precision": 0.9067426167173555, "recall": 0.8900602409638554, "f1-score": 0.8890153086467943, "support": 1328.0}}, "confusion_matrix": [[653, 6], [140, 529]], "train_size": 568, "test_size": 1328, "train_test_ratio": 0.42771084337349397}, "svm": {"accuracy": 0.9118975903614458, "classification_report": {"0": {"precision": 0.8904899135446686, "recall": 0.9377845220030349, "f1-score": 0.9135254988913526, "support": 659.0}, "1": {"precision": 0.9353312302839116, "recall": 0.8863976083707026, "f1-score": 0.9102072141212586, "support": 669.0}, "accuracy": 0.9118975903614458, "macro avg": {"precision": 0.91291057191429, "recall": 0.9120910651868688, "f1-score": 0.9118663565063057, "support": 1328.0}, "weighted avg": {"precision": 0.9130794021730976, "recall": 0.9118975903614458, "f1-score": 0.9118538629642494, "support": 1328.0}}, "confusion_matrix": [[618, 41], [76, 593]], "train_size": 568, "test_size": 1328, "train_test_ratio": 0.42771084337349397}}, "train_35_test_65_REVERSED": {"naive_bayes": {"accuracy": 0.8775344687753447, "classification_report": {"0": {"precision": 0.9138240574506283, "recall": 0.8316993464052288, "f1-score": 0.8708297690333618, "support": 612.0}, "1": {"precision": 0.8476331360946746, "recall": 0.9227053140096618, "f1-score": 0.8835774865073246, "support": 621.0}, "accuracy": 0.8775344687753447, "macro avg": {"precision": 0.8807285967726515, "recall": 0.8772023302074453, "f1-score": 0.8772036277703432, "support": 1233.0}, "weighted avg": {"precision": 0.8804870240669728, "recall": 0.8775344687753447, "f1-score": 0.8772501522866715, "support": 1233.0}}, "confusion_matrix": [[509, 103], [48, 573]], "train_size": 663, "test_size": 1233, "train_test_ratio": 0.537712895377129}, "logistic_regression": {"accuracy": 0.910786699107867, "classification_report": {"0": {"precision": 0.8814589665653495, "recall": 0.9477124183006536, "f1-score": 0.9133858267716536, "support": 612.0}, "1": {"precision": 0.9443478260869566, "recall": 0.8743961352657005, "f1-score": 0.9080267558528428, "support": 621.0}, "accuracy": 0.910786699107867, "macro avg": {"precision": 0.912903396326153, "recall": 0.9110542767831771, "f1-score": 0.9107062913122481, "support": 1233.0}, "weighted avg": {"precision": 0.9131329177112684, "recall": 0.910786699107867, "f1-score": 0.9106867326592599, "support": 1233.0}}, "confusion_matrix": [[580, 32], [78, 543]], "train_size": 663, "test_size": 1233, "train_test_ratio": 0.537712895377129}, "random_forest": {"accuracy": 0.8978102189781022, "classification_report": {"0": {"precision": 0.8347107438016529, "recall": 0.9901960784313726, "f1-score": 0.905829596412556, "support": 612.0}, "1": {"precision": 0.9881656804733728, "recall": 0.8067632850241546, "f1-score": 0.8882978723404256, "support": 621.0}, "accuracy": 0.8978102189781022, "macro avg": {"precision": 0.9114382121375129, "recall": 0.8984796817277636, "f1-score": 0.8970637343764908, "support": 1233.0}, "weighted avg": {"precision": 0.9119982666509133, "recall": 0.8978102189781022, "f1-score": 0.8969997499820668, "support": 1233.0}}, "confusion_matrix": [[606, 6], [120, 501]], "train_size": 663, "test_size": 1233, "train_test_ratio": 0.537712895377129}, "svm": {"accuracy": 0.9172749391727494, "classification_report": {"0": {"precision": 0.8959627329192547, "recall": 0.9428104575163399, "f1-score": 0.9187898089171974, "support": 612.0}, "1": {"precision": 0.9405772495755518, "recall": 0.8921095008051529, "f1-score": 0.915702479338843, "support": 621.0}, "accuracy": 0.9172749391727494, "macro avg": {"precision": 0.9182699912474033, "recall": 0.9174599791607464, "f1-score": 0.9172461441280202, "support": 1233.0}, "weighted avg": {"precision": 0.9184328179505283, "recall": 0.9172749391727494, "f1-score": 0.9172348765018217, "support": 1233.0}}, "confusion_matrix": [[577, 35], [67, 554]], "train_size": 663, "test_size": 1233, "train_test_ratio": 0.537712895377129}}}}