{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"id": "TD1SUfapU9L6"}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import matplotlib.ticker as ticker\n", "import imblearn\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.linear_model import LogisticRegressionCV\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.naive_bayes import GaussianNB\n", "from sklearn.neighbors import KNeighborsClassifier\n", "from sklearn.neural_network import MLPClassifier\n", "from sklearn.svm import SVC\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.metrics import accuracy_score, precision_score, confusion_matrix, roc_auc_score, roc_curve\n", "from sklearn.model_selection import GridSearchCV\n", "from sklearn.ensemble import VotingClassifier\n", "from sklearn.metrics import f1_score, recall_score"]}, {"cell_type": "markdown", "metadata": {"id": "yX8ZFgIYqt5t"}, "source": ["# <PERSON><PERSON>ü<PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Ltv5OxROlfEc", "outputId": "7818cf62-14ed-4954-dd31-521bd4376173"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mounted at /content/drive\n"]}], "source": ["from google.colab import drive\n", "drive.mount('/content/drive')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "QTQokMuom61E"}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 602}, "id": "2yt0kRYCU9L_", "outputId": "68fd3c5d-4e4d-46fb-e4a8-147d10986443"}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: '/content/drive/MyDrive/Colab/train_data.csv'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-6-6c23ec676d8a>\u001b[0m in \u001b[0;36m<cell line: 1>\u001b[0;34m()\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mcrowd_data\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mpd\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mread_csv\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'/content/drive/MyDrive/Colab/train_data.csv'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      2\u001b[0m \u001b[0mgold_data\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mpd\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mread_csv\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'/content/drive/MyDrive/Colab/gold_train.csv'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0msep\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m'\\t'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mon_bad_lines\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m'warn'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[0mtest_data\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mpd\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mread_csv\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'/content/drive/MyDrive/Colab/test.csv'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0msep\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m'\\t'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mon_bad_lines\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m'warn'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/pandas/io/parsers/readers.py\u001b[0m in \u001b[0;36mread_csv\u001b[0;34m(filepath_or_buffer, sep, delimiter, header, names, index_col, usecols, dtype, engine, converters, true_values, false_values, skipinitialspace, skiprows, skipfooter, nrows, na_values, keep_default_na, na_filter, verbose, skip_blank_lines, parse_dates, infer_datetime_format, keep_date_col, date_parser, date_format, dayfirst, cache_dates, iterator, chunksize, compression, thousands, decimal, lineterminator, quotechar, quoting, doublequote, escapechar, comment, encoding, encoding_errors, dialect, on_bad_lines, delim_whitespace, low_memory, memory_map, float_precision, storage_options, dtype_backend)\u001b[0m\n\u001b[1;32m   1024\u001b[0m     \u001b[0mkwds\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mupdate\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkwds_defaults\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1025\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1026\u001b[0;31m     \u001b[0;32mreturn\u001b[0m \u001b[0m_read\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfilepath_or_buffer\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mkwds\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1027\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1028\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/pandas/io/parsers/readers.py\u001b[0m in \u001b[0;36m_read\u001b[0;34m(filepath_or_buffer, kwds)\u001b[0m\n\u001b[1;32m    618\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    619\u001b[0m     \u001b[0;31m# Create the parser.\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 620\u001b[0;31m     \u001b[0mparser\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mTextFileReader\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfilepath_or_buffer\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwds\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    621\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    622\u001b[0m     \u001b[0;32mif\u001b[0m \u001b[0mchunksize\u001b[0m \u001b[0;32mor\u001b[0m \u001b[0miterator\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/pandas/io/parsers/readers.py\u001b[0m in \u001b[0;36m__init__\u001b[0;34m(self, f, engine, **kwds)\u001b[0m\n\u001b[1;32m   1618\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1619\u001b[0m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mhandles\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mIOHandles\u001b[0m \u001b[0;34m|\u001b[0m \u001b[0;32mNone\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1620\u001b[0;31m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_engine\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_make_engine\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mf\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mengine\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1621\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1622\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mclose\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;34m->\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/pandas/io/parsers/readers.py\u001b[0m in \u001b[0;36m_make_engine\u001b[0;34m(self, f, engine)\u001b[0m\n\u001b[1;32m   1878\u001b[0m                 \u001b[0;32mif\u001b[0m \u001b[0;34m\"b\"\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mmode\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1879\u001b[0m                     \u001b[0mmode\u001b[0m \u001b[0;34m+=\u001b[0m \u001b[0;34m\"b\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1880\u001b[0;31m             self.handles = get_handle(\n\u001b[0m\u001b[1;32m   1881\u001b[0m                 \u001b[0mf\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1882\u001b[0m                 \u001b[0mmode\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/pandas/io/common.py\u001b[0m in \u001b[0;36mget_handle\u001b[0;34m(path_or_buf, mode, encoding, compression, memory_map, is_text, errors, storage_options)\u001b[0m\n\u001b[1;32m    871\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mioargs\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mencoding\u001b[0m \u001b[0;32mand\u001b[0m \u001b[0;34m\"b\"\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mioargs\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mmode\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    872\u001b[0m             \u001b[0;31m# Encoding\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 873\u001b[0;31m             handle = open(\n\u001b[0m\u001b[1;32m    874\u001b[0m                 \u001b[0mhandle\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    875\u001b[0m                 \u001b[0mioargs\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mmode\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: '/content/drive/MyDrive/Colab/train_data.csv'"]}], "source": ["crowd_data = pd.read_csv('/content/drive/MyDrive/Colab/train_data.csv')\n", "gold_data = pd.read_csv('/content/drive/MyDrive/Colab/gold_train.csv', sep='\\t', on_bad_lines='warn')\n", "test_data = pd.read_csv('/content/drive/MyDrive/Colab/test.csv', sep='\\t', on_bad_lines='warn')"]}, {"cell_type": "markdown", "metadata": {"id": "YA-zbHXM11Yq"}, "source": ["# <PERSON><PERSON>ü<PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 211}, "id": "J1pakRXdU9MA", "outputId": "9626c093-2f8b-4571-f03d-7e068a0c332d"}, "outputs": [{"ename": "NameError", "evalue": "name 'crowd_data' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-1-96d03ff61c17>\u001b[0m in \u001b[0;36m<cell line: 3>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[0;31m# Select the first 70% of Ham and 70% of Spam for training\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      2\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 3\u001b[0;31m \u001b[0mcrowd_data_shuffled\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mcrowd_data\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msample\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfrac\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mrandom_state\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      4\u001b[0m \u001b[0mgold_data_shuffled\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mgold_data\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msample\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfrac\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mrandom_state\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[0mtest_data_shuffled\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mtest_data\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msample\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfrac\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mrandom_state\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mNameError\u001b[0m: name 'crowd_data' is not defined"]}], "source": ["# Select the first 70% of Ham and 70% of Spam for training\n", "\n", "crowd_data_shuffled = crowd_data.sample(frac=1,random_state=0)\n", "gold_data_shuffled = gold_data.sample(frac=1,random_state=0)\n", "test_data_shuffled = test_data.sample(frac=1,random_state=0)\n", "\n", "\n", "# Combine to make X_train, y_train, X_test, y_test\n", "\n", "X_train=crowd_data_shuffled['text']\n", "y_train=crowd_data_shuffled['sentiment']\n", "\n", "X_gold_train = gold_data_shuffled['text']\n", "Y_gold_train = gold_data_shuffled['sentiment']\n", "\n", "X_test=test_data_shuffled['text']\n", "y_test=test_data_shuffled['sentiment']\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 207}, "id": "2DFiB_EyU9MB", "outputId": "ac08c0ab-bd04-49d4-f54f-e9fd1790915b"}, "outputs": [{"ename": "NameError", "evalue": "name 'crowd_data' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-1-aba14b0cac4d>\u001b[0m in \u001b[0;36m<cell line: 3>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[0;31m# Represent each comment using TF-IDF features\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      2\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 3\u001b[0;31m \u001b[0mcorpus\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mcrowd_data\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0miloc\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;36m3\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      4\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[0mvectorizer\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mTfidfVectorizer\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mstop_words\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mNone\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mfit\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mcorpus\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mNameError\u001b[0m: name 'crowd_data' is not defined"]}], "source": ["# Represent each comment using TF-IDF features\n", "\n", "corpus = crowd_data.iloc[:,3]\n", "\n", "vectorizer = TfidfVectorizer(stop_words=None).fit(corpus)\n", "\n", "v_train=vectorizer.transform(X_train)\n", "print(v_train.shape)\n", "print(v_train.toarray())\n", "\n", "v_test=vectorizer.transform(X_test)\n", "print(v_test.shape)\n", "print(v_test.toarray())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "OdcuH1tCU9MC"}, "outputs": [], "source": ["# Using Logistic Regression, determine the best lambda value\n", "\n", "# Cs is inverse of lambda\n", "# log_10(lambda) value from [-5,-4,...,5]\n", "\n", "clf = LogisticRegressionCV(Cs=list(np.power(10.0, np.arange(5, -6, -1))), cv=5, penalty='l2', scoring='roc_auc',\n", "                           solver='newton-cg', max_iter=30000, random_state=0).fit(v_train, y_train)\n", "\n", "print('lambda=1/Cs, so the auc_roc score for log_10(lambda) value from [-5,-4,...,5]=\\n',clf.scores_[1].mean(axis=0))\n", "print('The max auc_roc:', clf.scores_[1].mean(axis=0).max())\n", "\n", "i=np.where(clf.scores_[1].mean(axis=0)==clf.scores_[1].mean(axis=0).max())\n", "i=int(i[0])\n", "Cs=list(np.power(10.0, np.arange(5, -6, -1)))\n", "Cs_select=Cs[i]\n", "print('The max auc_roc when lambda=',1/Cs[i])\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "NzxIJPckU9MC"}, "outputs": [], "source": ["# Train Logistic Regression using the optimal lambda\n", "\n", "accuracy=[]\n", "precisions=[]\n", "recall=[]\n", "F1=[]\n", "train_error=[]\n", "\n", "clf = LogisticRegression(random_state=0, C=Cs_select, penalty='l2', max_iter=30000).fit(v_train, y_train)\n", "y_train_pred=clf.predict(v_train)\n", "\n", "# Confusion Matrix\n", "cm0 = confusion_matrix(y_train,y_train_pred)\n", "print('Training data Confusion Matrix=', cm0)\n", "\n", "# Accuracy\n", "accuracy.append((cm0[1,1]+cm0[0,0])/(cm0[1,1]+cm0[0,0]+cm0[0,1]+cm0[1,0]+0.000001))\n", "print('Accuracy for training=', accuracy[0])\n", "\n", "# Precision\n", "precisions.append(cm0[1,1]/(cm0[1,1]+cm0[0,1]+0.000001))\n", "print('Precision for training=', precisions[0])\n", "\n", "# Recall\n", "recall.append(cm0[1,1]/(cm0[1,1]+cm0[1,0]+0.000001))\n", "print('Recall for training=', recall[0])\n", "\n", "#F1 score\n", "F1.append(2*cm0[1,1]/(2*cm0[1,1]+cm0[0,1]+cm0[1,0]+0.000001))\n", "print('F1 score for training=', F1[0])\n", "\n", " # ROC\n", "fpr0, tpr0, thresholds0 = roc_curve(y_train, y_train_pred)\n", "plt.plot(fpr0, tpr0,'b-',label='Roc for Training')\n", "plt.legend()\n", "\n", "plt.title('ROC curve')\n", "plt.xlabel('False Positive Rate')\n", "plt.ylabel('True Positive Rate')\n", "\n", "\n", "# AUC\n", "\n", "print('AUC in Training:',\n", "      roc_auc_score(y_train, y_train_pred))"]}, {"cell_type": "markdown", "metadata": {"id": "WNdfudBinj7T"}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "87jkBytJU9MD"}, "outputs": [], "source": ["# Train Logistic Regression using the optimal lambda\n", "\n", "accuracy=[]\n", "precisions=[]\n", "recall=[]\n", "F1=[]\n", "train_error=[]\n", "\n", "clf = LogisticRegression(random_state=0, C=Cs_select, penalty='l2', max_iter=30000).fit(v_train, y_train)\n", "y_train_pred=clf.predict(v_train)\n", "\n", "# Confusion Matrix\n", "cm0 = confusion_matrix(y_train,y_train_pred)\n", "print('Training data Confusion Matrix=', cm0)\n", "\n", "# Accuracy\n", "accuracy.append((cm0[1,1]+cm0[0,0])/(cm0[1,1]+cm0[0,0]+cm0[0,1]+cm0[1,0]+0.000001))\n", "print('Accuracy for training=', accuracy[0])\n", "\n", "# Precision\n", "precisions.append(cm0[1,1]/(cm0[1,1]+cm0[0,1]+0.000001))\n", "print('Precision for training=', precisions[0])\n", "\n", "# Recall\n", "recall.append(cm0[1,1]/(cm0[1,1]+cm0[1,0]+0.000001))\n", "print('Recall for training=', recall[0])\n", "\n", "#F1 score\n", "F1.append(2*cm0[1,1]/(2*cm0[1,1]+cm0[0,1]+cm0[1,0]+0.000001))\n", "print('F1 score for training=', F1[0])\n", "\n", " # ROC\n", "fpr0, tpr0, thresholds0 = roc_curve(y_train, y_train_pred)\n", "plt.plot(fpr0, tpr0,'b-',label='Roc for Training')\n", "plt.legend()\n", "\n", "plt.title('ROC curve')\n", "plt.xlabel('False Positive Rate')\n", "plt.ylabel('True Positive Rate')\n", "\n", "\n", "# AUC\n", "\n", "print('AUC in Training:',\n", "      roc_auc_score(y_train, y_train_pred))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "daoWhuPMcYiw"}, "outputs": [], "source": ["  plt.figure(figsize=(8, 6))\n", "\n", "  # ROC for Training\n", "  plt.plot(fpr0, tpr0, '#2178b5', 'b-', label='Roc for Training')\n", "\n", "  # ROC for Test\n", "  plt.plot(fpr, tpr, '#ff8f2c','r-', label='ROC for Test')\n", "\n", "  plt.legend()\n", "  plt.title('ROC curve')\n", "  plt.xlabel('False Positive Rate')\n", "  plt.ylabel('True Positive Rate')\n", "  plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 211}, "id": "OEQWmBBcU9ME", "outputId": "ccc58eaa-7850-44f8-e178-45775a1f19f8"}, "outputs": [{"ename": "NameError", "evalue": "name 'v_train' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-3-21cfc34b0403>\u001b[0m in \u001b[0;36m<cell line: 8>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      6\u001b[0m \u001b[0mrecall\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      7\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 8\u001b[0;31m \u001b[0mv_train_dense\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mv_train\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mtoarray\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      9\u001b[0m \u001b[0mv_test_dense\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mv_test\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mtoarray\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     10\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mNameError\u001b[0m: name 'v_train' is not defined"]}], "source": ["# Naive Bayes Approach\n", "\n", "precisions = []\n", "F1 = []\n", "train_error = []\n", "recall = []\n", "\n", "v_train_dense = v_train.toarray()\n", "v_test_dense = v_test.toarray()\n", "\n", "clf = GaussianNB().fit(v_train_dense, y_train)\n", "y_pred = clf.predict(v_test_dense)\n", "y_train_pred = clf.predict(v_train_dense)\n", "\n", "# confusion matrix, ROC, precision, recall, F1 score, and AUC\n", "\n", "# Confusion Matrix\n", "cm0 = confusion_matrix(y_train, y_train_pred)\n", "print('Training Confusion Matrix=', cm0)\n", "\n", "cm = confusion_matrix(y_test, y_pred)\n", "print('Test Confusion Matrix=', cm)\n", "\n", "# ROC\n", "fpr0, tpr0, thresholds0 = roc_curve(y_train, y_train_pred)\n", "fpr, tpr, thresholds = roc_curve(y_test, y_pred)\n", "plt.plot(fpr0, tpr0, '#2178b5', label='ROC for Training')\n", "plt.plot(fpr, tpr, '#ff8f2c', label='ROC for Test')\n", "plt.legend()\n", "\n", "plt.title('ROC curve')\n", "plt.xlabel('False Positive Rate')\n", "plt.ylabel('True Positive Rate')\n", "\n", "# Precision\n", "precisions.append(cm0[1, 1] / (cm0[1, 1] + cm0[0, 1] + 0.000001))\n", "precisions.append(cm[1, 1] / (cm[1, 1] + cm[0, 1] + 0.000001))\n", "\n", "print('Precision for train=', precisions[0], ', Precision for test=', precisions[1])\n", "\n", "# Recall\n", "recall.append(cm0[1, 1] / (cm0[1, 1] + cm0[1, 0] + 0.000001))\n", "recall.append(cm[1, 1] / (cm[1, 1] + cm[1, 0] + 0.000001))\n", "print('Recall for train=', recall[0], ', Recall for test=', recall[1])\n", "\n", "# F1 score\n", "F1.append(2 * cm0[1, 1] / (2 * cm0[1, 1] + cm0[0, 1] + cm0[1, 0] + 0.000001))\n", "F1.append(2 * cm[1, 1] / (2 * cm[1, 1] + cm[0, 1] + cm[1, 0] + 0.000001))\n", "\n", "print('F1 score for train=', F1[0], ', F1 score for test=', F1[1])\n", "\n", "# AUC\n", "print('AUC in Training:', roc_auc_score(y_train, y_train_pred))\n", "print('AUC in Test:', roc_auc_score(y_test, y_pred))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ds4d4UP6U9ME"}, "outputs": [], "source": ["# Compare the two ML approaches\n", "\n", "data = pd.read_csv('Youtube01-Psy.csv')\n", "#data = pd.read_csv('Youtube02-KatyPerry.csv')\n", "#data = pd.read_csv('Youtube03-LMFAO.csv')\n", "#data = pd.read_csv('Youtube04-Eminem.csv')\n", "#data = pd.read_csv('Youtube05-Shakira.csv')\n", "\n", "#data.info()\n", "\n", "X=data.iloc[:,3].to_numpy()\n", "y = data.iloc[:,4].to_numpy()\n", "\n", "index_1=np.where(y==1)\n", "index_0=np.where(y==0)\n", "\n", "L1=np.rint(len(index_1[0])*0.5)\n", "L0=np.rint(len(index_0[0])*0.5)\n", "\n", "L1=L1.astype(int)\n", "L0=L0.astype(int)\n", "\n", "temp=[]\n", "for item in index_1:\n", "    temp=np.append(temp,item)\n", "\n", "temp=temp.astype(int)\n", "\n", "I=temp[0:L1-1]\n", "X_train_1=X[I]\n", "y_train_1=y[I]\n", "\n", "I=temp[L1:len(temp)]\n", "X_test_1=X[I]\n", "y_test_1=y[I]\n", "\n", "temp=[]\n", "for item in index_0:\n", "    temp=np.append(temp,item)\n", "\n", "temp=temp.astype(int)\n", "\n", "I=temp[0:L0-1]\n", "X_train_0=X[I]\n", "y_train_0=y[I]\n", "\n", "I=temp[L0:len(temp)]\n", "X_test_0=X[I]\n", "y_test_0=y[I]\n", "\n", "\n", "# Combine to make X_train, y_train, X_test, y_test\n", "\n", "X_train=np.concatenate((X_train_1, X_train_0), axis=0)\n", "y_train=np.concatenate((y_train_1, y_train_0), axis=0)\n", "\n", "X_test=np.concatenate((X_test_1, X_test_0), axis=0)\n", "y_test=np.concatenate((y_test_1, y_test_0), axis=0)\n", "\n", "\n", "corpus = data.iloc[:,3]\n", "\n", "vectorizer = TfidfVectorizer(stop_words=None, max_features=800).fit(corpus)\n", "#vectorizer = TfidfVectorizer(stop_words=None).fit(corpus)\n", "\n", "v_train=vectorizer.transform(X_train)\n", "\n", "v_test=vectorizer.transform(X_test)\n", "\n", "\n", "clf = LogisticRegressionCV(Cs=list(np.power(10.0, np.arange(5, -6, -1))), cv=5, penalty='l2', scoring='roc_auc',\n", "                           solver='newton-cg', max_iter=30000, random_state=0).fit(v_train, y_train)\n", "\n", "i=np.where(clf.scores_[1].mean(axis=0)==clf.scores_[1].mean(axis=0).max())\n", "i=int(i[0])\n", "Cs=list(np.power(10.0, np.arange(5, -6, -1)))\n", "Cs_select=Cs[i]\n", "\n", "accuracy=[]\n", "precisions=[]\n", "recall=[]\n", "F1=[]\n", "AUC=[]\n", "\n", "clf = LogisticRegression(random_state=0, C=Cs_select, penalty='l2', max_iter=30000).fit(v_train, y_train)\n", "y_pred=clf.predict(v_test)\n", "\n", "\n", "# Confusion Matrix\n", "cm = confusion_matrix(y_test,y_pred)\n", "\n", "# Accuracy\n", "accuracy.append((cm[1,1]+cm[0,0])/(cm[1,1]+cm[0,0]+cm[0,1]+cm[1,0]+0.000001))\n", "\n", "# Precision\n", "precisions.append(cm[1,1]/(cm[1,1]+cm[0,1]+0.000001))\n", "\n", "# Recall\n", "recall.append(cm[1,1]/(cm[1,1]+cm[1,0]+0.000001))\n", "\n", "\n", "#F1 score\n", "F1.append(2*cm[1,1]/(2*cm[1,1]+cm[0,1]+cm[1,0]+0.000001))\n", "\n", "\n", "# AUC\n", "AUC.append(roc_auc_score(y_test, y_pred))\n", "\n", "\n", "\n", "v_train_dense=v_train.toarray()\n", "v_test_dense=v_test.toarray()\n", "\n", "clf = GaussianNB().fit(v_train_dense, y_train)\n", "y_pred=clf.predict(v_test_dense)\n", "\n", "# Confusion Matrix\n", "cm = confusion_matrix(y_test,y_pred)\n", "\n", "# Accuracy\n", "accuracy.append((cm[1,1]+cm[0,0])/(cm[1,1]+cm[0,0]+cm[0,1]+cm[1,0]+0.000001))\n", "\n", "# Precision\n", "precisions.append(cm[1,1]/(cm[1,1]+cm[0,1]+0.000001))\n", "\n", "# Recall\n", "recall.append(cm[1,1]/(cm[1,1]+cm[1,0]+0.000001))\n", "\n", "\n", "#F1 score\n", "F1.append(2*cm[1,1]/(2*cm[1,1]+cm[0,1]+cm[1,0]+0.000001))\n", "\n", "\n", "# AUC\n", "AUC.append(roc_auc_score(y_test, y_pred))\n", "\n", "\n", "headings = ['Accuracy', 'Precision', 'Recall', 'F1', 'AUC']\n", "names = ['LogisticRegression','NB-Gaussian']\n", "numpy_data=np.array([accuracy,precisions,recall,F1,AUC]).T\n", "\n", "df = pd.DataFrame(data=numpy_data, index=names, columns=headings)\n", "\n", "print('The Performance for Eminem is summarized in the following Table:')\n", "\n", "#display(df)\n", "display(df.style.highlight_max(color = 'lightgreen', axis = 0))\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 130}, "id": "fPizLNpzU9MF", "outputId": "c3f56e90-44d8-40f2-af91-c4607f090a13"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The Performance for Psy is summarized in the following Table:\n"]}, {"data": {"text/html": ["<style type=\"text/css\">\n", "#T_2549d_row0_col0, #T_2549d_row0_col1, #T_2549d_row0_col3, #T_2549d_row0_col4, #T_2549d_row1_col2 {\n", "  background-color: lightgreen;\n", "}\n", "</style>\n", "<table id=\"T_2549d\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_2549d_level0_col0\" class=\"col_heading level0 col0\" >Accuracy</th>\n", "      <th id=\"T_2549d_level0_col1\" class=\"col_heading level0 col1\" >Precision</th>\n", "      <th id=\"T_2549d_level0_col2\" class=\"col_heading level0 col2\" >Recall</th>\n", "      <th id=\"T_2549d_level0_col3\" class=\"col_heading level0 col3\" >F1</th>\n", "      <th id=\"T_2549d_level0_col4\" class=\"col_heading level0 col4\" >AUC</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_2549d_level0_row0\" class=\"row_heading level0 row0\" >LogisticRegression</th>\n", "      <td id=\"T_2549d_row0_col0\" class=\"data row0 col0\" >0.936782</td>\n", "      <td id=\"T_2549d_row0_col1\" class=\"data row0 col1\" >0.963415</td>\n", "      <td id=\"T_2549d_row0_col2\" class=\"data row0 col2\" >0.908046</td>\n", "      <td id=\"T_2549d_row0_col3\" class=\"data row0 col3\" >0.934911</td>\n", "      <td id=\"T_2549d_row0_col4\" class=\"data row0 col4\" >0.936782</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_2549d_level0_row1\" class=\"row_heading level0 row1\" >NB-Gaussian</th>\n", "      <td id=\"T_2549d_row1_col0\" class=\"data row1 col0\" >0.839080</td>\n", "      <td id=\"T_2549d_row1_col1\" class=\"data row1 col1\" >0.786408</td>\n", "      <td id=\"T_2549d_row1_col2\" class=\"data row1 col2\" >0.931034</td>\n", "      <td id=\"T_2549d_row1_col3\" class=\"data row1 col3\" >0.852632</td>\n", "      <td id=\"T_2549d_row1_col4\" class=\"data row1 col4\" >0.839080</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x7928eb085e40>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Compare the two ML approaches in Psy data set\n", "\n", "data = pd.read_csv('Youtube01-Psy.csv')\n", "#data = pd.read_csv('Youtube02-KatyPerry.csv')\n", "#data = pd.read_csv('Youtube03-LMFAO.csv')\n", "#data = pd.read_csv('Youtube04-Eminem.csv')\n", "#data = pd.read_csv('Youtube05-Shakira.csv')\n", "\n", "#data.info()\n", "\n", "X=data.iloc[:,3].to_numpy()\n", "y = data.iloc[:,4].to_numpy()\n", "\n", "index_1=np.where(y==1)\n", "index_0=np.where(y==0)\n", "\n", "L1=np.rint(len(index_1[0])*0.5)\n", "L0=np.rint(len(index_0[0])*0.5)\n", "\n", "L1=L1.astype(int)\n", "L0=L0.astype(int)\n", "\n", "temp=[]\n", "for item in index_1:\n", "    temp=np.append(temp,item)\n", "\n", "temp=temp.astype(int)\n", "\n", "I=temp[0:L1-1]\n", "X_train_1=X[I]\n", "y_train_1=y[I]\n", "\n", "I=temp[L1:len(temp)]\n", "X_test_1=X[I]\n", "y_test_1=y[I]\n", "\n", "temp=[]\n", "for item in index_0:\n", "    temp=np.append(temp,item)\n", "\n", "temp=temp.astype(int)\n", "\n", "I=temp[0:L0-1]\n", "X_train_0=X[I]\n", "y_train_0=y[I]\n", "\n", "I=temp[L0:len(temp)]\n", "X_test_0=X[I]\n", "y_test_0=y[I]\n", "\n", "\n", "# Combine to make X_train, y_train, X_test, y_test\n", "\n", "X_train=np.concatenate((X_train_1, X_train_0), axis=0)\n", "y_train=np.concatenate((y_train_1, y_train_0), axis=0)\n", "\n", "X_test=np.concatenate((X_test_1, X_test_0), axis=0)\n", "y_test=np.concatenate((y_test_1, y_test_0), axis=0)\n", "\n", "\n", "corpus = data.iloc[:,3]\n", "\n", "vectorizer = TfidfVectorizer(stop_words=None, max_features=800).fit(corpus)\n", "#vectorizer = TfidfVectorizer(stop_words=None).fit(corpus)\n", "\n", "v_train=vectorizer.transform(X_train)\n", "\n", "v_test=vectorizer.transform(X_test)\n", "\n", "\n", "clf = LogisticRegressionCV(Cs=list(np.power(10.0, np.arange(5, -6, -1))), cv=5, penalty='l2', scoring='roc_auc',\n", "                           solver='newton-cg', max_iter=30000, random_state=0).fit(v_train, y_train)\n", "\n", "i=np.where(clf.scores_[1].mean(axis=0)==clf.scores_[1].mean(axis=0).max())\n", "if len(i[0])==0:\n", "    i=int(i[0])\n", "else:\n", "    i=int(i[0][0])\n", "Cs=list(np.power(10.0, np.arange(5, -6, -1)))\n", "Cs_select=Cs[i]\n", "\n", "accuracy=[]\n", "precisions=[]\n", "recall=[]\n", "F1=[]\n", "AUC=[]\n", "\n", "clf = LogisticRegression(random_state=0, C=Cs_select, penalty='l2', max_iter=30000).fit(v_train, y_train)\n", "y_pred=clf.predict(v_test)\n", "\n", "\n", "# Confusion Matrix\n", "cm = confusion_matrix(y_test,y_pred)\n", "\n", "# Accuracy\n", "accuracy.append((cm[1,1]+cm[0,0])/(cm[1,1]+cm[0,0]+cm[0,1]+cm[1,0]+0.000001))\n", "\n", "# Precision\n", "precisions.append(cm[1,1]/(cm[1,1]+cm[0,1]+0.000001))\n", "\n", "# Recall\n", "recall.append(cm[1,1]/(cm[1,1]+cm[1,0]+0.000001))\n", "\n", "\n", "#F1 score\n", "F1.append(2*cm[1,1]/(2*cm[1,1]+cm[0,1]+cm[1,0]+0.000001))\n", "\n", "\n", "# AUC\n", "AUC.append(roc_auc_score(y_test, y_pred))\n", "\n", "\n", "\n", "v_train_dense=v_train.toarray()\n", "v_test_dense=v_test.toarray()\n", "\n", "clf = GaussianNB().fit(v_train_dense, y_train)\n", "y_pred=clf.predict(v_test_dense)\n", "\n", "# Confusion Matrix\n", "cm = confusion_matrix(y_test,y_pred)\n", "\n", "# Accuracy\n", "accuracy.append((cm[1,1]+cm[0,0])/(cm[1,1]+cm[0,0]+cm[0,1]+cm[1,0]+0.000001))\n", "\n", "# Precision\n", "precisions.append(cm[1,1]/(cm[1,1]+cm[0,1]+0.000001))\n", "\n", "# Recall\n", "recall.append(cm[1,1]/(cm[1,1]+cm[1,0]+0.000001))\n", "\n", "\n", "#F1 score\n", "F1.append(2*cm[1,1]/(2*cm[1,1]+cm[0,1]+cm[1,0]+0.000001))\n", "\n", "\n", "# AUC\n", "AUC.append(roc_auc_score(y_test, y_pred))\n", "\n", "\n", "headings = ['Accuracy', 'Precision', 'Recall', 'F1', 'AUC']\n", "names = ['LogisticRegression','NB-Gaussian']\n", "numpy_data=np.array([accuracy,precisions,recall,F1,AUC]).T\n", "\n", "df = pd.DataFrame(data=numpy_data, index=names, columns=headings)\n", "\n", "print('The Performance for Psy is summarized in the following Table:')\n", "\n", "#display(df)\n", "display(df.style.highlight_max(color = 'lightgreen', axis = 0))\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 186}, "id": "nGf-7TRQU9MG", "outputId": "57a1ba44-913c-4467-84ff-7139913c6380"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The Performance for KatyPerry is summarized in the following Table:\n"]}, {"name": "stderr", "output_type": "stream", "text": ["<ipython-input-3-7e677c46460e>:75: DeprecationWarning: Conversion of an array with ndim > 0 to a scalar is deprecated, and will error in future. Ensure you extract a single element from your array before performing this operation. (Deprecated NumPy 1.25.)\n", "  i=int(i[0])\n"]}, {"data": {"text/html": ["<style type=\"text/css\">\n", "#T_d9cf3_row0_col0, #T_d9cf3_row0_col1, #T_d9cf3_row0_col2, #T_d9cf3_row0_col3, #T_d9cf3_row0_col4 {\n", "  background-color: lightgreen;\n", "}\n", "</style>\n", "<table id=\"T_d9cf3\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_d9cf3_level0_col0\" class=\"col_heading level0 col0\" >Accuracy</th>\n", "      <th id=\"T_d9cf3_level0_col1\" class=\"col_heading level0 col1\" >Precision</th>\n", "      <th id=\"T_d9cf3_level0_col2\" class=\"col_heading level0 col2\" >Recall</th>\n", "      <th id=\"T_d9cf3_level0_col3\" class=\"col_heading level0 col3\" >F1</th>\n", "      <th id=\"T_d9cf3_level0_col4\" class=\"col_heading level0 col4\" >AUC</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_d9cf3_level0_row0\" class=\"row_heading level0 row0\" >LogisticRegression</th>\n", "      <td id=\"T_d9cf3_row0_col0\" class=\"data row0 col0\" >0.957143</td>\n", "      <td id=\"T_d9cf3_row0_col1\" class=\"data row0 col1\" >1.000000</td>\n", "      <td id=\"T_d9cf3_row0_col2\" class=\"data row0 col2\" >0.914286</td>\n", "      <td id=\"T_d9cf3_row0_col3\" class=\"data row0 col3\" >0.955224</td>\n", "      <td id=\"T_d9cf3_row0_col4\" class=\"data row0 col4\" >0.957143</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_d9cf3_level0_row1\" class=\"row_heading level0 row1\" >NB-Gaussian</th>\n", "      <td id=\"T_d9cf3_row1_col0\" class=\"data row1 col0\" >0.828571</td>\n", "      <td id=\"T_d9cf3_row1_col1\" class=\"data row1 col1\" >0.848485</td>\n", "      <td id=\"T_d9cf3_row1_col2\" class=\"data row1 col2\" >0.800000</td>\n", "      <td id=\"T_d9cf3_row1_col3\" class=\"data row1 col3\" >0.823529</td>\n", "      <td id=\"T_d9cf3_row1_col4\" class=\"data row1 col4\" >0.828571</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x7928aa18ace0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Compare the two ML approaches in KatyPerry data set\n", "\n", "data = pd.read_csv('Youtube01-Psy.csv')\n", "#data = pd.read_csv('Youtube02-KatyPerry.csv')\n", "#data = pd.read_csv('Youtube03-LMFAO.csv')\n", "#data = pd.read_csv('Youtube04-Eminem.csv')\n", "#data = pd.read_csv('Youtube05-Shakira.csv')\n", "\n", "#data.info()\n", "\n", "X=data.iloc[:,3].to_numpy()\n", "y = data.iloc[:,4].to_numpy()\n", "\n", "index_1=np.where(y==1)\n", "index_0=np.where(y==0)\n", "\n", "L1=np.rint(len(index_1[0])*0.8)\n", "L0=np.rint(len(index_0[0])*0.8)\n", "\n", "L1=L1.astype(int)\n", "L0=L0.astype(int)\n", "\n", "temp=[]\n", "for item in index_1:\n", "    temp=np.append(temp,item)\n", "\n", "temp=temp.astype(int)\n", "\n", "I=temp[0:L1-1]\n", "X_train_1=X[I]\n", "y_train_1=y[I]\n", "\n", "I=temp[L1:len(temp)]\n", "X_test_1=X[I]\n", "y_test_1=y[I]\n", "\n", "temp=[]\n", "for item in index_0:\n", "    temp=np.append(temp,item)\n", "\n", "temp=temp.astype(int)\n", "\n", "I=temp[0:L0-1]\n", "X_train_0=X[I]\n", "y_train_0=y[I]\n", "\n", "I=temp[L0:len(temp)]\n", "X_test_0=X[I]\n", "y_test_0=y[I]\n", "\n", "\n", "# Combine to make X_train, y_train, X_test, y_test\n", "\n", "X_train=np.concatenate((X_train_1, X_train_0), axis=0)\n", "y_train=np.concatenate((y_train_1, y_train_0), axis=0)\n", "\n", "X_test=np.concatenate((X_test_1, X_test_0), axis=0)\n", "y_test=np.concatenate((y_test_1, y_test_0), axis=0)\n", "\n", "\n", "corpus = data.iloc[:,3]\n", "\n", "vectorizer = TfidfVectorizer(stop_words=None, max_features=800).fit(corpus)\n", "#vectorizer = TfidfVectorizer(stop_words=None).fit(corpus)\n", "\n", "v_train=vectorizer.transform(X_train)\n", "\n", "v_test=vectorizer.transform(X_test)\n", "\n", "\n", "clf = LogisticRegressionCV(Cs=list(np.power(10.0, np.arange(5, -6, -1))), cv=5, penalty='l2', scoring='roc_auc',\n", "                           solver='newton-cg', max_iter=30000, random_state=0).fit(v_train, y_train)\n", "\n", "i=np.where(clf.scores_[1].mean(axis=0)==clf.scores_[1].mean(axis=0).max())\n", "i=int(i[0])\n", "Cs=list(np.power(10.0, np.arange(5, -6, -1)))\n", "Cs_select=Cs[i]\n", "\n", "accuracy=[]\n", "precisions=[]\n", "recall=[]\n", "F1=[]\n", "AUC=[]\n", "\n", "clf = LogisticRegression(random_state=0, C=Cs_select, penalty='l2', max_iter=30000).fit(v_train, y_train)\n", "y_pred=clf.predict(v_test)\n", "\n", "\n", "# Confusion Matrix\n", "cm = confusion_matrix(y_test,y_pred)\n", "\n", "# Accuracy\n", "accuracy.append((cm[1,1]+cm[0,0])/(cm[1,1]+cm[0,0]+cm[0,1]+cm[1,0]+0.000001))\n", "\n", "# Precision\n", "precisions.append(cm[1,1]/(cm[1,1]+cm[0,1]+0.000001))\n", "\n", "# Recall\n", "recall.append(cm[1,1]/(cm[1,1]+cm[1,0]+0.000001))\n", "\n", "\n", "#F1 score\n", "F1.append(2*cm[1,1]/(2*cm[1,1]+cm[0,1]+cm[1,0]+0.000001))\n", "\n", "\n", "# AUC\n", "AUC.append(roc_auc_score(y_test, y_pred))\n", "\n", "\n", "\n", "v_train_dense=v_train.toarray()\n", "v_test_dense=v_test.toarray()\n", "\n", "clf = GaussianNB().fit(v_train_dense, y_train)\n", "y_pred=clf.predict(v_test_dense)\n", "\n", "# Confusion Matrix\n", "cm = confusion_matrix(y_test,y_pred)\n", "\n", "# Accuracy\n", "accuracy.append((cm[1,1]+cm[0,0])/(cm[1,1]+cm[0,0]+cm[0,1]+cm[1,0]+0.000001))\n", "\n", "# Precision\n", "precisions.append(cm[1,1]/(cm[1,1]+cm[0,1]+0.000001))\n", "\n", "# Recall\n", "recall.append(cm[1,1]/(cm[1,1]+cm[1,0]+0.000001))\n", "\n", "\n", "#F1 score\n", "F1.append(2*cm[1,1]/(2*cm[1,1]+cm[0,1]+cm[1,0]+0.000001))\n", "\n", "\n", "# AUC\n", "AUC.append(roc_auc_score(y_test, y_pred))\n", "\n", "\n", "headings = ['Accuracy', 'Precision', 'Recall', 'F1', 'AUC']\n", "names = ['LogisticRegression','NB-Gaussian']\n", "numpy_data=np.array([accuracy,precisions,recall,F1,AUC]).T\n", "\n", "df = pd.DataFrame(data=numpy_data, index=names, columns=headings)\n", "\n", "print('The Performance for KatyPerry is summarized in the following Table:')\n", "\n", "#display(df)\n", "display(df.style.highlight_max(color = 'lightgreen', axis = 0))\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 219}, "id": "QYXezsZ3U9MG", "outputId": "5bf51c75-3a04-4adc-8c1e-c7aff1d509cc"}, "outputs": [{"ename": "NameError", "evalue": "name 'pd' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-1-8201961d2d7c>\u001b[0m in \u001b[0;36m<cell line: 5>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[0;31m#data = pd.read_csv('Youtube01-Psy.csv')\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      4\u001b[0m \u001b[0;31m#data = pd.read_csv('Youtube02-KatyPerry.csv')\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 5\u001b[0;31m \u001b[0mdata\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mpd\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mread_csv\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'Youtube03-LMFAO.csv'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      6\u001b[0m \u001b[0;31m#data = pd.read_csv('Youtube04-Eminem.csv')\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      7\u001b[0m \u001b[0;31m#data = pd.read_csv('Youtube05-Shakira.csv')\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mNameError\u001b[0m: name 'pd' is not defined"]}], "source": ["# Compare the two ML approaches in LMFAO data set\n", "\n", "#data = pd.read_csv('Youtube01-Psy.csv')\n", "#data = pd.read_csv('Youtube02-KatyPerry.csv')\n", "data = pd.read_csv('Youtube03-LMFAO.csv')\n", "#data = pd.read_csv('Youtube04-Eminem.csv')\n", "#data = pd.read_csv('Youtube05-Shakira.csv')\n", "\n", "#data.info()\n", "\n", "X=data.iloc[:,3].to_numpy()\n", "y = data.iloc[:,4].to_numpy()\n", "\n", "index_1=np.where(y==1)\n", "index_0=np.where(y==0)\n", "\n", "L1=np.rint(len(index_1[0])*0.8)\n", "L0=np.rint(len(index_0[0])*0.8)\n", "\n", "L1=L1.astype(int)\n", "L0=L0.astype(int)\n", "\n", "temp=[]\n", "for item in index_1:\n", "    temp=np.append(temp,item)\n", "\n", "temp=temp.astype(int)\n", "\n", "I=temp[0:L1-1]\n", "X_train_1=X[I]\n", "y_train_1=y[I]\n", "\n", "I=temp[L1:len(temp)]\n", "X_test_1=X[I]\n", "y_test_1=y[I]\n", "\n", "temp=[]\n", "for item in index_0:\n", "    temp=np.append(temp,item)\n", "\n", "temp=temp.astype(int)\n", "\n", "I=temp[0:L0-1]\n", "X_train_0=X[I]\n", "y_train_0=y[I]\n", "\n", "I=temp[L0:len(temp)]\n", "X_test_0=X[I]\n", "y_test_0=y[I]\n", "\n", "\n", "# Combine to make X_train, y_train, X_test, y_test\n", "\n", "X_train=np.concatenate((X_train_1, X_train_0), axis=0)\n", "y_train=np.concatenate((y_train_1, y_train_0), axis=0)\n", "\n", "X_test=np.concatenate((X_test_1, X_test_0), axis=0)\n", "y_test=np.concatenate((y_test_1, y_test_0), axis=0)\n", "\n", "\n", "corpus = data.iloc[:,3]\n", "\n", "vectorizer = TfidfVectorizer(stop_words=None, max_features=800).fit(corpus)\n", "#vectorizer = TfidfVectorizer(stop_words=None).fit(corpus)\n", "\n", "v_train=vectorizer.transform(X_train)\n", "\n", "v_test=vectorizer.transform(X_test)\n", "\n", "\n", "clf = LogisticRegressionCV(Cs=list(np.power(10.0, np.arange(5, -6, -1))), cv=5, penalty='l2', scoring='roc_auc',\n", "                           solver='newton-cg', max_iter=30000, random_state=0).fit(v_train, y_train)\n", "\n", "i=np.where(clf.scores_[1].mean(axis=0)==clf.scores_[1].mean(axis=0).max())\n", "i=int(i[0])\n", "Cs=list(np.power(10.0, np.arange(5, -6, -1)))\n", "Cs_select=Cs[i]\n", "\n", "accuracy=[]\n", "precisions=[]\n", "recall=[]\n", "F1=[]\n", "AUC=[]\n", "\n", "clf = LogisticRegression(random_state=0, C=Cs_select, penalty='l2', max_iter=30000).fit(v_train, y_train)\n", "y_pred=clf.predict(v_test)\n", "\n", "\n", "# Confusion Matrix\n", "cm = confusion_matrix(y_test,y_pred)\n", "\n", "# Accuracy\n", "accuracy.append((cm[1,1]+cm[0,0])/(cm[1,1]+cm[0,0]+cm[0,1]+cm[1,0]+0.000001))\n", "\n", "# Precision\n", "precisions.append(cm[1,1]/(cm[1,1]+cm[0,1]+0.000001))\n", "\n", "# Recall\n", "recall.append(cm[1,1]/(cm[1,1]+cm[1,0]+0.000001))\n", "\n", "\n", "#F1 score\n", "F1.append(2*cm[1,1]/(2*cm[1,1]+cm[0,1]+cm[1,0]+0.000001))\n", "\n", "\n", "# AUC\n", "AUC.append(roc_auc_score(y_test, y_pred))\n", "\n", "\n", "\n", "v_train_dense=v_train.toarray()\n", "v_test_dense=v_test.toarray()\n", "\n", "clf = GaussianNB().fit(v_train_dense, y_train)\n", "y_pred=clf.predict(v_test_dense)\n", "\n", "# Confusion Matrix\n", "cm = confusion_matrix(y_test,y_pred)\n", "\n", "# Accuracy\n", "accuracy.append((cm[1,1]+cm[0,0])/(cm[1,1]+cm[0,0]+cm[0,1]+cm[1,0]+0.000001))\n", "\n", "# Precision\n", "precisions.append(cm[1,1]/(cm[1,1]+cm[0,1]+0.000001))\n", "\n", "# Recall\n", "recall.append(cm[1,1]/(cm[1,1]+cm[1,0]+0.000001))\n", "\n", "\n", "#F1 score\n", "F1.append(2*cm[1,1]/(2*cm[1,1]+cm[0,1]+cm[1,0]+0.000001))\n", "\n", "\n", "# AUC\n", "AUC.append(roc_auc_score(y_test, y_pred))\n", "\n", "\n", "headings = ['Accuracy', 'Precision', 'Recall', 'F1', 'AUC']\n", "names = ['LogisticRegression','NB-Gaussian']\n", "numpy_data=np.array([accuracy,precisions,recall,F1,AUC]).T\n", "\n", "df = pd.DataFrame(data=numpy_data, index=names, columns=headings)\n", "\n", "print('The Performance for LMFAO is summarized in the following Table:')\n", "\n", "#display(df)\n", "display(df.style.highlight_max(color = 'lightgreen', axis = 0))\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 219}, "id": "kt9DnmQdU9MH", "outputId": "5b76099c-bd08-4715-8a32-5dd4370c14da"}, "outputs": [{"ename": "NameError", "evalue": "name 'pd' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-2-4248d67de329>\u001b[0m in \u001b[0;36m<cell line: 7>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[0;31m#data = pd.read_csv('Youtube03-LMFAO.csv')\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      6\u001b[0m \u001b[0;31m#data = pd.read_csv('Youtube04-Eminem.csv')\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 7\u001b[0;31m \u001b[0mdata\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mpd\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mread_csv\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'Youtube05-Shakira.csv'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      8\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      9\u001b[0m \u001b[0;31m#data.info()\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mNameError\u001b[0m: name 'pd' is not defined"]}], "source": ["# Compare the two ML approaches in Shakira data set\n", "\n", "#data = pd.read_csv('Youtube01-Psy.csv')\n", "#data = pd.read_csv('Youtube02-KatyPerry.csv')\n", "#data = pd.read_csv('Youtube03-LMFAO.csv')\n", "#data = pd.read_csv('Youtube04-Eminem.csv')\n", "data = pd.read_csv('Youtube05-Shakira.csv')\n", "\n", "#data.info()\n", "\n", "X=data.iloc[:,3].to_numpy()\n", "y = data.iloc[:,4].to_numpy()\n", "\n", "index_1=np.where(y==1)\n", "index_0=np.where(y==0)\n", "\n", "L1=np.rint(len(index_1[0])*0.8)\n", "L0=np.rint(len(index_0[0])*0.8)\n", "\n", "L1=L1.astype(int)\n", "L0=L0.astype(int)\n", "\n", "temp=[]\n", "for item in index_1:\n", "    temp=np.append(temp,item)\n", "\n", "temp=temp.astype(int)\n", "\n", "I=temp[0:L1-1]\n", "X_train_1=X[I]\n", "y_train_1=y[I]\n", "\n", "I=temp[L1:len(temp)]\n", "X_test_1=X[I]\n", "y_test_1=y[I]\n", "\n", "temp=[]\n", "for item in index_0:\n", "    temp=np.append(temp,item)\n", "\n", "temp=temp.astype(int)\n", "\n", "I=temp[0:L0-1]\n", "X_train_0=X[I]\n", "y_train_0=y[I]\n", "\n", "I=temp[L0:len(temp)]\n", "X_test_0=X[I]\n", "y_test_0=y[I]\n", "\n", "\n", "# Combine to make X_train, y_train, X_test, y_test\n", "\n", "X_train=np.concatenate((X_train_1, X_train_0), axis=0)\n", "y_train=np.concatenate((y_train_1, y_train_0), axis=0)\n", "\n", "X_test=np.concatenate((X_test_1, X_test_0), axis=0)\n", "y_test=np.concatenate((y_test_1, y_test_0), axis=0)\n", "\n", "\n", "corpus = data.iloc[:,3]\n", "\n", "vectorizer = TfidfVectorizer(stop_words=None, max_features=800).fit(corpus)\n", "#vectorizer = TfidfVectorizer(stop_words=None).fit(corpus)\n", "\n", "v_train=vectorizer.transform(X_train)\n", "\n", "v_test=vectorizer.transform(X_test)\n", "\n", "\n", "clf = LogisticRegressionCV(Cs=list(np.power(10.0, np.arange(5, -6, -1))), cv=5, penalty='l2', scoring='roc_auc',\n", "                           solver='newton-cg', max_iter=30000, random_state=0).fit(v_train, y_train)\n", "\n", "i=np.where(clf.scores_[1].mean(axis=0)==clf.scores_[1].mean(axis=0).max())\n", "i=int(i[0])\n", "Cs=list(np.power(10.0, np.arange(5, -6, -1)))\n", "Cs_select=Cs[i]\n", "\n", "accuracy=[]\n", "precisions=[]\n", "recall=[]\n", "F1=[]\n", "AUC=[]\n", "\n", "clf = LogisticRegression(random_state=0, C=Cs_select, penalty='l2', max_iter=30000).fit(v_train, y_train)\n", "y_pred=clf.predict(v_test)\n", "\n", "\n", "# Confusion Matrix\n", "cm = confusion_matrix(y_test,y_pred)\n", "\n", "# Accuracy\n", "accuracy.append((cm[1,1]+cm[0,0])/(cm[1,1]+cm[0,0]+cm[0,1]+cm[1,0]+0.000001))\n", "\n", "# Precision\n", "precisions.append(cm[1,1]/(cm[1,1]+cm[0,1]+0.000001))\n", "\n", "# Recall\n", "recall.append(cm[1,1]/(cm[1,1]+cm[1,0]+0.000001))\n", "\n", "\n", "#F1 score\n", "F1.append(2*cm[1,1]/(2*cm[1,1]+cm[0,1]+cm[1,0]+0.000001))\n", "\n", "\n", "# AUC\n", "AUC.append(roc_auc_score(y_test, y_pred))\n", "\n", "\n", "\n", "v_train_dense=v_train.toarray()\n", "v_test_dense=v_test.toarray()\n", "\n", "clf = GaussianNB().fit(v_train_dense, y_train)\n", "y_pred=clf.predict(v_test_dense)\n", "\n", "# Confusion Matrix\n", "cm = confusion_matrix(y_test,y_pred)\n", "\n", "# Accuracy\n", "accuracy.append((cm[1,1]+cm[0,0])/(cm[1,1]+cm[0,0]+cm[0,1]+cm[1,0]+0.000001))\n", "\n", "# Precision\n", "precisions.append(cm[1,1]/(cm[1,1]+cm[0,1]+0.000001))\n", "\n", "# Recall\n", "recall.append(cm[1,1]/(cm[1,1]+cm[1,0]+0.000001))\n", "\n", "\n", "#F1 score\n", "F1.append(2*cm[1,1]/(2*cm[1,1]+cm[0,1]+cm[1,0]+0.000001))\n", "\n", "\n", "# AUC\n", "AUC.append(roc_auc_score(y_test, y_pred))\n", "\n", "\n", "headings = ['Accuracy', 'Precision', 'Recall', 'F1', 'AUC']\n", "names = ['LogisticRegression','NB-Gaussian']\n", "numpy_data=np.array([accuracy,precisions,recall,F1,AUC]).T\n", "\n", "df = pd.DataFrame(data=numpy_data, index=names, columns=headings)\n", "\n", "print('The Performance for <PERSON><PERSON><PERSON> is summarized in the following Table:')\n", "\n", "#display(df)\n", "display(df.style.highlight_max(color = 'lightgreen', axis = 0))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "PrJqpIEkU9MH"}, "outputs": [], "source": ["from sklearn.metrics import roc_auc_score, roc_curve\n", "\n", "# Instantiate the KNN classifier\n", "knn = KNeighborsClassifier(n_neighbors=9)  # Adjust n_neighbors as needed\n", "\n", "# Train the classifier\n", "knn.fit(v_train, y_train)\n", "\n", "# Make predictions on the training data\n", "y_train_pred = knn.predict(v_train)\n", "\n", "# Make predictions on the test data\n", "y_test_pred = knn.predict(v_test)\n", "\n", "# Evaluate the performance on training data\n", "accuracy_train = accuracy_score(y_train, y_train_pred)\n", "precision_train = precision_score(y_train, y_train_pred)\n", "auc_roc_train = roc_auc_score(y_train, y_train_pred)\n", "\n", "# Print training performance metrics\n", "print(\"Training Accuracy:\", accuracy_train)\n", "print(\"Training Precision:\", precision_train)\n", "print(\"Training AUC-ROC:\", auc_roc_train)\n", "\n", "# Evaluate the performance on test data\n", "accuracy_test = accuracy_score(y_test, y_test_pred)\n", "precision_test = precision_score(y_test, y_test_pred)\n", "auc_roc_test = roc_auc_score(y_test, y_test_pred)\n", "\n", "# Print test performance metrics\n", "print(\"Test Accuracy:\", accuracy_test)\n", "print(\"Test Precision:\", precision_test)\n", "print(\"Test AUC-ROC:\", auc_roc_test)\n", "\n", "# Plot the AUC-ROC curve for training data\n", "fpr_train, tpr_train, thresholds_train = roc_curve(y_train, y_train_pred)\n", "\n", "# Plot the AUC-ROC curve for test data\n", "fpr_test, tpr_test, thresholds_test = roc_curve(y_test, y_test_pred)\n", "\n", "plt.figure()\n", "plt.plot(fpr_train, tpr_train, label='KNN Classifier (Training AUC = %0.2f)' % auc_roc_train)\n", "plt.plot(fpr_test, tpr_test, label='KNN Classifier (Test AUC = %0.2f)' % auc_roc_test)\n", "plt.xlabel('False Positive Rate')\n", "plt.ylabel('True Positive Rate')\n", "plt.title('Receiver Operating Characteristic (ROC) Curve')\n", "plt.legend(loc='lower right')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "mKV6FDYkibYs"}, "outputs": [], "source": ["from sklearn.neural_network import MLPClassifier\n", "from sklearn.metrics import roc_auc_score, roc_curve\n", "\n", "# Instantiate the MLP classifier\n", "mlp = MLPClassifier(hidden_layer_sizes=(220, 30), max_iter=100)\n", "\n", "# Train the classifier\n", "mlp.fit(v_train, y_train)\n", "\n", "# Make predictions on the training data\n", "y_train_pred = mlp.predict(v_train)\n", "\n", "# Make predictions on the test data\n", "y_test_pred = mlp.predict(v_test)\n", "\n", "# Evaluate the performance on training data\n", "accuracy_train = accuracy_score(y_train, y_train_pred)\n", "precision_train = precision_score(y_train, y_train_pred)\n", "auc_roc_train = roc_auc_score(y_train, y_train_pred)\n", "\n", "# Print training performance metrics\n", "print(\"Training Accuracy:\", accuracy_train)\n", "print(\"Training Precision:\", precision_train)\n", "print(\"Training AUC-ROC:\", auc_roc_train)\n", "\n", "# Evaluate the performance on test data\n", "accuracy_test = accuracy_score(y_test, y_test_pred)\n", "precision_test = precision_score(y_test, y_test_pred)\n", "auc_roc_test = roc_auc_score(y_test, y_test_pred)\n", "\n", "# Print test performance metrics\n", "print(\"Test Accuracy:\", accuracy_test)\n", "print(\"Test Precision:\", precision_test)\n", "print(\"Test AUC-ROC:\", auc_roc_test)\n", "\n", "# Plot the AUC-ROC curve for training data\n", "fpr_train, tpr_train, thresholds_train = roc_curve(y_train, y_train_pred)\n", "\n", "# Plot the AUC-ROC curve for test data\n", "fpr_test, tpr_test, thresholds_test = roc_curve(y_test, y_test_pred)\n", "\n", "plt.figure()\n", "plt.plot(fpr_train, tpr_train, label='MLP Classifier (Training AUC = %0.2f)' % auc_roc_train)\n", "plt.plot(fpr_test, tpr_test, label='MLP Classifier (Test AUC = %0.2f)' % auc_roc_test)\n", "plt.xlabel('False Positive Rate')\n", "plt.ylabel('True Positive Rate')\n", "plt.title('Receiver Operating Characteristic (ROC) Curve')\n", "plt.legend(loc='lower right')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 219}, "id": "yPW3o2B1waoY", "outputId": "6690e911-7a53-442f-f8da-91663bcc7433"}, "outputs": [{"ename": "NameError", "evalue": "name 'v_train' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-1-960c23703663>\u001b[0m in \u001b[0;36m<cell line: 16>\u001b[0;34m()\u001b[0m\n\u001b[1;32m     14\u001b[0m \u001b[0;31m# Perform grid search to find the best hyperparameters\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     15\u001b[0m \u001b[0mgrid_search\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mGridSearchCV\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0msvm\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mparam_grid\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mcv\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;36m5\u001b[0m\u001b[0;34m)\u001b[0m  \u001b[0;31m# Adjust cv (number of cross-validation folds)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 16\u001b[0;31m \u001b[0mgrid_search\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mfit\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mv_train\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0my_train\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     17\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     18\u001b[0m \u001b[0;31m# Get the best hyperparameters\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mNameError\u001b[0m: name 'v_train' is not defined"]}], "source": ["from sklearn.svm import SVC\n", "from sklearn.model_selection import GridSearchCV\n", "from sklearn.metrics import roc_auc_score, roc_curve\n", "\n", "# Parameter grid for grid search\n", "param_grid = {\n", "    'C': [0.1, 1, 10],  # Adjust C values as needed\n", "    'gamma': [0.1, 1, 10]  # Adjust gamma values as needed\n", "}\n", "\n", "# Instantiate the SVM classifier\n", "svm = SVC()  # adjust hyperparameters later\n", "\n", "# Perform grid search to find the best hyperparameters\n", "grid_search = GridSearchCV(svm, param_grid, cv=5)  # Adjust cv (number of cross-validation folds)\n", "grid_search.fit(v_train, y_train)\n", "\n", "# Get the best hyperparameters\n", "best_C = grid_search.best_params_['C']\n", "best_gamma = grid_search.best_params_['gamma']\n", "\n", "print(\"Best C:\", best_C)\n", "print(\"Best gamma:\", best_gamma)\n", "\n", "# Instantiate the SVC classifier with the best hyperparameters\n", "svm_best = SVC(C=best_C, gamma=best_gamma)\n", "\n", "# Train the classifier\n", "svm_best.fit(v_train, y_train)\n", "\n", "# Make predictions on the training data\n", "y_train_pred = svm_best.predict(v_train)\n", "\n", "# Make predictions on the test data\n", "y_test_pred = svm_best.predict(v_test)\n", "\n", "# Evaluate the performance on training data\n", "accuracy_train = accuracy_score(y_train, y_train_pred)\n", "precision_train = precision_score(y_train, y_train_pred)\n", "auc_roc_train = roc_auc_score(y_train, y_train_pred)\n", "\n", "# Print training performance metrics\n", "print(\"Training Accuracy:\", accuracy_train)\n", "print(\"Training Precision:\", precision_train)\n", "print(\"Training AUC-ROC:\", auc_roc_train)\n", "\n", "# Evaluate the performance on test data\n", "accuracy_test = accuracy_score(y_test, y_test_pred)\n", "precision_test = precision_score(y_test, y_test_pred)\n", "auc_roc_test = roc_auc_score(y_test, y_test_pred)\n", "\n", "# Print test performance metrics\n", "print(\"Test Accuracy:\", accuracy_test)\n", "print(\"Test Precision:\", precision_test)\n", "print(\"Test AUC-ROC:\", auc_roc_test)\n", "\n", "# Plot the AUC-ROC curve for training data\n", "fpr_train, tpr_train, thresholds_train = roc_curve(y_train, y_train_pred)\n", "\n", "# Plot the AUC-ROC curve for test data\n", "fpr_test, tpr_test, thresholds_test = roc_curve(y_test, y_test_pred)\n", "\n", "plt.figure()\n", "plt.plot(fpr_train, tpr_train, label='SVM SVC Classifier (Training AUC = %0.2f)' % auc_roc_train)\n", "plt.plot(fpr_test, tpr_test, label='SVM SVC Classifier (Test AUC = %0.2f)' % auc_roc_test)\n", "plt.xlabel('False Positive Rate')\n", "plt.ylabel('True Positive Rate')\n", "plt.title('Receiver Operating Characteristic (ROC) Curve')\n", "plt.legend(loc='lower right')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "5i_cSdron7Pd"}, "outputs": [], "source": ["from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import GridSearchCV\n", "from sklearn.metrics import roc_auc_score, roc_curve\n", "\n", "# Parameter grid for grid search\n", "param_grid = {\n", "    'n_estimators': [20, 50, 75, 100, 150, 175, 180, 190, 200, 300],\n", "    'max_depth': [None, 10, 20, 30, 40, 50, 60]\n", "}\n", "\n", "# Instantiate the Random Forest Classifier\n", "random_forest = RandomForestClassifier()\n", "\n", "# Perform grid search to find the best hyperparameters\n", "grid_search = GridSearchCV(random_forest, param_grid, cv=5)  # Adjust cv (number of cross-validation folds)\n", "grid_search.fit(v_train, y_train)\n", "\n", "# Get the best hyperparameters\n", "best_n_estimators = grid_search.best_params_['n_estimators']\n", "best_max_depth = grid_search.best_params_['max_depth']\n", "\n", "print(\"Best n_estimators:\", best_n_estimators)\n", "print(\"Best max_depth:\", best_max_depth)\n", "\n", "# Instantiate the Random Forest Classifier with the best hyperparameters\n", "random_forest_best = RandomForestClassifier(n_estimators=best_n_estimators, max_depth=best_max_depth)\n", "\n", "# Train the classifier\n", "random_forest_best.fit(v_train, y_train)\n", "\n", "# Make predictions on the training data\n", "y_train_pred = random_forest_best.predict(v_train)\n", "\n", "# Make predictions on the test data\n", "y_test_pred = random_forest_best.predict(v_test)\n", "\n", "# Evaluate the performance on training data\n", "accuracy_train = accuracy_score(y_train, y_train_pred)\n", "precision_train = precision_score(y_train, y_train_pred)\n", "auc_roc_train = roc_auc_score(y_train, y_train_pred)\n", "\n", "# Print training performance metrics\n", "print(\"Training Accuracy:\", accuracy_train)\n", "print(\"Training Precision:\", precision_train)\n", "print(\"Training AUC-ROC:\", auc_roc_train)\n", "\n", "# Evaluate the performance on test data\n", "accuracy_test = accuracy_score(y_test, y_test_pred)\n", "precision_test = precision_score(y_test, y_test_pred)\n", "auc_roc_test = roc_auc_score(y_test, y_test_pred)\n", "\n", "# Print test performance metrics\n", "print(\"Test Accuracy:\", accuracy_test)\n", "print(\"Test Precision:\", precision_test)\n", "print(\"Test AUC-ROC:\", auc_roc_test)\n", "\n", "# Plot the AUC-ROC curve for training data\n", "fpr_train, tpr_train, thresholds_train = roc_curve(y_train, y_train_pred)\n", "\n", "# Plot the AUC-ROC curve for test data\n", "fpr_test, tpr_test, thresholds_test = roc_curve(y_test, y_test_pred)\n", "\n", "plt.figure()\n", "plt.plot(fpr_train, tpr_train, label='Random Forest Classifier (Training AUC = %0.2f)' % auc_roc_train)\n", "plt.plot(fpr_test, tpr_test, label='Random Forest Classifier (Test AUC = %0.2f)' % auc_roc_test)\n", "plt.xlabel('False Positive Rate')\n", "plt.ylabel('True Positive Rate')\n", "plt.title('Receiver Operating Characteristic (ROC) Curve')\n", "plt.legend(loc='lower right')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"background_save": true}, "id": "7PIK6yKEtz5L", "outputId": "e1a45cfb-29c2-430b-fee7-65760f3f657b"}, "outputs": [{"ename": "NameError", "evalue": "name 'v_train' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-1-ef8d8c047fc3>\u001b[0m in \u001b[0;36m<cell line: 8>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      6\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      7\u001b[0m \u001b[0;31m# Train the classifier\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 8\u001b[0;31m \u001b[0mdecision_tree\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mfit\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mv_train\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0my_train\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      9\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     10\u001b[0m \u001b[0;31m# Make predictions on the training data\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mNameError\u001b[0m: name 'v_train' is not defined"]}], "source": ["from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.metrics import roc_auc_score, roc_curve\n", "\n", "# Instantiate the Decision Tree Classifier\n", "decision_tree = DecisionTreeClassifier()\n", "\n", "# Train the classifier\n", "decision_tree.fit(v_train, y_train)\n", "\n", "# Make predictions on the training data\n", "y_train_pred = decision_tree.predict(v_train)\n", "\n", "# Make predictions on the test data\n", "y_test_pred = decision_tree.predict(v_test)\n", "\n", "# Evaluate the performance on training data\n", "accuracy_train = accuracy_score(y_train, y_train_pred)\n", "precision_train = precision_score(y_train, y_train_pred)\n", "auc_roc_train = roc_auc_score(y_train, y_train_pred)\n", "\n", "# Print training performance metrics\n", "print(\"Training Accuracy:\", accuracy_train)\n", "print(\"Training Precision:\", precision_train)\n", "print(\"Training AUC-ROC:\", auc_roc_train)\n", "\n", "# Evaluate the performance on test data\n", "accuracy_test = accuracy_score(y_test, y_test_pred)\n", "precision_test = precision_score(y_test, y_test_pred)\n", "auc_roc_test = roc_auc_score(y_test, y_test_pred)\n", "\n", "# Print test performance metrics\n", "print(\"Test Accuracy:\", accuracy_test)\n", "print(\"Test Precision:\", precision_test)\n", "print(\"Test AUC-ROC:\", auc_roc_test)\n", "\n", "# Plot the AUC-ROC curve for training data\n", "fpr_train, tpr_train, thresholds_train = roc_curve(y_train, y_train_pred)\n", "\n", "# Plot the AUC-ROC curve for test data\n", "fpr_test, tpr_test, thresholds_test = roc_curve(y_test, y_test_pred)\n", "\n", "plt.figure()\n", "plt.plot(fpr_train, tpr_train, label='Decision Tree Classifier (Training AUC = %0.2f)' % auc_roc_train)\n", "plt.plot(fpr_test, tpr_test, label='Decision Tree Classifier (Test AUC = %0.2f)' % auc_roc_test)\n", "plt.xlabel('False Positive Rate')\n", "plt.ylabel('True Positive Rate')\n", "plt.title('Receiver Operating Characteristic (ROC) Curve')\n", "plt.legend(loc='lower right')\n", "plt.show()\n", "\n", "# Print F1 score and recall for test data\n", "f1 = f1_score(y_test, y_test_pred)\n", "recall = recall_score(y_test, y_test_pred)\n", "print('F1 score:', f1)\n", "print('Recall:', recall)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "3uqRlXJKxgRL"}, "outputs": [], "source": ["from sklearn.ensemble import VotingClassifier\n", "from sklearn.metrics import roc_auc_score, roc_curve\n", "\n", "# Define individual classifiers\n", "knn_classifier = KNeighborsClassifier()\n", "mlp_classifier = MLPClassifier()\n", "svc_classifier = SVC(probability=True)\n", "decision_tree = DecisionTreeClassifier()\n", "random_forest = RandomForestClassifier()\n", "\n", "# Create a Voting Classifier\n", "voting_classifier = VotingClassifier(\n", "    estimators=[\n", "        ('knn', knn_classifier),\n", "        ('mlp', mlp_classifier),\n", "        ('svc', svc_classifier),\n", "        ('decision_tree', decision_tree),\n", "        ('random_forest', random_forest)\n", "    ],\n", "    voting='hard'\n", ")\n", "\n", "# Train the voting classifier\n", "voting_classifier.fit(v_train, y_train)\n", "\n", "# Make predictions on the training data\n", "y_train_pred = voting_classifier.predict(v_train)\n", "\n", "# Make predictions on the test data\n", "y_test_pred = voting_classifier.predict(v_test)\n", "\n", "# Evaluate the performance on training data\n", "accuracy_train = accuracy_score(y_train, y_train_pred)\n", "precision_train = precision_score(y_train, y_train_pred)\n", "auc_roc_train = roc_auc_score(y_train, y_train_pred)\n", "\n", "# Print training performance metrics\n", "print(\"Training Accuracy:\", accuracy_train)\n", "print(\"Training Precision:\", precision_train)\n", "print(\"Training AUC-ROC:\", auc_roc_train)\n", "\n", "# Evaluate the performance on test data\n", "accuracy_test = accuracy_score(y_test, y_test_pred)\n", "precision_test = precision_score(y_test, y_test_pred)\n", "auc_roc_test = roc_auc_score(y_test, y_test_pred)\n", "\n", "# Print test performance metrics\n", "print(\"Test Accuracy:\", accuracy_test)\n", "print(\"Test Precision:\", precision_test)\n", "print(\"Test AUC-ROC:\", auc_roc_test)\n", "\n", "# Plot the AUC-ROC curve for training data\n", "fpr_train, tpr_train, thresholds_train = roc_curve(y_train, y_train_pred)\n", "\n", "# Plot the AUC-ROC curve for test data\n", "fpr_test, tpr_test, thresholds_test = roc_curve(y_test, y_test_pred)\n", "\n", "plt.figure()\n", "plt.plot(fpr_train, tpr_train, label='Voting Classifier (Training AUC = %0.2f)' % auc_roc_train)\n", "plt.plot(fpr_test, tpr_test, label='Voting Classifier (Test AUC = %0.2f)' % auc_roc_test)\n", "plt.xlabel('False Positive Rate')\n", "plt.ylabel('True Positive Rate')\n", "plt.title('Receiver Operating Characteristic (ROC) Curve')\n", "plt.legend(loc='lower right')\n", "plt.show()\n", "\n", "# Print F1 score and recall for test data\n", "f1 = f1_score(y_test, y_test_pred)\n", "recall = recall_score(y_test, y_test_pred)\n", "print('F1 score:', f1)\n", "print('Recall:', recall)\n"]}], "metadata": {"@webio": {"lastCommId": null, "lastKernelId": null}, "colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 0}