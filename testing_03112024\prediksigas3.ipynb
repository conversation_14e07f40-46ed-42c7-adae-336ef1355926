{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "\n", "def load_and_prepare_data(data_path):\n", "    try:\n", "        df = pd.read_csv(data_path, delimiter=';', on_bad_lines='skip', skipinitialspace=True)\n", "    except pd.errors.<PERSON><PERSON>r<PERSON><PERSON>r as e:\n", "        print(f\"Error loading CSV: {e}\")\n", "        raise SystemExit(e)\n", "    \n", "    # Standardize column names\n", "    df.columns = df.columns.str.strip().str.upper()\n", "    # Remove rows with missing values\n", "    df.dropna(inplace=True)\n", "    # Fill missing values with column mean\n", "    df.fillna(df.mean(), inplace=True)\n", "    return df\n", "\n", "def split_data(df):\n", "    try:\n", "        X = df[['SUHU1', 'SUHU2', 'SUHU3', 'SUHU4']]\n", "        y_methane = df['METHANE']\n", "        y_co = df['CO']\n", "        y_hydrogen = df['HYDROGEN']\n", "    except KeyError as e:\n", "        print(f\"KeyError: {e}. Ensure temperature and gas component columns are present in the CSV.\")\n", "        raise SystemExit(e)\n", "    \n", "    # Split the dataset into training and testing sets (80% train, 20% test)\n", "    X_train, X_test, y_methane_train, y_methane_test = train_test_split(X, y_methane, test_size=0.2, random_state=42)\n", "    _, _, y_co_train, y_co_test = train_test_split(X, y_co, test_size=0.2, random_state=42)\n", "    _, _, y_hydrogen_train, y_hydrogen_test = train_test_split(X, y_hydrogen, test_size=0.2, random_state=42)\n", "    \n", "    return X_train, X_test, y_methane_train, y_methane_test, y_co_train, y_co_test, y_hydrogen_train, y_hydrogen_test\n", "\n", "def train_models(X_train, y_methane_train, y_co_train, y_hydrogen_train):\n", "    # Create and train the linear regression models for each gas component\n", "    methane_model = LinearRegression().fit(X_train, y_methane_train)\n", "    co_model = LinearRegression().fit(X_train, y_co_train)\n", "    hydrogen_model = LinearRegression().fit(X_train, y_hydrogen_train)\n", "    return methane_model, co_model, hydrogen_model\n", "\n", "def evaluate_models(models, X_test, y_tests):\n", "    results = {}\n", "    for model, y_test, name in zip(models, y_tests, ['Methane', 'CO', 'Hydrogen']):\n", "        y_pred = model.predict(X_test)\n", "        r2 = r2_score(y_test, y_pred)\n", "        mse = mean_squared_error(y_test, y_pred)\n", "        results[name] = {'R^2': r2, 'MSE': mse}\n", "    return results\n", "\n", "def predict_average(models, average_temp):\n", "    predictions = {}\n", "    for model, name in zip(models, ['Methane', 'CO', 'Hydrogen']):\n", "        predictions[name] = model.predict(average_temp)[0]\n", "    return predictions\n", "\n", "def plot_trends(df):\n", "    plt.figure(figsize=(14, 8))\n", "    plt.plot(df.index, df['METHANE'], label='Methane', color='blue')\n", "    plt.plot(df.index, df['CO'], label='CO', color='green')\n", "    plt.plot(df.index, df['HYDROGEN'], label='Hydrogen', color='red')\n", "    plt.xlabel('Index')\n", "    plt.ylabel('Gas Concentration')\n", "    plt.title('Trends of Gas Components')\n", "    plt.legend()\n", "    plt.xticks(rotation=45)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Main execution\n", "if __name__ == \"__main__\":\n", "    data_path = '/mnt/data/gasifikasibagus2.csv'\n", "    sheet1_df = load_and_prepare_data(data_path)\n", "    \n", "    X_train, X_test, y_methane_train, y_methane_test, y_co_train, y_co_test, y_hydrogen_train, y_hydrogen_test = split_data(sheet1_df)\n", "    \n", "    methane_model, co_model, hydrogen_model = train_models(X_train, y_methane_train, y_co_train, y_hydrogen_train)\n", "    \n", "    results = evaluate_models([methane_model, co_model, hydrogen_model], X_test, [y_methane_test, y_co_test, y_hydrogen_test])\n", "    for gas, metrics in results.items():\n", "        print(f\"{gas} R^2: {metrics['R^2']}, {gas} MSE: {metrics['MSE']}\")\n", "    \n", "    # Predict gas compositions for an average temperature of 300 degrees Celsius\n", "    average_temp = np.array([[300, 300, 300, 300]])\n", "    predictions = predict_average([methane_model, co_model, hydrogen_model], average_temp)\n", "    for gas, value in predictions.items():\n", "        print(f\"Predicted {gas} (ppm): {value}\")\n", "    \n", "    # Plot trends\n", "    plot_trends(sheet1_df)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}