YOUTUBE SPAM CLASSIFICATION - TRAINING REPORT
==================================================
Training Date: 2025-06-30 00:04:23
Dataset: dataset/youtube_spam.csv
Total Samples: 1896
Class Distribution: {1: 955, 0: 941}

SPLIT: TRAIN_75_TEST_25
------------------------------
NAIVE_BAYES:
  Accuracy: 0.8840
  Train Size: 1422
  Test Size: 474

LOGISTIC_REGRESSION:
  Accuracy: 0.9241
  Train Size: 1422
  Test Size: 474

RANDOM_FOREST:
  Accuracy: 0.9198
  Train Size: 1422
  Test Size: 474

SVM:
  Accuracy: 0.9304
  Train Size: 1422
  Test Size: 474


SPLIT: TRAIN_70_TEST_30
------------------------------
NAIVE_BAYES:
  Accuracy: 0.8822
  Train Size: 1327
  Test Size: 569

LOGISTIC_REGRESSION:
  Accuracy: 0.9227
  Train Size: 1327
  Test Size: 569

RANDOM_FOREST:
  Accuracy: 0.9297
  Train Size: 1327
  Test Size: 569

SVM:
  Accuracy: 0.9297
  Train Size: 1327
  Test Size: 569


SPLIT: TRAIN_65_TEST_35
------------------------------
NAIVE_BAYES:
  Accuracy: 0.8810
  Train Size: 1232
  Test Size: 664

LOGISTIC_REGRESSION:
  Accuracy: 0.9142
  Train Size: 1232
  Test Size: 664

RANDOM_FOREST:
  Accuracy: 0.9066
  Train Size: 1232
  Test Size: 664

SVM:
  Accuracy: 0.9232
  Train Size: 1232
  Test Size: 664

