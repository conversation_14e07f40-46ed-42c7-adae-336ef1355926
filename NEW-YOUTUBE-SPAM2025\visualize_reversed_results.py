#!/usr/bin/env python3
"""
YouTube Spam Classification - Reversed Split Results Visualization
==================================================================

Visualizes and analyzes results from the reversed split training
(25/75, 30/70, 35/65 train/test ratios).

Author: AI Assistant
Date: 2025-06-29
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import json
import os
from datetime import datetime
import numpy as np

class ReversedResultsVisualizer:
    """Visualizer for reversed split training results"""
    
    def __init__(self):
        self.results_dir = "NEW-YOUTUBE-SPAM2025/results_reversed"
        self.dataset_dir = "NEW-YOUTUBE-SPAM2025/dataset_reversed"
        
        # Create results directories
        os.makedirs(self.results_dir, exist_ok=True)
        os.makedirs(os.path.join(self.results_dir, "split_analysis"), exist_ok=True)
        os.makedirs(os.path.join(self.results_dir, "visualizations"), exist_ok=True)
        os.makedirs(os.path.join(self.results_dir, "tables"), exist_ok=True)
        
        self.load_results()
    
    def load_results(self):
        """Load all reversed split results"""
        self.all_results = []
        
        splits = ['train_25_test_75_REVERSED', 'train_30_test_70_REVERSED', 'train_35_test_65_REVERSED']
        
        for split in splits:
            results_file = os.path.join(self.dataset_dir, f"{split}_results.json")
            if os.path.exists(results_file):
                with open(results_file, 'r') as f:
                    split_data = json.load(f)
                
                for model_name, metrics in split_data.items():
                    self.all_results.append({
                        'Split': split.replace('_REVERSED', '').replace('_', ' ').title(),
                        'Split_Code': split,
                        'Model': model_name.replace('_', ' ').title(),
                        'Accuracy': metrics['accuracy'],
                        'Train_Size': metrics['train_size'],
                        'Test_Size': metrics['test_size'],
                        'Train_Test_Ratio': metrics['train_test_ratio'],
                        'Spam_Precision': metrics['classification_report']['1']['precision'],
                        'Spam_Recall': metrics['classification_report']['1']['recall'],
                        'Spam_F1_Score': metrics['classification_report']['1']['f1-score'],
                        'Ham_Precision': metrics['classification_report']['0']['precision'],
                        'Ham_Recall': metrics['classification_report']['0']['recall'],
                        'Ham_F1_Score': metrics['classification_report']['0']['f1-score']
                    })
        
        self.df = pd.DataFrame(self.all_results)
        # Sort by accuracy descending
        self.df = self.df.sort_values('Accuracy', ascending=False).reset_index(drop=True)
    
    def create_reversed_split_visualizations(self):
        """Create visualizations for each reversed split"""
        print("📊 Creating reversed split-specific visualizations...")
        
        splits = ['train_25_test_75_REVERSED', 'train_30_test_70_REVERSED', 'train_35_test_65_REVERSED']
        
        for split_code in splits:
            split_data = self.df[self.df['Split_Code'] == split_code]
            split_name = split_code.replace('_REVERSED', '').replace('_', ' ').title()
            
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle(f'REVERSED SPLIT ANALYSIS: {split_name}\n(Limited Training Data)', fontsize=16, fontweight='bold')
            
            # 1. Model Accuracy Comparison
            ax1 = axes[0, 0]
            bars = ax1.bar(split_data['Model'], split_data['Accuracy'], 
                          color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
            ax1.set_title('Model Accuracy Comparison', fontweight='bold')
            ax1.set_ylabel('Accuracy')
            ax1.set_ylim(0.8, 1.0)
            
            # Add value labels on bars
            for bar, acc in zip(bars, split_data['Accuracy']):
                ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
                        f'{acc:.3f}', ha='center', va='bottom', fontweight='bold')
            
            # 2. Spam Detection Metrics (Precision vs Recall)
            ax2 = axes[0, 1]
            ax2.scatter(split_data['Spam_Recall'], split_data['Spam_Precision'], 
                       s=100, c=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
            for i, model in enumerate(split_data['Model']):
                ax2.annotate(model, (split_data['Spam_Recall'].iloc[i], split_data['Spam_Precision'].iloc[i]),
                           xytext=(5, 5), textcoords='offset points', fontsize=9)
            ax2.set_xlabel('Spam Recall')
            ax2.set_ylabel('Spam Precision')
            ax2.set_title('Spam Detection: Precision vs Recall', fontweight='bold')
            ax2.grid(True, alpha=0.3)
            
            # 3. F1-Score Comparison
            ax3 = axes[1, 0]
            x = np.arange(len(split_data))
            width = 0.35
            ax3.bar(x - width/2, split_data['Ham_F1_Score'], width, label='Ham F1', color='#FFB6C1')
            ax3.bar(x + width/2, split_data['Spam_F1_Score'], width, label='Spam F1', color='#FFA07A')
            ax3.set_xlabel('Models')
            ax3.set_ylabel('F1-Score')
            ax3.set_title('F1-Score Comparison by Class', fontweight='bold')
            ax3.set_xticks(x)
            ax3.set_xticklabels(split_data['Model'], rotation=45)
            ax3.legend()
            ax3.grid(True, alpha=0.3)
            
            # 4. Training Set Size Impact
            ax4 = axes[1, 1]
            train_size = split_data['Train_Size'].iloc[0]
            test_size = split_data['Test_Size'].iloc[0]
            
            # Create metrics comparison
            metrics = ['Accuracy', 'Spam_Precision', 'Spam_Recall', 'Spam_F1_Score']
            colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
            
            for i, model in enumerate(split_data['Model']):
                model_metrics = [split_data[metric].iloc[i] for metric in metrics]
                ax4.plot(metrics, model_metrics, marker='o', linewidth=2, 
                        label=model, color=colors[i])
            
            ax4.set_title(f'Performance Metrics\n(Train: {train_size}, Test: {test_size})', fontweight='bold')
            ax4.set_ylabel('Score')
            ax4.legend()
            ax4.grid(True, alpha=0.3)
            ax4.set_xticklabels(metrics, rotation=45)
            
            plt.tight_layout()
            
            # Save the plot
            filename = f"{split_code}_analysis.png"
            filepath = os.path.join(self.results_dir, "split_analysis", filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"✅ Created visualization: {filename}")
    
    def create_overall_comparison(self):
        """Create overall performance comparison"""
        print("📊 Creating overall reversed split comparison...")
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('REVERSED SPLITS: Overall Performance Analysis\n(Small Train / Large Test)', 
                     fontsize=16, fontweight='bold')
        
        # 1. Accuracy Heatmap
        ax1 = axes[0, 0]
        pivot_data = self.df.pivot(index='Model', columns='Split', values='Accuracy')
        sns.heatmap(pivot_data, annot=True, fmt='.3f', cmap='RdYlBu_r', ax=ax1)
        ax1.set_title('Accuracy Heatmap by Model and Split', fontweight='bold')
        
        # 2. Training Set Size Impact
        ax2 = axes[0, 1]
        for model in self.df['Model'].unique():
            model_data = self.df[self.df['Model'] == model]
            ax2.plot(model_data['Train_Size'], model_data['Accuracy'], 
                    marker='o', linewidth=2, label=model)
        ax2.set_xlabel('Training Set Size')
        ax2.set_ylabel('Accuracy')
        ax2.set_title('Accuracy vs Training Set Size', fontweight='bold')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. Model Performance Ranking
        ax3 = axes[1, 0]
        model_avg = self.df.groupby('Model')['Accuracy'].mean().sort_values(ascending=True)
        bars = ax3.barh(model_avg.index, model_avg.values, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
        ax3.set_xlabel('Average Accuracy')
        ax3.set_title('Model Performance Ranking\n(Average Across All Reversed Splits)', fontweight='bold')
        
        # Add value labels
        for bar, acc in zip(bars, model_avg.values):
            ax3.text(bar.get_width() + 0.002, bar.get_y() + bar.get_height()/2,
                    f'{acc:.3f}', ha='left', va='center', fontweight='bold')
        
        # 4. Split Performance Comparison
        ax4 = axes[1, 1]
        split_avg = self.df.groupby('Split')['Accuracy'].mean()
        split_std = self.df.groupby('Split')['Accuracy'].std()
        
        bars = ax4.bar(split_avg.index, split_avg.values, 
                      yerr=split_std.values, capsize=5,
                      color=['#FFB6C1', '#FFA07A', '#98FB98'])
        ax4.set_ylabel('Average Accuracy')
        ax4.set_title('Performance by Split Ratio\n(Error bars show std dev)', fontweight='bold')
        ax4.set_xticklabels(split_avg.index, rotation=45)
        
        # Add value labels
        for bar, acc in zip(bars, split_avg.values):
            ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
                    f'{acc:.3f}', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        
        # Save the plot
        filepath = os.path.join(self.results_dir, "visualizations", "overall_reversed_analysis.png")
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ Created overall reversed analysis visualization")
    
    def create_comparison_tables(self):
        """Create CSV and Excel comparison tables"""
        print("📋 Creating reversed split comparison tables...")
        
        # Summary table
        summary_df = self.df[['Split', 'Model', 'Accuracy', 'Train_Size', 'Test_Size', 
                             'Spam_Precision', 'Spam_Recall', 'Spam_F1_Score']].copy()
        summary_path = os.path.join(self.results_dir, "tables", "reversed_summary_results.csv")
        summary_df.to_csv(summary_path, index=False)
        
        # Detailed table
        detailed_path = os.path.join(self.results_dir, "tables", "reversed_detailed_results.csv")
        self.df.to_csv(detailed_path, index=False)
        
        # Excel workbook
        excel_path = os.path.join(self.results_dir, "tables", "reversed_complete_results.xlsx")
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            summary_df.to_excel(writer, sheet_name='Summary', index=False)
            self.df.to_excel(writer, sheet_name='Detailed_Metrics', index=False)
            
            # Split-specific sheets
            for split in self.df['Split_Code'].unique():
                split_data = self.df[self.df['Split_Code'] == split]
                sheet_name = split.replace('_REVERSED', '').replace('_', '_')
                split_data.to_excel(writer, sheet_name=sheet_name, index=False)
        
        print("✅ Created comparison tables:")
        print("   - reversed_summary_results.csv")
        print("   - reversed_detailed_results.csv") 
        print("   - reversed_complete_results.xlsx")
    
    def run_analysis(self):
        """Run complete reversed split analysis"""
        print("YOUTUBE SPAM CLASSIFICATION - REVERSED SPLIT RESULTS ANALYSIS")
        print("=" * 70)
        print("Analyzing results from SMALL training sets / LARGE test sets")
        print("=" * 70)
        
        self.create_reversed_split_visualizations()
        self.create_overall_comparison()
        self.create_comparison_tables()
        
        # Print summary
        print(f"\n📈 REVERSED SPLIT PERFORMANCE SUMMARY")
        print("-" * 50)
        
        for split in self.df['Split'].unique():
            split_data = self.df[self.df['Split'] == split]
            best_model = split_data.iloc[0]  # Already sorted by accuracy
            print(f"\n{split}:")
            print(f"  Best Model: {best_model['Model']} ({best_model['Accuracy']:.3f})")
            print(f"  Training Size: {best_model['Train_Size']}")
            print(f"  Test Size: {best_model['Test_Size']}")
            print(f"  Train/Test Ratio: {best_model['Train_Test_Ratio']:.2f}")
        
        print(f"\n🏆 BEST OVERALL (REVERSED SPLITS): {self.df.iloc[0]['Model']} - {self.df.iloc[0]['Split']}")
        print(f"   Accuracy: {self.df.iloc[0]['Accuracy']:.3f} ({self.df.iloc[0]['Accuracy']*100:.1f}%)")
        
        print(f"\n📁 All results saved to: {self.results_dir}")
        print("=" * 70)

def main():
    """Main function"""
    visualizer = ReversedResultsVisualizer()
    visualizer.run_analysis()

if __name__ == "__main__":
    main()
