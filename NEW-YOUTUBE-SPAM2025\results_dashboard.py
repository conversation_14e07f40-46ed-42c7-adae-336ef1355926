#!/usr/bin/env python3
"""
YouTube Spam Classification - Results Dashboard
===============================================

Interactive dashboard to view and analyze all training results.
Provides quick access to key metrics, visualizations, and comparisons.

Author: AI Assistant
Date: 2025-06-29
"""

import pandas as pd
import json
import os
from datetime import datetime

class ResultsDashboard:
    """Interactive dashboard for viewing training results"""
    
    def __init__(self):
        self.results_dir = "NEW-YOUTUBE-SPAM2025/results"
        self.dataset_dir = "NEW-YOUTUBE-SPAM2025/dataset"
        self.load_data()
    
    def load_data(self):
        """Load all results data"""
        # Load summary table
        summary_path = os.path.join(self.results_dir, "tables", "summary_results.csv")
        if os.path.exists(summary_path):
            self.summary_df = pd.read_csv(summary_path)
        else:
            print("❌ Summary results not found. Run visualize_results.py first.")
            return
        
        # Load detailed table
        detailed_path = os.path.join(self.results_dir, "tables", "detailed_results.csv")
        if os.path.exists(detailed_path):
            self.detailed_df = pd.read_csv(detailed_path)
        
        # Load training summary
        training_summary_path = os.path.join(self.dataset_dir, "training_summary.json")
        if os.path.exists(training_summary_path):
            with open(training_summary_path, 'r') as f:
                self.training_summary = json.load(f)
    
    def show_header(self):
        """Display dashboard header"""
        print("=" * 80)
        print("🎯 YOUTUBE SPAM CLASSIFICATION - RESULTS DASHBOARD")
        print("=" * 80)
        print(f"📅 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📊 Total Models: {len(self.summary_df)}")
        print(f"🏆 Best Accuracy: {self.summary_df['Accuracy'].max():.3f} ({self.summary_df['Accuracy'].max()*100:.1f}%)")
        print("=" * 80)
    
    def show_top_performers(self, n=5):
        """Show top performing models"""
        print(f"\n🏆 TOP {n} PERFORMING MODELS")
        print("-" * 50)
        
        top_models = self.summary_df.head(n)
        
        for i, (_, row) in enumerate(top_models.iterrows(), 1):
            print(f"{i}. {row['Model']} ({row['Split']})")
            print(f"   Accuracy: {row['Accuracy']:.3f} ({row['Accuracy']*100:.1f}%)")
            print(f"   Spam Precision: {row['Spam_Precision']:.3f}")
            print(f"   Spam Recall: {row['Spam_Recall']:.3f}")
            print(f"   Spam F1-Score: {row['Spam_F1_Score']:.3f}")
            print()
    
    def show_split_comparison(self):
        """Compare performance across different splits"""
        print("\n📊 PERFORMANCE BY TRAIN/TEST SPLIT")
        print("-" * 50)
        
        split_stats = self.summary_df.groupby('Split').agg({
            'Accuracy': ['mean', 'max', 'min', 'std'],
            'Spam_F1_Score': ['mean', 'max', 'min']
        }).round(3)
        
        for split in self.summary_df['Split'].unique():
            split_data = self.summary_df[self.summary_df['Split'] == split]
            best_model = split_data.loc[split_data['Accuracy'].idxmax()]
            
            print(f"\n{split}:")
            print(f"  Best Model: {best_model['Model']} ({best_model['Accuracy']:.3f})")
            print(f"  Average Accuracy: {split_data['Accuracy'].mean():.3f}")
            print(f"  Accuracy Range: {split_data['Accuracy'].min():.3f} - {split_data['Accuracy'].max():.3f}")
            print(f"  Models Trained: {len(split_data)}")
    
    def show_model_comparison(self):
        """Compare performance across different models"""
        print("\n🤖 PERFORMANCE BY MODEL TYPE")
        print("-" * 50)
        
        model_stats = self.summary_df.groupby('Model').agg({
            'Accuracy': ['mean', 'max', 'min', 'std'],
            'Spam_Precision': 'mean',
            'Spam_Recall': 'mean',
            'Spam_F1_Score': 'mean'
        }).round(3)
        
        # Sort by average accuracy
        model_avg = self.summary_df.groupby('Model')['Accuracy'].mean().sort_values(ascending=False)
        
        for i, (model, avg_acc) in enumerate(model_avg.items(), 1):
            model_data = self.summary_df[self.summary_df['Model'] == model]
            best_result = model_data.loc[model_data['Accuracy'].idxmax()]
            
            print(f"\n{i}. {model}:")
            print(f"   Average Accuracy: {avg_acc:.3f} ({avg_acc*100:.1f}%)")
            print(f"   Best Performance: {best_result['Accuracy']:.3f} ({best_result['Split']})")
            print(f"   Accuracy Range: {model_data['Accuracy'].min():.3f} - {model_data['Accuracy'].max():.3f}")
            print(f"   Avg Spam F1-Score: {model_data['Spam_F1_Score'].mean():.3f}")
    
    def show_confusion_matrix_summary(self):
        """Show confusion matrix insights from best model"""
        print("\n🎯 BEST MODEL DETAILED ANALYSIS")
        print("-" * 50)
        
        best_model = self.summary_df.iloc[0]  # Already sorted by accuracy
        print(f"Model: {best_model['Model']}")
        print(f"Split: {best_model['Split']}")
        print(f"Overall Accuracy: {best_model['Accuracy']:.3f} ({best_model['Accuracy']*100:.1f}%)")
        print()
        
        # Get detailed metrics for best model
        best_detailed = self.detailed_df[
            (self.detailed_df['Model'] == best_model['Model']) & 
            (self.detailed_df['Split'] == best_model['Split'])
        ].iloc[0]
        
        print("Class-wise Performance:")
        print(f"  Ham (Class 0):")
        print(f"    Precision: {best_detailed['Ham_Precision']:.3f}")
        print(f"    Recall: {best_detailed['Ham_Recall']:.3f}")
        print(f"    F1-Score: {best_detailed['Ham_F1_Score']:.3f}")
        print(f"    Support: {best_detailed['Ham_Support']}")
        print()
        print(f"  Spam (Class 1):")
        print(f"    Precision: {best_detailed['Spam_Precision']:.3f}")
        print(f"    Recall: {best_detailed['Spam_Recall']:.3f}")
        print(f"    F1-Score: {best_detailed['Spam_F1_Score']:.3f}")
        print(f"    Support: {best_detailed['Spam_Support']}")
        print()
        print("Overall Averages:")
        print(f"  Macro Average F1: {best_detailed['Macro_Avg_F1_Score']:.3f}")
        print(f"  Weighted Average F1: {best_detailed['Weighted_Avg_F1_Score']:.3f}")
    
    def show_file_locations(self):
        """Show where all result files are located"""
        print("\n📁 GENERATED FILES LOCATION")
        print("-" * 50)
        
        files_info = [
            ("Split-specific visualizations", "results/split_analysis/*.png"),
            ("Overall performance chart", "results/visualizations/overall_performance_analysis.png"),
            ("Summary results table", "results/tables/summary_results.csv"),
            ("Detailed results table", "results/tables/detailed_results.csv"),
            ("Excel workbook (all sheets)", "results/tables/complete_results.xlsx"),
            ("Step-by-step analysis", "results/step_by_step_analysis.txt"),
            ("Results documentation", "results/README_RESULTS.md"),
            ("Trained models (12 files)", "dataset/*_model.pkl"),
            ("Training reports", "dataset/training_*.txt|json")
        ]
        
        for description, location in files_info:
            print(f"  {description:30} → {location}")
    
    def show_recommendations(self):
        """Show usage recommendations"""
        print("\n💡 RECOMMENDATIONS")
        print("-" * 50)
        
        best_model = self.summary_df.iloc[0]
        
        print("For Production Deployment:")
        print(f"  ✅ Use: {best_model['Model']} with {best_model['Split']} split")
        print(f"  ✅ Expected Accuracy: {best_model['Accuracy']:.3f} ({best_model['Accuracy']*100:.1f}%)")
        print(f"  ✅ Model File: dataset/{best_model['Split'].lower().replace(' ', '_')}_{best_model['Model'].lower().replace(' ', '_')}_model.pkl")
        print()
        
        print("For Further Analysis:")
        print("  📊 View split_analysis/*.png for detailed visualizations")
        print("  📋 Import complete_results.xlsx for business reporting")
        print("  📝 Read step_by_step_analysis.txt for methodology")
        print()
        
        print("Performance Monitoring:")
        print(f"  🎯 Target Accuracy: ≥ {best_model['Accuracy']:.3f}")
        print(f"  🎯 Target Spam Precision: ≥ {best_model['Spam_Precision']:.3f}")
        print(f"  🎯 Target Spam Recall: ≥ {best_model['Spam_Recall']:.3f}")
    
    def run_dashboard(self):
        """Run the complete dashboard"""
        self.show_header()
        self.show_top_performers()
        self.show_split_comparison()
        self.show_model_comparison()
        self.show_confusion_matrix_summary()
        self.show_file_locations()
        self.show_recommendations()
        
        print("\n" + "=" * 80)
        print("🎉 DASHBOARD COMPLETE - All results successfully analyzed!")
        print("=" * 80)

def main():
    """Main function to run the dashboard"""
    dashboard = ResultsDashboard()
    dashboard.run_dashboard()

if __name__ == "__main__":
    main()
