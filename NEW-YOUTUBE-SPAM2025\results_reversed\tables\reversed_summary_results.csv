<PERSON>,<PERSON>,Accuracy,<PERSON>_<PERSON>ze,<PERSON>_<PERSON><PERSON>,<PERSON><PERSON>_Precision,<PERSON><PERSON>_<PERSON>call,Spam_F1_Score
Train 35 Test 65,Svm,0.9172749391727494,663,1233,0.9405772495755518,0.8921095008051529,0.915702479338843
Train 30 Test 70,Logistic Regression,0.9141566265060241,568,1328,0.9468599033816425,0.8789237668161435,0.9116279069767442
Train 30 Test 70,Svm,0.9118975903614458,568,1328,0.9353312302839116,0.8863976083707026,0.9102072141212586
Train 35 Test 65,Logistic Regression,0.910786699107867,663,1233,0.9443478260869566,0.8743961352657005,0.9080267558528428
Train 25 Test 75,Svm,0.9092827004219409,474,1422,0.9335302806499262,0.88268156424581,0.9073941134242641
Train 25 Test 75,Logis<PERSON> Regression,0.9050632911392406,474,1422,0.9355322338830585,0.8715083798882681,0.9023861171366594
Train 35 Test 65,Random Forest,0.8978102189781022,663,1233,0.9881656804733728,0.8067632850241546,0.8882978723404256
Train 30 Test 70,Random Forest,0.8900602409638554,568,1328,0.9887850467289719,0.7907324364723468,0.8787375415282392
Train 25 Test 75,Random Forest,0.8895921237693389,474,1422,0.9912126537785588,0.7877094972067039,0.8778210116731517
Train 35 Test 65,Naive Bayes,0.8775344687753447,663,1233,0.8476331360946746,0.9227053140096618,0.8835774865073246
Train 30 Test 70,Naive Bayes,0.875,568,1328,0.8449931412894376,0.9207772795216741,0.8812589413447782
Train 25 Test 75,Naive Bayes,0.8684950773558369,474,1422,0.8327044025157233,0.9245810055865922,0.8762409000661814
