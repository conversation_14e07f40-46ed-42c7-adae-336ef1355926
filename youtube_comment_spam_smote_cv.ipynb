{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import re\n", "from nltk.corpus import stopwords\n", "from nltk.stem import PorterStemmer, WordNetLemmatizer\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.naive_bayes import MultinomialNB\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.svm import SVC\n", "from sklearn.metrics import classification_report, accuracy_score, confusion_matrix, ConfusionMatrixDisplay, precision_score, recall_score, f1_score, roc_auc_score, roc_curve\n", "from sklearn.model_selection import train_test_split, GridSearchCV, cross_val_score\n", "from sklearn.utils import resample\n", "import joblib\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "from sklearn.preprocessing import label_binarize\n", "from sklearn.model_selection import learning_curve\n", "from imblearn.over_sampling import SMOTE"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Training Dataset"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training Data Size: 1956\n", "Training Data Distribution:\n", "CLASS\n", "1    1005\n", "0     951\n", "Name: count, dtype: int64\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "\n", "# Load your training dataset\n", "# Using the uploaded 'Youtube-Spam-Dataset.csv' with columns 'CONTENT' and 'CLASS'\n", "file_path = 'dataset/youtube_spam.csv'\n", "train_data = pd.read_csv(file_path)\n", "train_texts = train_data['CONTENT']\n", "train_labels = train_data['CLASS']\n", "\n", "# Display training data size and distribution\n", "print(f'Training Data Size: {len(train_data)}')\n", "print('Training Data Distribution:')\n", "print(train_labels.value_counts())\n", "\n", "# Plot the distribution of classes in the training dataset\n", "plt.figure(figsize=(6, 4))\n", "train_labels.value_counts().plot(kind='bar', color=['skyblue', 'lightgreen'])\n", "plt.xlabel('Class')\n", "plt.ylabel('Count')\n", "plt.title('Training Data Distribution')\n", "plt.xticks(rotation=0)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Preprocessing function for text cleaning\n", "def preprocess_text(text):\n", "    # Lowercase the text\n", "    text = text.lower()\n", "    # Remove special characters and numbers\n", "    text = re.sub(r'[^a-zA-Z\\s]', '', text)\n", "    # Remove stopwords\n", "    stop_words = set(stopwords.words('english'))\n", "    text = ' '.join([word for word in text.split() if word not in stop_words])\n", "    return text\n", "\n", "# Stemming and Lemmatization functions\n", "stemmer = PorterStemmer()\n", "lemmatizer = WordNetLemmatizer()\n", "\n", "def stem_and_lemmatize(text):\n", "    # Apply stemming and lemmatization\n", "    words = text.split()\n", "    words = [stemmer.stem(word) for word in words]\n", "    words = [lemmatizer.lemmatize(word) for word in words]\n", "    return ' '.join(words)\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Balanced Training Data Size: 1584\n", "Balanced Training Data Distribution:\n", "{0: 792, 1: 792}\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Best Parameters from GridSearch: {'alpha': 0.5}\n", "Training Accuracy with Best Parameters: 0.98\n", "Cross-validation Accuracy Scores: [0.87381703 0.85173502 0.89274448 0.85488959 0.90822785]\n", "Mean Cross-validation Accuracy: 0.88\n"]}], "source": ["# Apply preprocessing to the dataset\n", "train_texts = train_data['CONTENT'].apply(preprocess_text).apply(stem_and_lemmatize)\n", "train_labels = train_data['CLASS']\n", "\n", "# Convert the text data into TF-IDF feature vectors\n", "tfidf_vectorizer = TfidfVectorizer(stop_words='english', max_features=5000)\n", "X_train = tfidf_vectorizer.fit_transform(train_texts)\n", "y_train = train_labels\n", "\n", "# Split the data into training and validation sets before balancing\n", "X_train, X_val, y_train, y_val = train_test_split(X_train, y_train, test_size=0.2, random_state=42)\n", "\n", "# Balancing the training data using SMOTE\n", "smote = SMOTE(random_state=42)\n", "X_train, y_train = smote.fit_resample(X_train, y_train)\n", "\n", "# Display balanced training data size and distribution\n", "unique, counts = np.unique(y_train, return_counts=True)\n", "print(f'Balanced Training Data Size: {len(y_train)}')\n", "print('Balanced Training Data Distribution:')\n", "print(dict(zip(unique, counts)))\n", "\n", "# Plot the distribution of classes in the balanced training dataset after SMOTE\n", "plt.figure(figsize=(6, 4))\n", "plt.bar(['Not Spam (Class 0)', 'Spam (Class 1)'], counts, color=['skyblue', 'lightgreen'])\n", "plt.xlabel('Class')\n", "plt.ylabel('Count')\n", "plt.title('Balanced Training Data Distribution after SMOTE')\n", "plt.xticks(rotation=0)\n", "plt.show()\n", "\n", "# Define the model and apply GridSearch for hyperparameter tuning\n", "param_grid = {\n", "    'alpha': [0.1, 0.5, 1.0]\n", "}\n", "grid_search = GridSearchCV(MultinomialNB(), param_grid, cv=5, scoring='accuracy')\n", "grid_search.fit(X_train, y_train)\n", "\n", "# Get the best model from grid search\n", "best_model = grid_search.best_estimator_\n", "print(f'Best Parameters from GridSearch: {grid_search.best_params_}')\n", "\n", "# Display accuracy of the best model\n", "train_accuracy = accuracy_score(y_train, best_model.predict(X_train))\n", "print(f'Training Accuracy with Best Parameters: {train_accuracy:.2f}')\n", "\n", "# Cross-validation scores\n", "cv_scores = cross_val_score(best_model, X_train, y_train, cv=5, scoring='accuracy')\n", "print(f'Cross-validation Accuracy Scores: {cv_scores}')\n", "print(f'Mean Cross-validation Accuracy: {cv_scores.mean():.2f}')"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Learning Curve Visualization\n", "def plot_learning_curve(estimator, X, y, title=\"Learning Curves\", cv=5):\n", "    plt.figure(figsize=(10, 6))\n", "    train_sizes, train_scores, test_scores = learning_curve(estimator, X, y, cv=cv, n_jobs=-1, train_sizes=np.linspace(0.1, 1.0, 5))\n", "    train_scores_mean = np.mean(train_scores, axis=1)\n", "    test_scores_mean = np.mean(test_scores, axis=1)\n", "    \n", "    plt.plot(train_sizes, train_scores_mean, 'o-', color=\"r\", label=\"Training score\")\n", "    plt.plot(train_sizes, test_scores_mean, 'o-', color=\"g\", label=\"Cross-validation score\")\n", "    plt.xlabel(\"Training Examples\")\n", "    plt.ylabel(\"Score\")\n", "    plt.title(title)\n", "    plt.legend(loc=\"best\")\n", "    plt.grid()\n", "    plt.show()\n", "\n", "plot_learning_curve(best_model, X_train, y_train, title=\"Learning Curves for Naive Bayes Model\")\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["['tfidf_vectorizer.pkl']"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# Save the best trained model and vectorizer for baseline testing with a different dataset\n", "joblib.dump(best_model, 'spam_detection_model.pkl')\n", "joblib.dump(tfidf_vectorizer, 'tfidf_vectorizer.pkl')\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["# Load the new testing dataset for baseline testing\n", "# Using the uploaded 'youtube_comments_i6IOiUi6IYY.xlsx'\n", "test_file_path = 'youtube_comments_i6IOiUi6IYY.xlsx'\n", "new_test_data = pd.read_excel(test_file_path)\n", "new_test_texts = new_test_data['Comment']\n", "\n", "# Apply preprocessing to the new testing dataset\n", "new_test_texts = new_test_texts.apply(preprocess_text).apply(stem_and_lemmatize)\n", "\n", "# Transform the new test data\n", "X_new_test = tfidf_vectorizer.transform(new_test_texts)\n", "\n", "# Predict labels for the new testing dataset\n", "new_test_predictions = best_model.predict(X_new_test)\n", "new_test_data['Prediction'] = new_test_predictions\n", "# Save the predictions to an Excel file\n", "new_test_data.to_excel('new_test_predictions.xlsx', index=False)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Evaluating with test size: 0.2\n", "Accuracy: 1.00\n", "Precision: 1.00\n", "Recall: 1.00\n", "F1 Score: 1.00\n", "Confusion Matrix for Testing Dataset:\n", "[[75  0]\n", " [ 0 56]]\n"]}, {"data": {"image/png": "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******************************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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "# Evaluate model performance with different test sizes for comparison\n", "test_sizes = [0.2]\n", "results = []\n", "\n", "for test_size in test_sizes:\n", "    print(f'\\nEvaluating with test size: {test_size}')\n", "    \n", "    # Split the new test data into testing parts\n", "    X_train_new, X_test_new, y_train_new, y_test_new = train_test_split(X_new_test, new_test_data['Prediction'], test_size=test_size, random_state=42)\n", "    \n", "    # Predict using the best model on the split test set\n", "    y_test_predictions = best_model.predict(X_test_new)\n", "    y_test_probabilities = best_model.predict_proba(X_test_new)[:, 1]  # Probability for class 1 (Spam)\n", "    \n", "    # Evaluate the model performance\n", "    test_accuracy = accuracy_score(y_test_new, y_test_predictions)\n", "    test_precision = precision_score(y_test_new, y_test_predictions, average='weighted')\n", "    test_recall = recall_score(y_test_new, y_test_predictions, average='weighted')\n", "    test_f1 = f1_score(y_test_new, y_test_predictions, average='weighted')\n", "    \n", "    results.append({\n", "        'Test Size': test_size,\n", "        'Accuracy': test_accuracy,\n", "        'Precision': test_precision,\n", "        'Recall': test_recall,\n", "        'F1 Score': test_f1\n", "    })\n", "    \n", "    print(f'Accuracy: {test_accuracy:.2f}')\n", "    print(f'Precision: {test_precision:.2f}')\n", "    print(f'Recall: {test_recall:.2f}')\n", "    print(f'F1 Score: {test_f1:.2f}')\n", "    \n", "    # Confusion Matrix for the testing dataset\n", "    test_conf_matrix = confusion_matrix(y_test_new, y_test_predictions)\n", "    print('Confusion Matrix for Testing Dataset:')\n", "    print(test_conf_matrix)\n", "    \n", "    # Display Confusion Matrix for the testing dataset\n", "    disp_test = ConfusionMatrixDisplay(confusion_matrix=test_conf_matrix, display_labels=['Not Spam (Class 0)', 'Spam (Class 1)'])\n", "    disp_test.plot()\n", "    plt.title(f'Confusion Matrix for Test Size {test_size}')\n", "    plt.show()\n", "    \n", "    # ROC/AUC Curve for Testing Dataset\n", "    fpr, tpr, _ = roc_curve(y_test_new, y_test_probabilities)\n", "    plt.figure()\n", "    plt.plot(fpr, tpr, color='blue', lw=2, label='ROC curve (area = %0.2f)' % roc_auc_score(y_test_new, y_test_probabilities))\n", "    plt.plot([0, 1], [0, 1], color='gray', linestyle='--')\n", "    plt.xlim([0.0, 1.0])\n", "    plt.ylim([0.0, 1.05])\n", "    plt.xlabel('False Positive Rate')\n", "    plt.ylabel('True Positive Rate')\n", "    plt.title(f'Receiver Operating Characteristic (ROC) Curve for Test Size {test_size}')\n", "    plt.legend(loc='lower right')\n", "    plt.show()\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   Test Size  Accuracy  Precision  Recall  F1 Score\n", "0        0.2       1.0        1.0     1.0       1.0\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Display a summary comparison of different test sizes\n", "results_df = pd.DataFrame(results)\n", "print(results_df)\n", "\n", "# Visualization of the comparison\n", "plt.figure(figsize=(10, 6))\n", "plt.plot(results_df['Test Size'], results_df['Accuracy'], marker='o', label='Accuracy')\n", "plt.plot(results_df['Test Size'], results_df['Precision'], marker='o', label='Precision')\n", "plt.plot(results_df['Test Size'], results_df['Recall'], marker='o', label='Recall')\n", "plt.plot(results_df['Test Size'], results_df['F1 Score'], marker='o', label='F1 Score')\n", "plt.xlabel('Test Size')\n", "plt.ylabel('Score')\n", "plt.title('Model Performance Metrics for Different Test Sizes')\n", "plt.legend()\n", "plt.grid()\n", "plt.show()\n", "\n", "# Optionally, save the predictions to a CSV file\n", "new_test_data.to_csv('new_test_predictions.csv', index=False)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}