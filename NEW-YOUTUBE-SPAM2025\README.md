# YouTube Spam Classification - NEW-YOUTUBE-SPAM-2025

A comprehensive machine learning project for classifying YouTube comments as spam or ham (legitimate) using multiple algorithms and train/test splits.

## 📊 Project Overview

This project implements a complete machine learning pipeline for YouTube spam detection with:
- **Dataset**: 1,896 YouTube comments with spam/ham labels
- **Multiple Models**: Naive <PERSON>, Logistic Regression, Random Forest, SVM
- **Multiple Splits**: 75/25, 70/30, 65/35 train/test ratios
- **High Accuracy**: Up to 93% accuracy achieved

## 🎯 Results Summary

### Best Model Performance by Split:

| Split | Best Model | Accuracy |
|-------|------------|----------|
| 75/25 | SVM | 93.04% |
| 70/30 | Random Forest & SVM | 92.97% |
| 65/35 | SVM | 92.32% |

### All Model Results:

#### 75% Train / 25% Test Split
- **Naive Bayes**: 88.40%
- **Logistic Regression**: 92.41%
- **Random Forest**: 91.98%
- **SVM**: 93.04% ⭐

#### 70% Train / 30% Test Split
- **Naive <PERSON>**: 88.22%
- **Logistic Regression**: 92.27%
- **Random Forest**: 92.97% ⭐
- **SVM**: 92.97% ⭐

#### 65% Train / 35% Test Split
- **Naive Bayes**: 88.10%
- **Logistic Regression**: 91.42%
- **Random Forest**: 90.66%
- **SVM**: 92.32% ⭐

## 📁 Project Structure

```
NEW-YOUTUBE-SPAM2025/
├── dataset/                          # Trained models and results
│   ├── train_75_test_25_*.pkl        # Models for 75/25 split
│   ├── train_70_test_30_*.pkl        # Models for 70/30 split
│   ├── train_65_test_35_*.pkl        # Models for 65/35 split
│   ├── *_results.json               # Detailed results per split
│   ├── training_summary.json        # Complete training summary
│   └── training_report.txt          # Human-readable report
├── youtube_spam_ml_training.py       # Main training pipeline
├── model_predictor.py               # Prediction and demo script
├── requirements.txt                 # Python dependencies
└── README.md                        # This file
```

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Train Models (Already Done)
```bash
python youtube_spam_ml_training.py
```

### 3. Test Predictions
```bash
python model_predictor.py
```

## 🔧 Usage

### Making Predictions

```python
from model_predictor import SpamPredictor

# Initialize predictor
predictor = SpamPredictor()

# Predict single comment
result = predictor.predict_single("Check out my channel! Subscribe now!")
print(result)

# Predict multiple comments
comments = ["Great video!", "Subscribe to my channel!!!"]
results = predictor.predict_batch(comments)
```

### Loading Specific Models

```python
# Use specific model for prediction
result = predictor.predict_single(
    "This is spam content", 
    model_name="train_75_test_25_svm"
)
```

## 📈 Model Features

### Text Preprocessing
- Lowercase conversion
- URL removal
- Email address removal
- Special character cleaning
- Whitespace normalization

### Feature Engineering
- **TF-IDF Vectorization**: Max 5,000 features
- **N-grams**: Unigrams and bigrams (1,2)
- **Stop Words**: English stop words removed

### Models Implemented
1. **Naive Bayes** (MultinomialNB)
2. **Logistic Regression** (L2 regularization)
3. **Random Forest** (100 estimators)
4. **Support Vector Machine** (Linear kernel)

## 📊 Dataset Information

- **Source**: YouTube comments dataset
- **Total Samples**: 1,896 (after cleaning)
- **Features**: Comment text content
- **Target**: Binary classification (0=Ham, 1=Spam)
- **Class Distribution**: 
  - Spam (1): 955 samples (50.4%)
  - Ham (0): 941 samples (49.6%)

## 🎯 Model Evaluation

Each model is evaluated using:
- **Accuracy Score**
- **Classification Report** (Precision, Recall, F1-score)
- **Confusion Matrix**
- **Cross-validation** with stratified splits

## 📝 Files Generated

### Model Files (.pkl)
- 12 trained models (4 algorithms × 3 splits)
- Ready for production use
- Include preprocessing pipelines

### Result Files (.json)
- Detailed metrics for each model
- Training/test set sizes
- Performance comparisons

### Reports (.txt)
- Human-readable summary
- Training timestamps
- Quick performance overview

## 🔍 Example Predictions

```
Comment: "Check out my channel! Subscribe now! Free money!"
Prediction: SPAM (Confidence: 99.3%)

Comment: "This is a great video! Thanks for sharing."
Prediction: HAM (Confidence: 78.2%)

Comment: "I really enjoyed this content. Very informative."
Prediction: HAM (Confidence: 77.5%)
```

## 🛠️ Technical Details

- **Framework**: scikit-learn
- **Language**: Python 3.7+
- **Dependencies**: pandas, numpy, scikit-learn
- **Training Time**: ~2-3 minutes
- **Model Size**: ~1-5MB per model

## 📧 Contact

For questions or improvements, please refer to the training logs and result files in the `dataset/` directory.

---

**Generated**: 2025-06-29  
**Status**: ✅ Training Complete - All Models Ready for Use
