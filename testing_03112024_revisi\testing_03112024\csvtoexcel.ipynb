{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["<>:5: <PERSON><PERSON><PERSON>x<PERSON>arning: invalid escape sequence '\\!'\n", "<>:5: <PERSON><PERSON><PERSON>x<PERSON>arning: invalid escape sequence '\\!'\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_23244\\*********.py:5: SyntaxWarning: invalid escape sequence '\\!'\n", "  csv_directory = f'E:\\!!PYTHON2023\\github-youtube-sentiment\\testing_03112024\\awal\\csv_result'  # Replace with the path to your directory\n"]}, {"ename": "SyntaxError", "evalue": "(unicode error) 'unicodeescape' codec can't decode bytes in position 62-63: truncated \\xXX escape (*********.py, line 6)", "output_type": "error", "traceback": ["\u001b[1;36m  Cell \u001b[1;32mIn[2], line 6\u001b[1;36m\u001b[0m\n\u001b[1;33m    xlsx_directory = f'E:\\!!PYTHON2023\\github-youtube-sentiment\\testing_03112024\\awal\\xlsx_result'  # Replace with the path to save Excel files\u001b[0m\n\u001b[1;37m                                                                                                 ^\u001b[0m\n\u001b[1;31mSyntaxError\u001b[0m\u001b[1;31m:\u001b[0m (unicode error) 'unicodeescape' codec can't decode bytes in position 62-63: truncated \\xXX escape\n"]}], "source": ["import os\n", "import pandas as pd\n", "\n", "# Specify the directory containing CSV files\n", "# csv_directory = f'E:\\!!PYTHON2023\\github-youtube-sentiment\\testing_03112024\\awal\\csv_result'  # Replace with the path to your directory\n", "# xlsx_directory = f'E:\\!!PYTHON2023\\github-youtube-sentiment\\testing_03112024\\awal\\xlsx_result'  # Replace with the path to save Excel files\n", "csv_directory = 'E:\\\\!!PYTHON2023\\\\github-youtube-sentiment\\\\testing_03112024\\\\awal\\\\csv_result'\n", "xlsx_directory = 'E:\\\\!!PYTHON2023\\\\github-youtube-sentiment\\\\testing_03112024\\\\awal\\\\xlsx_result'\n", "\n", "# Create the output directory if it doesn't exist\n", "os.makedirs(xlsx_directory, exist_ok=True)\n", "\n", "# Iterate through all files in the specified directory\n", "for file_name in os.listdir(csv_directory):\n", "    if file_name.endswith('.csv'):  # Check if the file is a CSV\n", "        csv_path = os.path.join(csv_directory, file_name)\n", "        xlsx_path = os.path.join(xlsx_directory, file_name.replace('.csv', '.xlsx'))\n", "        \n", "        # Load CSV and save as Excel\n", "        try:\n", "            data = pd.read_csv(csv_path)\n", "            data.to_excel(xlsx_path, index=False)\n", "            print(f\"Converted: {file_name} -> {os.path.basename(xlsx_path)}\")\n", "        except Exception as e:\n", "            print(f\"Failed to convert {file_name}: {e}\")\n", "\n", "print(f\"All CSV files in {csv_directory} have been processed.\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}