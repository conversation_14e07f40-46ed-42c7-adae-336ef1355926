{"cells": [{"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[nltk_data] Downloading package punkt to\n", "[nltk_data]     C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package stopwords to\n", "[nltk_data]     C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data...\n", "[nltk_data]   Package stopwords is already up-to-date!\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["# Importing necessary libraries\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score\n", "from sklearn.naive_bayes import MultinomialNB\n", "import joblib  # For saving/loading the model\n", "import nltk\n", "from nltk.corpus import stopwords\n", "from nltk.tokenize import word_tokenize\n", "import string\n", "\n", "# Downloading stopwords\n", "nltk.download('punkt')\n", "nltk.download('stopwords')"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [], "source": ["import os\n", "# Create directory if not exists\n", "output_folder = 'finetuning'\n", "os.makedirs(output_folder, exist_ok=True)"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training Data: 1956 samples\n"]}], "source": ["\n", "\n", "# Load training dataset (A.csv) containing 'comments' and 'label'\n", "train_data = pd.read_csv('../dataset/youtube_spam.csv')  # A.csv must contain 'comments' and 'label' columns\n", "print(f\"Training Data: {train_data.shape[0]} samples\")\n"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Testing Data: 653 samples\n"]}], "source": ["\n", "# Load testing dataset (B.csv) containing only 'comments'\n", "test_data = pd.read_excel('../dataset/youtube_comments_i6IOiUi6IYY.xlsx')  # B.csv must contain only 'comments' column\n", "print(f\"Testing Data: {test_data.shape[0]} samples\")"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [], "source": ["\n", "# Preprocessing Function\n", "def preprocess(text):\n", "    \"\"\"\n", "    Preprocess the input text by tokenizing, removing stopwords and punctuation,\n", "    and converting the text to lowercase.\n", "    \"\"\"\n", "    # Convert to lowercase\n", "    text = text.lower()\n", "    \n", "    # Tokenize the text\n", "    tokens = word_tokenize(text)\n", "    \n", "    # Remove punctuation and stopwords\n", "    tokens = [word for word in tokens if word not in string.punctuation and word not in stopwords.words('english')]\n", "    \n", "    return ' '.join(tokens)\n"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training Data: 1956 samples\n", "Testing Data: 653 samples\n", "Columns in training data: Index(['COMMENT_ID', 'AUTHOR', 'DATE', 'CONTENT', 'VIDEO_NAME', 'CLASS'], dtype='object')\n", "Columns in testing data: Index(['Author', 'Timestamp', 'Likes', 'Comment'], dtype='object')\n", "Features (X_train) shape: (1956, 4375)\n", "Labels (y_train) shape: (1956,)\n", "Predicted Labels for Test Data:\n", "[0 0 0 0 1 1 0 0 0 0 1 0 1 1 0 1 0 1 1 0 0 0 1 0 1 0 0 1 0 0 1 0 0 0 0 0 0\n", " 0 1 1 0 1 1 1 1 0 1 1 1 1 0 0 1 0 1 0 1 0 0 1 1 1 0 1 0 1 1 0 1 0 1 0 0 0\n", " 1 1 1 1 0 0 0 1 1 0 0 0 1 0 0 1 0 0 0 0 1 0 0 1 0 0 1 0 0 0 0 0 1 0 1 1 0\n", " 1 1 1 0 1 0 1 1 1 0 1 1 1 0 0 0 0 1 1 0 0 0 1 1 1 1 1 0 0 1 0 1 1 0 1 0 1\n", " 1 0 1 0 1 0 0 0 1 1 1 1 1 1 1 0 1 0 1 1 1 1 0 1 0 0 1 1 1 0 1 1 0 1 0 1 1\n", " 0 1 1 0 0 0 0 1 0 1 1 0 1 1 1 1 1 0 0 0 0 1 0 1 1 0 1 0 0 0 1 1 1 1 0 0 0\n", " 1 0 0 0 0 0 0 0 1 0 1 0 1 0 1 0 1 1 0 1 1 1 0 1 1 1 0 0 1 1 1 0 1 0 0 1 1\n", " 1 1 1 1 1 1 1 0 1 0 1 1 1 0 0 0 0 1 1 1 0 0 1 1 1 0 1 0 0 1 1 1 0 0 0 0 0\n", " 0 0 1 1 0 1 1 0 1 1 1 1 0 0 1 0 0 0 1 1 1 1 0 1 1 0 0 0 0 1 1 1 0 0 0 0 0\n", " 1 1 1 1 1 1 1 1 0 1 0 1 1 0 0 1 0 1 1 1 0 1 0 1 0 1 0 0 1 1 0 1 1 1 0 1 0\n", " 1 0 0 1 0 1 1 0 1 1 1 1 1 0 1 0 1 0 1 0 1 1 0 1 1 1 1 1 1 1 0 1 1 1 1 1 0\n", " 1 0 1 0 0 0 0 1 0 0 1 1 0 0 0 1 1 0 1 1 1 1 0 0 0 0 1 0 0 1 1 1 1 1 1 0 0\n", " 0 1 0 0 1 1 0 1 1 1 1 0 1 0 1 1 1 1 0 0 0 0 0 1 0 0 1 1 1 0 1 0 0 1 1 1 1\n", " 0 1 0 0 1 1 1 1 0 1 0 1 1 0 1 1 1 1 0 1 1 1 1 1 1 1 0 1 1 1 1 1 0 1 1 0 1\n", " 1 1 1 0 1 1 1 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 0 1 0 1 1 1 0 0 1 1 0 1 1 1 0\n", " 1 1 0 1 1 0 1 1 0 0 1 1 1 1 1 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 1 1 0\n", " 1 1 1 1 1 0 1 0 1 0 1 0 1 1 0 1 1 1 1 1 1 1 1 0 0 0 1 1 1 1 0 1 0 1 1 1 1\n", " 1 0 0 1 0 0 1 0 1 1 1 1 1 1 1 1 1 1 0 1 1 0 1 1]\n"]}], "source": ["# Load training dataset (A.csv) containing 'comments' and 'label'\n", "train_data = pd.read_csv('../dataset/youtube_spam.csv')  # A.csv must contain 'CONTENT' and 'CLASS' columns\n", "print(f\"Training Data: {train_data.shape[0]} samples\")\n", "\n", "# Load testing dataset (B.xlsx) containing only 'comments'\n", "test_data = pd.read_excel('../dataset/youtube_comments_i6IOiUi6IYY.xlsx')  # B.xlsx must contain 'Comment' column\n", "print(f\"Testing Data: {test_data.shape[0]} samples\")\n", "\n", "# Check columns of both datasets to verify names\n", "print(f\"Columns in training data: {train_data.columns}\")\n", "print(f\"Columns in testing data: {test_data.columns}\")\n", "\n", "# Preprocessing Function\n", "def preprocess(text):\n", "    \"\"\"\n", "    Preprocess the input text by tokenizing, removing stopwords and punctuation,\n", "    and converting the text to lowercase.\n", "    \"\"\"\n", "    # Convert to lowercase\n", "    text = text.lower()\n", "    \n", "    # Tokenize the text\n", "    tokens = word_tokenize(text)\n", "    \n", "    # Remove punctuation and stopwords\n", "    tokens = [word for word in tokens if word not in string.punctuation and word not in stopwords.words('english')]\n", "    \n", "    return ' '.join(tokens)\n", "\n", "# Apply preprocessing to both training and testing datasets\n", "train_data['CONTENT'] = train_data['CONTENT'].apply(preprocess)\n", "test_data['Comment'] = test_data['Comment'].apply(preprocess)\n", "\n", "# Vectorization using TF-IDF\n", "vectorizer = TfidfVectorizer(max_features=5000)\n", "\n", "# Training Phase\n", "# Apply vectorization to the 'CONTENT' column and labels from 'CLASS'\n", "X_train = vectorizer.fit_transform(train_data['CONTENT'])  # Fit and transform the 'CONTENT' column\n", "y_train = train_data['CLASS']  # Labels for training (ensure 'CLASS' is the correct label column)\n", "\n", "# Check the shape of the features and labels for training\n", "print(f\"Features (X_train) shape: {X_train.shape}\")\n", "print(f\"Labels (y_train) shape: {y_train.shape}\")\n", "\n", "# Model training using <PERSON><PERSON>\n", "model = MultinomialNB()\n", "model.fit(X_train, y_train)\n", "\n", "# Save the trained model and vectorizer\n", "joblib.dump(model, 'text_classification_model.pkl')  # Save model\n", "joblib.dump(vectorizer, 'vectorizer.pkl')  # Save vectorizer\n", "\n", "# Testing Phase\n", "# Load the saved model and vectorizer\n", "model = joblib.load('text_classification_model.pkl')\n", "vectorizer = joblib.load('vectorizer.pkl')\n", "\n", "# Prepare test data (without labels)\n", "X_test = vectorizer.transform(test_data['Comment'])  # Only transform the test data\n", "\n", "# Since we don't have labels for testing, we'll predict the categories\n", "y_pred = model.predict(X_test)\n", "\n", "# If ground truth labels are available for evaluation (uncomment if you have labels)\n", "# y_test = test_data['label']  # Uncomment if you have labels in the test dataset\n", "# accuracy = accuracy_score(y_test, y_pred)\n", "# precision = precision_score(y_test, y_pred, average='weighted')\n", "# recall = recall_score(y_test, y_pred, average='weighted')\n", "# f1 = f1_score(y_test, y_pred, average='weighted')\n", "# roc_auc = roc_auc_score(y_test, model.predict_proba(X_test), multi_class='ovr')\n", "\n", "# Since labels are not available in test data, we can't compute these metrics directly\n", "print(\"Predicted Labels for Test Data:\")\n", "print(y_pred)\n"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [], "source": ["from sklearn.metrics import roc_auc_score\n"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Predicted Labels for Test Data:\n", "[0 0 0 0 1 1 0 0 0 0 1 0 1 1 0 1 0 1 1 0 0 0 1 0 1 0 0 1 0 0 1 0 0 0 0 0 0\n", " 0 1 1 0 1 1 1 1 0 1 1 1 1 0 0 1 0 1 0 1 0 0 1 1 1 0 1 0 1 1 0 1 0 1 0 0 0\n", " 1 1 1 1 0 0 0 1 1 0 0 0 1 0 0 1 0 0 0 0 1 0 0 1 0 0 1 0 0 0 0 0 1 0 1 1 0\n", " 1 1 1 0 1 0 1 1 1 0 1 1 1 0 0 0 0 1 1 0 0 0 1 1 1 1 1 0 0 1 0 1 1 0 1 0 1\n", " 1 0 1 0 1 0 0 0 1 1 1 1 1 1 1 0 1 0 1 1 1 1 0 1 0 0 1 1 1 0 1 1 0 1 0 1 1\n", " 0 1 1 0 0 0 0 1 0 1 1 0 1 1 1 1 1 0 0 0 0 1 0 1 1 0 1 0 0 0 1 1 1 1 0 0 0\n", " 1 0 0 0 0 0 0 0 1 0 1 0 1 0 1 0 1 1 0 1 1 1 0 1 1 1 0 0 1 1 1 0 1 0 0 1 1\n", " 1 1 1 1 1 1 1 0 1 0 1 1 1 0 0 0 0 1 1 1 0 0 1 1 1 0 1 0 0 1 1 1 0 0 0 0 0\n", " 0 0 1 1 0 1 1 0 1 1 1 1 0 0 1 0 0 0 1 1 1 1 0 1 1 0 0 0 0 1 1 1 0 0 0 0 0\n", " 1 1 1 1 1 1 1 1 0 1 0 1 1 0 0 1 0 1 1 1 0 1 0 1 0 1 0 0 1 1 0 1 1 1 0 1 0\n", " 1 0 0 1 0 1 1 0 1 1 1 1 1 0 1 0 1 0 1 0 1 1 0 1 1 1 1 1 1 1 0 1 1 1 1 1 0\n", " 1 0 1 0 0 0 0 1 0 0 1 1 0 0 0 1 1 0 1 1 1 1 0 0 0 0 1 0 0 1 1 1 1 1 1 0 0\n", " 0 1 0 0 1 1 0 1 1 1 1 0 1 0 1 1 1 1 0 0 0 0 0 1 0 0 1 1 1 0 1 0 0 1 1 1 1\n", " 0 1 0 0 1 1 1 1 0 1 0 1 1 0 1 1 1 1 0 1 1 1 1 1 1 1 0 1 1 1 1 1 0 1 1 0 1\n", " 1 1 1 0 1 1 1 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 0 1 0 1 1 1 0 0 1 1 0 1 1 1 0\n", " 1 1 0 1 1 0 1 1 0 0 1 1 1 1 1 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 1 1 0\n", " 1 1 1 1 1 0 1 0 1 0 1 0 1 1 0 1 1 1 1 1 1 1 1 0 0 0 1 1 1 1 0 1 0 1 1 1 1\n", " 1 0 0 1 0 0 1 0 1 1 1 1 1 1 1 1 1 1 0 1 1 0 1 1]\n"]}], "source": ["# Since labels are not available in test data, we can't compute these metrics directly\n", "print(\"Predicted Labels for Test Data:\")\n", "print(y_pred)\n"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Testing with Test Size: 0.2\n", "Original y_test_split shape: (392,)\n", "y_test_split is already 1D with shape: (392,)\n", "Flattened y_test_split shape: (392,)\n", "Shape of y_test_split: (392,)\n", "Shape of model.predict_proba(X_test_split_vec): (392, 2)\n", "Error in ROC AUC calculation: y should be a 1d array, got an array of shape (392, 2) instead.\n", "Check the shapes of y_test_split: (392,) and model.predict_proba(X_test_split_vec): (392, 2)\n", "Accuracy: 0.9235\n", "Precision: 0.9257\n", "Recall: 0.9235\n", "F1 Score: 0.9230\n", "ROC AUC: 0.0000\n", "\n", "Testing with Test Size: 0.25\n", "Original y_test_split shape: (489,)\n", "y_test_split is already 1D with shape: (489,)\n", "Flattened y_test_split shape: (489,)\n", "Shape of y_test_split: (489,)\n", "Shape of model.predict_proba(X_test_split_vec): (489, 2)\n", "Error in ROC AUC calculation: y should be a 1d array, got an array of shape (489, 2) instead.\n", "Check the shapes of y_test_split: (489,) and model.predict_proba(X_test_split_vec): (489, 2)\n", "Accuracy: 0.9141\n", "Precision: 0.9158\n", "Recall: 0.9141\n", "F1 Score: 0.9137\n", "ROC AUC: 0.0000\n", "\n", "Testing with Test Size: 0.3\n", "Original y_test_split shape: (587,)\n", "y_test_split is already 1D with shape: (587,)\n", "Flattened y_test_split shape: (587,)\n", "Shape of y_test_split: (587,)\n", "Shape of model.predict_proba(X_test_split_vec): (587, 2)\n", "Error in ROC AUC calculation: y should be a 1d array, got an array of shape (587, 2) instead.\n", "Check the shapes of y_test_split: (587,) and model.predict_proba(X_test_split_vec): (587, 2)\n", "Accuracy: 0.9131\n", "Precision: 0.9153\n", "Recall: 0.9131\n", "F1 Score: 0.9128\n", "ROC AUC: 0.0000\n", "\n", "Testing with Test Size: 0.35\n", "Original y_test_split shape: (685,)\n", "y_test_split is already 1D with shape: (685,)\n", "Flattened y_test_split shape: (685,)\n", "Shape of y_test_split: (685,)\n", "Shape of model.predict_proba(X_test_split_vec): (685, 2)\n", "Error in ROC AUC calculation: y should be a 1d array, got an array of shape (685, 2) instead.\n", "Check the shapes of y_test_split: (685,) and model.predict_proba(X_test_split_vec): (685, 2)\n", "Accuracy: 0.9095\n", "Precision: 0.9119\n", "Recall: 0.9095\n", "F1 Score: 0.9091\n", "ROC AUC: 0.0000\n"]}], "source": ["\n", "# Optionally: Experiment with different test sizes\n", "test_sizes = [0.2, 0.25, 0.3, 0.35]\n", "for size in test_sizes:\n", "    print(f\"\\nTesting with Test Size: {size}\")\n", "    \n", "    # Train-Test Split with different test sizes\n", "    X_train_split, X_test_split, y_train_split, y_test_split = train_test_split(\n", "        train_data['CONTENT'], y_train, test_size=size, random_state=42)\n", "    \n", "    # Vectorization\n", "    X_train_split_vec = vectorizer.fit_transform(X_train_split)\n", "    X_test_split_vec = vectorizer.transform(X_test_split)\n", "    \n", "    # Model Training and Prediction\n", "    model.fit(X_train_split_vec, y_train_split)\n", "    y_pred_split = model.predict(X_test_split_vec)\n", "    \n", "    # Evaluation (if y_test_split is available)\n", "    accuracy_split = accuracy_score(y_test_split, y_pred_split)\n", "    precision_split = precision_score(y_test_split, y_pred_split, average='weighted')\n", "    recall_split = recall_score(y_test_split, y_pred_split, average='weighted')\n", "    f1_split = f1_score(y_test_split, y_pred_split, average='weighted')\n", "    \n", "    # Before calculating ROC AUC, check if y_test_split is one-hot encoded\n", "    print(f\"Original y_test_split shape: {y_test_split.shape}\")\n", "    \n", "    # Convert one-hot encoded y_test_split to class labels if necessary\n", "    if len(y_test_split.shape) > 1 and y_test_split.shape[1] > 1:\n", "        y_test_split = np.argmax(y_test_split, axis=1)  # Convert one-hot to class labels\n", "        print(f\"Converted y_test_split shape: {y_test_split.shape}\")\n", "    else:\n", "        print(f\"y_test_split is already 1D with shape: {y_test_split.shape}\")\n", "    \n", "    # Flatten y_test_split to ensure it's 1D\n", "    y_test_split = np.ravel(y_test_split)  # Flatten to ensure it is 1D\n", "    print(f\"Flattened y_test_split shape: {y_test_split.shape}\")\n", "    \n", "    # Before calculating ROC AUC, print the shapes of the inputs to verify they match\n", "    print(f\"Shape of y_test_split: {y_test_split.shape}\")\n", "    print(f\"Shape of model.predict_proba(X_test_split_vec): {model.predict_proba(X_test_split_vec).shape}\")\n", "    # Initialize roc_auc_split variable\n", "    roc_auc_split = 0\n", "    # ROC AUC: Ensure that predict_proba output is a 2D array of probabilities\n", "    try:\n", "        roc_auc_split = roc_auc_score(y_test_split, model.predict_proba(X_test_split_vec), multi_class='ovr')\n", "        print(f\"ROC AUC: {roc_auc_split:.4f}\")\n", "    except ValueError as e:\n", "        print(f\"Error in ROC AUC calculation: {e}\")\n", "        # Optionally, you can check the shapes again if the error message doesn't provide enough details\n", "        print(f\"Check the shapes of y_test_split: {y_test_split.shape} and model.predict_proba(X_test_split_vec): {model.predict_proba(X_test_split_vec).shape}\")\n", "        \n", "    # Print the evaluation metrics\n", "    print(f\"Accuracy: {accuracy_split:.4f}\")\n", "    print(f\"Precision: {precision_split:.4f}\")\n", "    print(f\"Recall: {recall_split:.4f}\")\n", "    print(f\"F1 Score: {f1_split:.4f}\")\n", "    print(f\"ROC AUC: {roc_auc_split:.4f}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}