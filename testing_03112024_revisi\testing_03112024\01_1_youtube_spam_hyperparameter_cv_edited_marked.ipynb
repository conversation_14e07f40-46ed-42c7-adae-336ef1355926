{"cells": [{"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import os\n", "import re\n", "import joblib\n", "import xgboost as xgb\n", "from joblib import Parallel, delayed\n", "from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold, RandomizedSearchCV\n", "from sklearn.naive_bayes import GaussianNB\n", "from sklearn.svm import SVC\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import classification_report, accuracy_score, confusion_matrix, recall_score, precision_score, f1_score\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.feature_extraction.text import ENGLISH_STOP_WORDS\n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["\n", "# Create directory if not exists\n", "output_folder = 'finetuning'\n", "os.makedirs(output_folder, exist_ok=True)\n", "\n", "# Function to preprocess text data\n", "def preprocess_text(text):\n", "    # Remove URLs, special characters, numbers, and make lowercase\n", "    text = re.sub(r\"http\\S+|www\\S+|https\\S+\", '', text, flags=re.MULTILINE)\n", "    text = re.sub(r'[^A-Za-z\\s]', '', text)\n", "    text = text.lower()\n", "    \n", "    # Tokenize and remove stopwords\n", "    tokens = text.split()\n", "    cleaned_tokens = [word for word in tokens if word not in ENGLISH_STOP_WORDS]\n", "    \n", "    # Join tokens back to string\n", "    cleaned_text = ' '.join(cleaned_tokens)\n", "    return cleaned_text\n", "\n", "# Function to load and preprocess training dataset\n", "def load_training_data():\n", "    # Load training dataset\n", "    train_val_dataset = pd.read_csv('../dataset/youtube_spam.csv')  # Load the uploaded training and validation dataset\n", "\n", "    # Preprocess 'CONTENT' column\n", "    train_val_dataset['clean_content'] = train_val_dataset['CONTENT'].apply(preprocess_text)\n", "    \n", "    # Use only 'CONTENT' as features and 'CLASS' as the target variable\n", "    X_train_val_raw = train_val_dataset['clean_content']  # Features (content of the comments)\n", "    y_train_val = train_val_dataset['CLASS']        # Target variable (spam or not spam)\n", "\n", "    return X_train_val_raw, y_train_val\n", "\n", "# Function to perform feature extraction using TF-IDF\n", "def feature_extraction(X_train_val_raw):\n", "    # Convert text data to numerical data using TF-IDF Vectorizer\n", "    tfidf = TfidfVectorizer(stop_words='english', max_features=5000)\n", "    X_train_val = tfidf.fit_transform(X_train_val_raw)\n", "    return X_train_val, tfidf"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["\n", "\n", "\n", "# Function to load and preprocess testing dataset\n", "def load_testing_data(tfidf):\n", "    # Load and preprocess the testing dataset\n", "    test_dataset = pd.read_excel('../dataset/youtube_comments_i6IOiUi6IYY.xlsx')  # Load the testing dataset\n", "    test_dataset['cleaned_comment'] = test_dataset['Comment'].apply(preprocess_text)  # Preprocess 'Comment' column\n", "    X_test_raw = test_dataset['cleaned_comment']  # Use the cleaned comments for testing\n", "    X_test = tfidf.transform(X_test_raw)  # Transform the test dataset using the same TF-IDF Vectorizer\n", "\n", "    # Add original and cleaned comments to dataframe for analysis\n", "    df = pd.DataFrame({'comment': test_dataset['Comment'], 'clean_comment': test_dataset['cleaned_comment']})\n", "\n", "    # Save the processed test dataset to CSV and Excel\n", "    test_dataset.to_csv(f'{output_folder}/processed_test_dataset.csv', index=False)\n", "    test_dataset.to_excel(f'{output_folder}/processed_test_dataset.xlsx', index=False)\n", "    print(f\"Processed test dataset saved to '{output_folder}/processed_test_dataset.csv' and '{output_folder}/processed_test_dataset.xlsx'\")\n", "\n", "    # return X_test\n", "    return X_test, df\n", "  \n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["# Function to plot confusion matrix\n", "def plot_confusion_matrix(y_true, y_pred, model_name, phase, test_size):\n", "    cm = confusion_matrix(y_true, y_pred)\n", "    plt.figure(figsize=(8, 6))\n", "    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', xticklabels=np.unique(y_true), yticklabels=np.unique(y_true))\n", "    plt.xlabel('Predicted')\n", "    plt.ylabel('Actual')\n", "    plt.title(f'Confusion Matrix for {model_name} ({phase})')\n", "    plt.savefig(f'{output_folder}/confusion_matrix_{model_name}_{phase.lower()}_test_size_{test_size}.png')\n", "    plt.close()\n", "\n", "# Function to plot validation results\n", "def plot_validation_results(validation_results, test_size):\n", "    model_names, accuracies = zip(*validation_results)\n", "    plt.figure(figsize=(10, 5))\n", "    plt.bar(model_names, accuracies, color='skyblue')\n", "    plt.xlabel('Model')\n", "    plt.ylabel('Validation Accuracy')\n", "    plt.title(f'Validation Accuracy for Different Models (test_size={test_size})')\n", "    plt.xticks(rotation=45)\n", "    plt.tight_layout()\n", "    plt.savefig(f'{output_folder}/validation_accuracy_test_size_{test_size}.png')\n", "    plt.close()"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["# Function to initialize models\n", "def initialize_models():\n", "    return {\n", "        'NaiveBayes': GaussianNB(),\n", "        'SVM (Linear)': SVC(kernel='linear'),\n", "        'SVM (Polynomial)': SVC(kernel='poly'),\n", "        'SVM (RBF)': SVC(kernel='rbf'),\n", "        'SVM (Sigmoid)': SVC(kernel='sigmoid'),\n", "        'RandomForest': RandomForestClassifier(n_estimators=100, random_state=42),\n", "        'LogisticRegression': LogisticRegression(max_iter=1000, random_state=42),\n", "        # 'XGBoost': xgb.XGBClassifier(use_label_encoder=False, eval_metric='mlogloss', random_state=42)\n", "        'XGBoost': xgb.XGBClassifier(use_label_encoder=False, eval_metric='mlogloss', random_state=42)\n", "\n", "    }\n", "\n", "# Function to perform randomized search cross-validation\n", "def randomized_search_cv(model, X_train, y_train, param_distributions, cv_folds=5, n_iter=10):\n", "    skf = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)\n", "    random_search = RandomizedSearchCV(model, param_distributions, n_iter=n_iter, cv=skf, scoring='accuracy', n_jobs=-1, random_state=42)\n", "    random_search.fit(X_train, y_train)\n", "    print(\"Best parameters found:\", random_search.best_params_)\n", "    print(\"Best cross-validation accuracy:\", random_search.best_score_)\n", "    return random_search.best_estimator_\n"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["\n", "# Function to train and evaluate a single model\n", "def train_and_evaluate_model(model_name, model, X_train, y_train, X_val, y_val, test_size):\n", "    # Define hyperparameter search space for RandomizedSearchCV\n", "    param_distributions = {\n", "        'RandomForest': {\n", "            'n_estimators': [50, 100, 150],\n", "            'max_depth': [None, 10, 20],\n", "            'min_samples_split': [2, 5, 10]\n", "        },\n", "        'SVM': {\n", "            'C': [0.1, 1, 10, 100],\n", "            'kernel': ['linear', 'poly', 'rbf', 'sigmoid'],\n", "            'gamma': ['scale', 'auto']\n", "        },\n", "        'LogisticRegression': {\n", "            'C': [0.1, 1, 10, 100],\n", "            'solver': ['lbfgs', 'saga']\n", "        },\n", "        'XGBoost': {\n", "            'n_estimators': [50, 100, 150],\n", "            'learning_rate': [0.01, 0.1, 0.2],\n", "            'max_depth': [3, 6, 10]\n", "        }\n", "    }\n", "    \n", "    # Apply randomized search cross-validation if hyperparameters are defined for the model\n", "    if model_name in param_distributions:\n", "        model = randomized_search_cv(model, X_train, y_train, param_distributions[model_name])\n", "    \n", "    # Train the model on the entire training set after cross-validation\n", "    # model.fit(X_train.toarray(), y_train)\n", "    \n", "    # Train the model on the entire training set after cross-validation\n", "    # if model_name == 'XGBoost':\n", "    #     X_train_split, X_val_split, y_train_split, y_val_split = train_test_split(X_train, y_train, test_size=0.2, random_state=42)\n", "    #     model.fit(\n", "    #         X_train_split.toarray(),\n", "    #         y_train_split,\n", "    #         early_stopping_rounds=10,\n", "    #         eval_set=[(X_val_split.toarray(), y_val_split)],\n", "    #         verbose=True\n", "    #     )\n", "    # else:\n", "    #     model.fit(X_train.toarray(), y_train)\n", "    # Train the model on the entire training set after cross-validation\n", "    if model_name == 'XGBoost':\n", "        X_train_split, X_val_split, y_train_split, y_val_split = train_test_split(X_train, y_train, test_size=0.2, random_state=42)\n", "        # Edited: Updated eval_set to use X_val_split and y_val_split directly for consistency and to provide proper validation dataset\n", "        model.fit(\n", "            X_train_split.toarray(),\n", "            y_train_split,\n", "            early_stopping_rounds=10,\n", "            # eval_set=[(X_val_split.toarray(), y_val_split)],\n", "            eval_set=[(X_val, y_val)],\n", "   \n", "            verbose=True\n", "        )\n", "    else:\n", "        model.fit(X_train.toarray(), y_train)   \n", "    # Save the trained model\n", "    model_filename = f'{output_folder}/{model_name.replace(\" \", \"_\")}_model.pkl'\n", "    joblib.dump(model, model_filename)\n", "    print(f\"Model {model_name} saved to {model_filename}\")\n", "\n", "    # Validate the model\n", "    y_val_pred = model.predict(X_val.toarray())\n", "    accuracy = accuracy_score(y_val, y_val_pred)\n", "    recall = recall_score(y_val, y_val_pred, average='weighted')\n", "    precision = precision_score(y_val, y_val_pred, average='weighted')\n", "    f1 = f1_score(y_val, y_val_pred, average='weighted')\n", "\n", "    # Display metrics\n", "    print(f\"Validation Accuracy: {accuracy:.4f}\")\n", "    print(f\"Recall: {recall:.4f}, Precision: {precision:.4f}, F1 Score: {f1:.4f}\\n\")\n", "\n", "    # Confusion Matrix Visualization\n", "    plot_confusion_matrix(y_val, y_val_pred, model_name, 'Validation', test_size)\n", "\n", "    # Return metrics results\n", "    return [(model_name, 'Validation', accuracy, recall, precision, f1)], [(model_name, accuracy)]\n"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["\n", "# Function to train and validate models using cross-validation\n", "def train_and_validate_models(models, X_train, X_val, y_train, y_val, test_size):\n", "    metrics_results = []\n", "    validation_results = []\n", "\n", "    print(\"\\nTraining and Validation Phase\\n\")\n", "    results = Parallel(n_jobs=-1)(delayed(train_and_evaluate_model)(model_name, model, X_train, y_train, X_val, y_val, test_size) for model_name, model in models.items())\n", "    for metrics_result, validation_result in results:\n", "        metrics_results.extend(metrics_result)\n", "        validation_results.extend(validation_result)\n", "    \n", "    # Plotting validation results\n", "    plot_validation_results(validation_results, test_size)\n", "\n", "    return metrics_results\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["\n", "# Function to test models\n", "def test_models(models, X_test, df,test_size):\n", "    print(\"\\nTesting Phase\\n\")\n", "    test_results = []\n", "    metrics_results = []\n", "    \n", "    for model_name, model in models.items():\n", "        # Load the trained model\n", "        model_filename = f'{output_folder}/{model_name.replace(\" \", \"_\")}_model.pkl'\n", "        print(f\"Attempting to load model from: {model_filename}\")\n", "        try:\n", "            model = joblib.load(model_filename)\n", "            print(f\"Model {model_name} successfully loaded.\")\n", "        except FileNotFoundError:\n", "            print(f\"Model file {model_filename} not found.\")\n", "            continue\n", "        except Exception as e:\n", "            print(f\"An error occurred while loading {model_name}: {e}\")\n", "            continue\n", "        \n", "        # Predict the test set\n", "        try:\n", "            y_test_pred = model.predict(X_test.toarray())\n", "            test_results.append((model_name, y_test_pred))\n", "            print(f\"Model {model_name} successfully made predictions.\")\n", "        except Exception as e:\n", "            print(f\"Error during prediction with model {model_name}: {e}\")\n", "            continue\n", "        \n", "        # If actual labels are available for testing (optional)\n", "        if 'actual_labels' in df.columns:\n", "            y_test_actual = df['actual_labels']\n", "\n", "            # Calculate the metrics for testing\n", "            accuracy = accuracy_score(y_test_actual, y_test_pred)\n", "            recall = recall_score(y_test_actual, y_test_pred, average='weighted')\n", "            precision = precision_score(y_test_actual, y_test_pred, average='weighted')\n", "            f1 = f1_score(y_test_actual, y_test_pred, average='weighted')\n", "\n", "            # Append metrics to results\n", "            metrics_results.append((model_name, test_size, accuracy, precision, recall, f1))\n", "\n", "            # Confusion Matrix Visualization for Testing\n", "            plot_confusion_matrix(y_test_actual, y_test_pred, model_name, 'Testing', test_size)\n", "        else:\n", "            print(f\"Warning: Actual labels are not provided for meaningful evaluation of {model_name}\")\n", "\n", "    # Save test results to CSV including original and cleaned comments\n", "    # Ensure that there are predictions to process\n", "    if len(test_results) == 0:\n", "        print(\"Warning: No test results available. Skipping majority vote calculation.\")\n", "        return []\n", "\n", "    # Calculate majority vote\n", "    predictions = np.array([y_pred for _, y_pred in test_results]).T\n", "    if len(predictions) == 0:\n", "        print(\"Warning: No predictions available.\")\n", "        return []\n", "\n", "    # Calculate majority vote\n", "    majority_vote = [np.bincount(row).argmax() for row in predictions]\n", "\n", "    # Ensure that the length of majority_vote matches the length of df\n", "    if len(majority_vote) != len(df):\n", "        print(f\"Warning: Length of majority_vote ({len(majority_vote)}) does not match length of df ({len(df)}).\")\n", "        return []\n", "\n", "    output_df = df.copy()\n", "    for model_name, y_test_pred in test_results:\n", "        output_df[model_name.replace(' ', '_') + '_prediction'] = y_test_pred  # Add prediction for each model\n", "\n", "    output_df['majority_vote'] = majority_vote\n", "    \n", "    output_df.to_csv(f'{output_folder}/test_predictions_test_size_{test_size}.csv', index=False)\n", "    print(f\"Test predictions saved to '{output_folder}/test_predictions_test_size_{test_size}.csv'\")\n", "\n", "    # Save metrics results to CSV\n", "    if metrics_results:\n", "        metrics_df = pd.DataFrame(metrics_results, columns=['Model', 'Test_Size', 'Accuracy', 'Precision', 'Recall', 'F1'])\n", "        metrics_df.to_csv(f'{output_folder}/test_metrics_results_test_size_{test_size}.csv', index=False)\n", "        print(f\"Test metrics results saved to '{output_folder}/test_metrics_results_test_size_{test_size}.csv'\")\n", "    \n", "    # Display statistics of prediction results and save\n", "    stats = []\n", "    for model_name, _ in test_results:\n", "        count_0 = output_df[model_name].value_counts().get(0, 0)\n", "        count_1 = output_df[model_name].value_counts().get(1, 0)\n", "        stats.append({'Model': model_name, 'Predicted_Ham': count_0, 'Predicted_Spam': count_1})\n", "        print(f\"Model: {model_name}\")\n", "        print(f\"Predicted 'ham' (0): {count_0}\")\n", "        print(f\"Predicted 'spam' (1): {count_1}\\n\")\n", "    \n", "    # Save statistics to CSV\n", "    stats_df = pd.DataFrame(stats)\n", "    stats_df.to_csv(f'{output_folder}/prediction_statistics_test_size_{test_size}.csv', index=False)\n", "    print(f\"Prediction statistics saved to '{output_folder}/prediction_statistics_test_size_{test_size}.csv'\")\n", "\n", "    # Visualize the statistics\n", "    plt.figure(figsize=(12, 6))\n", "    for model_stat in stats:\n", "        plt.bar(model_stat['Model'], model_stat['Predicted_Ham'], color='blue', alpha=0.6, label='Ham')\n", "        plt.bar(model_stat['Model'], model_stat['Predicted_Spam'], bottom=model_stat['Predicted_Ham'], color='red', alpha=0.6, label='Spam')\n", "    \n", "    plt.xlabel('Model')\n", "    plt.ylabel('Count of Predictions')\n", "    plt.title(f'Prediction Counts for Each Model (test_size={test_size})')\n", "    plt.xticks(rotation=45)\n", "    plt.tight_layout()\n", "    plt.savefig(f'{output_folder}/prediction_statistics_visualization_test_size_{test_size}.png')\n", "    plt.close()\n", "\n", "    return metrics_results\n"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processed test dataset saved to 'finetuning/processed_test_dataset.csv' and 'finetuning/processed_test_dataset.xlsx'\n", "\n", "Using test_size = 0.2 for splitting the data\n", "\n", "\n", "Training and Validation Phase\n", "\n"]}, {"ename": "XGBoostError", "evalue": "[15:03:15] C:\\buildkite-agent\\builds\\buildkite-windows-cpu-autoscaling-group-i-0b3782d1791676daf-1\\xgboost\\xgboost-ci-windows\\src\\metric\\multiclass_metric.cu:35: Check failed: label_error >= 0 && label_error < static_cast<int32_t>(n_class): MultiClassEvaluation: label must be in [0, num_class), num_class=1 but found 1 in label", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m_RemoteTraceback\u001b[0m                          Traceback (most recent call last)", "\u001b[1;31m_RemoteTraceback\u001b[0m: \n\"\"\"\nTraceback (most recent call last):\n  File \"c:\\Python312\\Lib\\site-packages\\joblib\\externals\\loky\\process_executor.py\", line 463, in _process_worker\n    r = call_item()\n        ^^^^^^^^^^^\n  File \"c:\\Python312\\Lib\\site-packages\\joblib\\externals\\loky\\process_executor.py\", line 291, in __call__\n    return self.fn(*self.args, **self.kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Python312\\Lib\\site-packages\\joblib\\parallel.py\", line 598, in __call__\n    return [func(*args, **kwargs)\n            ^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_33204\\155043958.py\", line 49, in train_and_evaluate_model\n  File \"c:\\Python312\\Lib\\site-packages\\xgboost\\core.py\", line 730, in inner_f\n    return func(**kwargs)\n           ^^^^^^^^^^^^^^\n  File \"c:\\Python312\\Lib\\site-packages\\xgboost\\sklearn.py\", line 1519, in fit\n    self._Booster = train(\n                    ^^^^^^\n  File \"c:\\Python312\\Lib\\site-packages\\xgboost\\core.py\", line 730, in inner_f\n    return func(**kwargs)\n           ^^^^^^^^^^^^^^\n  File \"c:\\Python312\\Lib\\site-packages\\xgboost\\training.py\", line 182, in train\n    if cb_container.after_iteration(bst, i, dtrain, evals):\n       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Python312\\Lib\\site-packages\\xgboost\\callback.py\", line 238, in after_iteration\n    score: str = model.eval_set(evals, epoch, self.metric, self._output_margin)\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Python312\\Lib\\site-packages\\xgboost\\core.py\", line 2125, in eval_set\n    _check_call(\n  File \"c:\\Python312\\Lib\\site-packages\\xgboost\\core.py\", line 282, in _check_call\n    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\nxgboost.core.XGBoostError: [15:03:15] C:\\buildkite-agent\\builds\\buildkite-windows-cpu-autoscaling-group-i-0b3782d1791676daf-1\\xgboost\\xgboost-ci-windows\\src\\metric\\multiclass_metric.cu:35: Check failed: label_error >= 0 && label_error < static_cast<int32_t>(n_class): MultiClassEvaluation: label must be in [0, num_class), num_class=1 but found 1 in label\n\"\"\"", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31mXGBoostError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[35], line 32\u001b[0m\n\u001b[0;32m     29\u001b[0m         \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMetrics results saved to \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00moutput_folder\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m/metrics_results_test_size_\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mtest_size\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.csv\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     31\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;18m__name__\u001b[39m \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m__main__\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[1;32m---> 32\u001b[0m     \u001b[43mmain\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[1;32mIn[35], line 19\u001b[0m, in \u001b[0;36mmain\u001b[1;34m()\u001b[0m\n\u001b[0;32m     16\u001b[0m X_train, X_val, y_train, y_val \u001b[38;5;241m=\u001b[39m train_test_split(X_train_val, y_train_val, test_size\u001b[38;5;241m=\u001b[39mtest_size, random_state\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m42\u001b[39m)\n\u001b[0;32m     18\u001b[0m \u001b[38;5;66;03m# Train and validate models\u001b[39;00m\n\u001b[1;32m---> 19\u001b[0m metrics_results \u001b[38;5;241m=\u001b[39m \u001b[43mtrain_and_validate_models\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmodels\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mX_train\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mX_val\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my_train\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my_val\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtest_size\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     21\u001b[0m \u001b[38;5;66;03m# Train and cross-validate models\u001b[39;00m\n\u001b[0;32m     22\u001b[0m \u001b[38;5;66;03m# metrics_results = train_and_cross_validate_models(models, X_train_val, y_train_val)\u001b[39;00m\n\u001b[0;32m     23\u001b[0m \u001b[38;5;66;03m# Test models\u001b[39;00m\n\u001b[0;32m     24\u001b[0m metrics_results \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m test_models(models, X_test,df, test_size)\n", "Cell \u001b[1;32mIn[33], line 7\u001b[0m, in \u001b[0;36mtrain_and_validate_models\u001b[1;34m(models, X_train, X_val, y_train, y_val, test_size)\u001b[0m\n\u001b[0;32m      4\u001b[0m validation_results \u001b[38;5;241m=\u001b[39m []\n\u001b[0;32m      6\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124mTraining and Validation Phase\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m----> 7\u001b[0m results \u001b[38;5;241m=\u001b[39m \u001b[43mParallel\u001b[49m\u001b[43m(\u001b[49m\u001b[43mn_jobs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m-\u001b[39;49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdelayed\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtrain_and_evaluate_model\u001b[49m\u001b[43m)\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmodel_name\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mX_train\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my_train\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mX_val\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my_val\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtest_size\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mmodel_name\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mmodels\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mitems\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m      8\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m metrics_result, validation_result \u001b[38;5;129;01min\u001b[39;00m results:\n\u001b[0;32m      9\u001b[0m     metrics_results\u001b[38;5;241m.\u001b[39mextend(metrics_result)\n", "File \u001b[1;32mc:\\Python312\\Lib\\site-packages\\joblib\\parallel.py:2007\u001b[0m, in \u001b[0;36mParallel.__call__\u001b[1;34m(self, iterable)\u001b[0m\n\u001b[0;32m   2001\u001b[0m \u001b[38;5;66;03m# The first item from the output is blank, but it makes the interpreter\u001b[39;00m\n\u001b[0;32m   2002\u001b[0m \u001b[38;5;66;03m# progress until it enters the Try/Except block of the generator and\u001b[39;00m\n\u001b[0;32m   2003\u001b[0m \u001b[38;5;66;03m# reaches the first `yield` statement. This starts the asynchronous\u001b[39;00m\n\u001b[0;32m   2004\u001b[0m \u001b[38;5;66;03m# dispatch of the tasks to the workers.\u001b[39;00m\n\u001b[0;32m   2005\u001b[0m \u001b[38;5;28mnext\u001b[39m(output)\n\u001b[1;32m-> 2007\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m output \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mreturn_generator \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;43mlist\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43moutput\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Python312\\Lib\\site-packages\\joblib\\parallel.py:1650\u001b[0m, in \u001b[0;36mParallel._get_outputs\u001b[1;34m(self, iterator, pre_dispatch)\u001b[0m\n\u001b[0;32m   1647\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n\u001b[0;32m   1649\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backend\u001b[38;5;241m.\u001b[39mretrieval_context():\n\u001b[1;32m-> 1650\u001b[0m         \u001b[38;5;28;01<PERSON><PERSON> from\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_retrieve()\n\u001b[0;32m   1652\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mGeneratorExit\u001b[39;00m:\n\u001b[0;32m   1653\u001b[0m     \u001b[38;5;66;03m# The generator has been garbage collected before being fully\u001b[39;00m\n\u001b[0;32m   1654\u001b[0m     \u001b[38;5;66;03m# consumed. This aborts the remaining tasks if possible and warn\u001b[39;00m\n\u001b[0;32m   1655\u001b[0m     \u001b[38;5;66;03m# the user if necessary.\u001b[39;00m\n\u001b[0;32m   1656\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exception \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n", "File \u001b[1;32mc:\\Python312\\Lib\\site-packages\\joblib\\parallel.py:1754\u001b[0m, in \u001b[0;36mParallel._retrieve\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1747\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_wait_retrieval():\n\u001b[0;32m   1748\u001b[0m \n\u001b[0;32m   1749\u001b[0m     \u001b[38;5;66;03m# If the callback thread of a worker has signaled that its task\u001b[39;00m\n\u001b[0;32m   1750\u001b[0m     \u001b[38;5;66;03m# triggered an exception, or if the retrieval loop has raised an\u001b[39;00m\n\u001b[0;32m   1751\u001b[0m     \u001b[38;5;66;03m# exception (e.g. `GeneratorExit`), exit the loop and surface the\u001b[39;00m\n\u001b[0;32m   1752\u001b[0m     \u001b[38;5;66;03m# worker traceback.\u001b[39;00m\n\u001b[0;32m   1753\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_aborting:\n\u001b[1;32m-> 1754\u001b[0m         \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_raise_error_fast\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1755\u001b[0m         \u001b[38;5;28;01mbreak\u001b[39;00m\n\u001b[0;32m   1757\u001b[0m     \u001b[38;5;66;03m# If the next job is not ready for retrieval yet, we just wait for\u001b[39;00m\n\u001b[0;32m   1758\u001b[0m     \u001b[38;5;66;03m# async callbacks to progress.\u001b[39;00m\n", "File \u001b[1;32mc:\\Python312\\Lib\\site-packages\\joblib\\parallel.py:1789\u001b[0m, in \u001b[0;36mParallel._raise_error_fast\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1785\u001b[0m \u001b[38;5;66;03m# If this error job exists, immediately raise the error by\u001b[39;00m\n\u001b[0;32m   1786\u001b[0m \u001b[38;5;66;03m# calling get_result. This job might not exists if abort has been\u001b[39;00m\n\u001b[0;32m   1787\u001b[0m \u001b[38;5;66;03m# called directly or if the generator is gc'ed.\u001b[39;00m\n\u001b[0;32m   1788\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m error_job \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m-> 1789\u001b[0m     \u001b[43merror_job\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_result\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Python312\\Lib\\site-packages\\joblib\\parallel.py:745\u001b[0m, in \u001b[0;36mBatchCompletionCallBack.get_result\u001b[1;34m(self, timeout)\u001b[0m\n\u001b[0;32m    739\u001b[0m backend \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mparallel\u001b[38;5;241m.\u001b[39m_backend\n\u001b[0;32m    741\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m backend\u001b[38;5;241m.\u001b[39msupports_retrieve_callback:\n\u001b[0;32m    742\u001b[0m     \u001b[38;5;66;03m# We assume that the result has already been retrieved by the\u001b[39;00m\n\u001b[0;32m    743\u001b[0m     \u001b[38;5;66;03m# callback thread, and is stored internally. It's just waiting to\u001b[39;00m\n\u001b[0;32m    744\u001b[0m     \u001b[38;5;66;03m# be returned.\u001b[39;00m\n\u001b[1;32m--> 745\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_return_or_raise\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    747\u001b[0m \u001b[38;5;66;03m# For other backends, the main thread needs to run the retrieval step.\u001b[39;00m\n\u001b[0;32m    748\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n", "File \u001b[1;32mc:\\Python312\\Lib\\site-packages\\joblib\\parallel.py:763\u001b[0m, in \u001b[0;36mBatchCompletionCallBack._return_or_raise\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    761\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m    762\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstatus \u001b[38;5;241m==\u001b[39m TASK_ERROR:\n\u001b[1;32m--> 763\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_result\n\u001b[0;32m    764\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_result\n\u001b[0;32m    765\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n", "\u001b[1;31mXGBoostError\u001b[0m: [15:03:15] C:\\buildkite-agent\\builds\\buildkite-windows-cpu-autoscaling-group-i-0b3782d1791676daf-1\\xgboost\\xgboost-ci-windows\\src\\metric\\multiclass_metric.cu:35: Check failed: label_error >= 0 && label_error < static_cast<int32_t>(n_class): MultiClassEvaluation: label must be in [0, num_class), num_class=1 but found 1 in label"]}], "source": ["# Main function to execute the process\n", "def main():\n", "    X_train_val_raw, y_train_val = load_training_data()\n", "    X_train_val, tfidf = feature_extraction(X_train_val_raw)\n", "    X_test, df = load_testing_data(tfidf)\n", "    models = initialize_models()\n", "\n", "    # Define test sizes for splitting\n", "    test_sizes = [0.2, 0.25, 0.3, 0.35]\n", "    # test_sizes = [0.2,0.3]\n", "    \n", "    for test_size in test_sizes:\n", "        print(f\"\\nUsing test_size = {test_size} for splitting the data\\n\")\n", "        \n", "        # Split the training and validation data\n", "        X_train, X_val, y_train, y_val = train_test_split(X_train_val, y_train_val, test_size=test_size, random_state=42)\n", "        \n", "        # Train and validate models\n", "        metrics_results = train_and_validate_models(models, X_train, X_val, y_train, y_val, test_size)\n", "        \n", "        # Train and cross-validate models\n", "        # metrics_results = train_and_cross_validate_models(models, X_train_val, y_train_val)\n", "        # Test models\n", "        metrics_results += test_models(models, X_test,df, test_size)\n", "        \n", "        # Save metrics results to CSV\n", "        metrics_df = pd.DataFrame(metrics_results, columns=['Model', 'Phase', 'Accuracy', 'Recall', 'Precision', 'F1'])\n", "        metrics_df.to_csv(f'{output_folder}/metrics_results_test_size_{test_size}.csv', index=False)\n", "        print(f\"Metrics results saved to '{output_folder}/metrics_results_test_size_{test_size}.csv'\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}