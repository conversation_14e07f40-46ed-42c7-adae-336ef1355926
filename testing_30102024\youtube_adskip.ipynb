{"cells": [{"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["from selenium import webdriver\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "import pandas as pd\n", "import time\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["\n", "# Path to Chrome driver and profile\n", "driver_path = '/path/to/chromedriver'  # Replace with your chromedriver path\n", "profile_path = r'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Default'  # Replace with your profile path\n", "\n", "# YouTube video ID and URL\n", "youtubeid = \"W9-racb2rAY\"\n", "video_url = f\"https://www.youtube.com/watch?v={youtubeid}\""]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["\n", "\n", "def initialize_driver_with_profile(profile_path):\n", "    \"\"\"Inisialisasi driver dengan profil Chrome yang sudah ada.\"\"\"\n", "    chrome_options = Options()\n", "    chrome_options.add_argument(f\"user-data-dir={profile_path}\")\n", "    chrome_options.add_argument(\"--no-sandbox\")\n", "    chrome_options.add_argument(\"--disable-dev-shm-usage\")\n", "    chrome_options.add_argument(\"--no-first-run\")\n", "    chrome_options.add_argument(\"--remote-debugging-port=9222\")\n", "    chrome_options.add_argument(\"--enable-logging\")\n", "    chrome_options.add_argument(\"--v=1\")  # Level logging dasar\n", "    # service = Service(driver_path)\n", "    # driver = webdriver.Chrome(service=service, options=chrome_options)\n", "    driver = webdriver.Chrome(options=chrome_options)\n", "    return driver\n", "\n", "def detect_and_skip_ad(driver):\n", "    \"\"\"Detect and skip ad if present.\"\"\"\n", "    ad_present = False\n", "    try:\n", "        WebDriverWait(driver, 15).until(\n", "            EC.presence_of_any_elements_located([\n", "                (By.CLASS_NAME, \"video-ads.ytp-ad-module\"),\n", "                (By.<PERSON>LA<PERSON>_NAME, \"ytp-ad-player-overlay-layout_\")\n", "            ])\n", "        )\n", "        ad_present = True\n", "        print(\"Ad detected.\")\n", "\n", "        skip_button = WebDriverWait(driver, 30).until(\n", "            EC.element_to_be_clickable((By.CLASS_NAME, \"ytp-skip-ad-button\"))\n", "        )\n", "        skip_button.click()\n", "        print(\"Skip Ad button clicked.\")\n", "\n", "    except Exception as e:\n", "        print(\"No ad detected or Skip Ad button not found.\")\n", "        print(f\"Error: {e}\")\n", "\n", "    return ad_present\n", "\n", "def get_all_youtube_comments(driver, max_comments=None):\n", "    \"\"\"Fetch all comments by scrolling.\"\"\"\n", "    comments = []\n", "    last_height = driver.execute_script(\"return document.documentElement.scrollHeight\")\n", "\n", "    while True:\n", "        driver.execute_script(\"window.scrollTo(0, document.documentElement.scrollHeight);\")\n", "        time.sleep(2)\n", "\n", "        try:\n", "            WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, '//*[@id=\"content-text\"]'))\n", "            )\n", "        except Exception as e:\n", "            print(\"Comments did not load in time.\")\n", "            break\n", "\n", "        comment_elements = driver.find_elements(By.XPATH, '//*[@id=\"content-text\"]')\n", "        for element in comment_elements[len(comments):]:\n", "            comment_text = element.text\n", "            if comment_text:\n", "                comments.append(comment_text)\n", "                if max_comments and len(comments) >= max_comments:\n", "                    return comments\n", "\n", "        new_height = driver.execute_script(\"return document.documentElement.scrollHeight\")\n", "        if new_height == last_height:\n", "            break\n", "        last_height = new_height\n", "\n", "    return comments\n", "\n", "def save_to_csv(comments, filename):\n", "    \"\"\"Save comments to CSV.\"\"\"\n", "    df = pd.DataFrame(comments, columns=[\"Comment\"])\n", "    df.to_csv(filename, index=False)\n", "    print(f\"Comments saved to {filename}\")\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["No ad detected or Skip Ad button not found.\n", "Error: module 'selenium.webdriver.support.expected_conditions' has no attribute 'presence_of_any_elements_located'\n", "Comments saved to youtube_comments_W9-racb2rAY.csv\n"]}], "source": ["\n", "# Initialize WebDriver with Chrome profile\n", "driver = initialize_driver_with_profile(profile_path)\n", "\n", "# Access YouTube video\n", "driver.get(video_url)\n", "WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, \"comments\")))\n", "\n", "# Detect and skip ad if present\n", "ad_present = detect_and_skip_ad(driver)\n", "\n", "# Begin fetching comments\n", "comments = get_all_youtube_comments(driver, max_comments=None)\n", "save_to_csv(comments, filename=f\"youtube_comments_{youtubeid}.csv\")\n", "\n", "# Quit driver\n", "driver.quit()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}