{"cells": [{"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import re\n", "from nltk.corpus import stopwords\n", "from nltk.stem import PorterStemmer, WordNetLemmatizer\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.naive_bayes import MultinomialNB\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.svm import SVC\n", "from sklearn.metrics import classification_report, accuracy_score, confusion_matrix, ConfusionMatrixDisplay, precision_score, recall_score, f1_score\n", "from sklearn.model_selection import train_test_split, GridSearchCV\n", "from sklearn.utils import resample\n", "import joblib\n", "import matplotlib.pyplot as plt\n", "import numpy as np"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Training Dataset"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["\n", "# Load your training dataset\n", "# Using the uploaded 'Youtube-Spam-Dataset.csv' with columns 'CONTENT' and 'CLASS'\n", "file_path = 'dataset/youtube_spam.csv'\n", "train_data = pd.read_csv(file_path)\n", "train_texts = train_data['CONTENT']\n", "train_labels = train_data['CLASS']\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training Data Size: 1956\n", "Training Data Distribution:\n", "CLASS\n", "1    1005\n", "0     951\n", "Name: count, dtype: int64\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Display training data size and distribution\n", "print(f'Training Data Size: {len(train_data)}')\n", "print('Training Data Distribution:')\n", "print(train_labels.value_counts())\n", "\n", "# Plot the distribution of classes in the training dataset\n", "plt.figure(figsize=(6, 4))\n", "train_labels.value_counts().plot(kind='bar', color=['skyblue', 'lightgreen'])\n", "plt.xlabel('Class')\n", "plt.ylabel('Count')\n", "plt.title('Training Data Distribution')\n", "plt.xticks(rotation=0)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Balancing Training Set"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Balanced Training Data Size: 1902\n", "Balanced Training Data Distribution:\n", "CLASS\n", "0    951\n", "1    951\n", "Name: count, dtype: int64\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Balancing the training data using upsampling\n", "train_data_majority = train_data[train_data['CLASS'] == 0]\n", "train_data_minority = train_data[train_data['CLASS'] == 1]\n", "\n", "train_data_minority_upsampled = resample(train_data_minority, \n", "                                         replace=True,     # sample with replacement\n", "                                         n_samples=len(train_data_majority),    # to match majority class\n", "                                         random_state=42)  # reproducible results\n", "\n", "train_data_balanced = pd.concat([train_data_majority, train_data_minority_upsampled])\n", "\n", "# Display balanced training data size and distribution\n", "print(f'Balanced Training Data Size: {len(train_data_balanced)}')\n", "print('Balanced Training Data Distribution:')\n", "print(train_data_balanced['CLASS'].value_counts())\n", "\n", "# Plot the distribution of classes in the balanced training dataset\n", "plt.figure(figsize=(6, 4))\n", "train_data_balanced['CLASS'].value_counts().plot(kind='bar', color=['skyblue', 'lightgreen'])\n", "plt.xlabel('Class')\n", "plt.ylabel('Count')\n", "plt.title('Balanced Training Data Distribution')\n", "plt.xticks(rotation=0)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Text Preprocessing"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Preprocessing function for text cleaning\n", "def preprocess_text(text):\n", "    # Lowercase the text\n", "    text = text.lower()\n", "    # Remove special characters and numbers\n", "    text = re.sub(r'[^a-zA-Z\\s]', '', text)\n", "    # Remove stopwords\n", "    stop_words = set(stopwords.words('english'))\n", "    text = ' '.join([word for word in text.split() if word not in stop_words])\n", "    return text\n", "\n", "# Stemming and Lemmatization functions\n", "stemmer = PorterStemmer()\n", "lemmatizer = WordNetLemmatizer()\n", "\n", "def stem_and_lemmatize(text):\n", "    # Apply stemming and lemmatization\n", "    words = text.split()\n", "    words = [stemmer.stem(word) for word in words]\n", "    words = [lemmatizer.lemmatize(word) for word in words]\n", "    return ' '.join(words)\n", "\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Apply preprocessing to the dataset\n", "train_texts = train_texts.apply(preprocess_text).apply(stem_and_lemmatize)\n", "\n", "# Convert the text data into TF-IDF feature vectors\n", "tfidf_vectorizer = TfidfVectorizer(stop_words='english', max_features=5000)\n", "\n", "X_train = tfidf_vectorizer.fit_transform(train_texts)\n", "y_train = train_labels"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training Accuracy: 0.95\n", "Training Precision: 0.95\n", "Training Recall: 0.95\n", "Training F1 Score: 0.95\n", "Training Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.96      0.95      0.95       951\n", "           1       0.95      0.96      0.96      1005\n", "\n", "    accuracy                           0.95      1956\n", "   macro avg       0.95      0.95      0.95      1956\n", "weighted avg       0.95      0.95      0.95      1956\n", "\n", "Confusion Matrix for Training Dataset:\n", "[[902  49]\n", " [ 40 965]]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["['tfidf_vectorizer.pkl']"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["# Define the model\n", "model = MultinomialNB()\n", "\n", "# Train the model on the entire balanced training dataset\n", "model.fit(X_train, y_train)\n", "\n", "# Display training results\n", "train_predictions = model.predict(X_train)\n", "train_accuracy = accuracy_score(y_train, train_predictions)\n", "train_precision = precision_score(y_train, train_predictions, average='weighted')\n", "train_recall = recall_score(y_train, train_predictions, average='weighted')\n", "train_f1 = f1_score(y_train, train_predictions, average='weighted')\n", "\n", "print(f'Training Accuracy: {train_accuracy:.2f}')\n", "print(f'Training Precision: {train_precision:.2f}')\n", "print(f'Training Recall: {train_recall:.2f}')\n", "print(f'Training F1 Score: {train_f1:.2f}')\n", "print('Training Classification Report:')\n", "print(classification_report(y_train, train_predictions))\n", "\n", "# Confusion Matrix for the training dataset\n", "train_conf_matrix = confusion_matrix(y_train, train_predictions)\n", "print('Confusion Matrix for Training Dataset:')\n", "print(train_conf_matrix)\n", "\n", "# Display Confusion Matrix for the training dataset\n", "disp_train = ConfusionMatrixDisplay(confusion_matrix=train_conf_matrix, display_labels=['Not Spam', 'Spam'])\n", "disp_train.plot()\n", "plt.title('Confusion Matrix for Training Dataset')\n", "plt.show()\n", "\n", "# Save the trained model and vectorizer for baseline testing with a different dataset\n", "joblib.dump(model, 'spam_detection_model.pkl')\n", "joblib.dump(tfidf_vectorizer, 'tfidf_vectorizer.pkl')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Testing Model"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# Load the new testing dataset for baseline testing\n", "# Using the uploaded 'youtube_comments_i6IOiUi6IYY.xlsx'\n", "test_file_path = 'youtube_comments_i6IOiUi6IYY.xlsx'\n", "new_test_data = pd.read_excel(test_file_path)\n", "new_test_texts = new_test_data['Comment']\n", "\n", "# Apply preprocessing to the new testing dataset\n", "new_test_texts = new_test_texts.apply(preprocess_text).apply(stem_and_lemmatize)\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# Load the saved model and vectorizer\n", "model = joblib.load('spam_detection_model.pkl')\n", "tfidf_vectorizer = joblib.load('tfidf_vectorizer.pkl')\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                               Comment  Prediction\n", "0      <PERSON>… man has been bringing it lately.           0\n", "1    <PERSON><PERSON> is the best he prove this  everytime by d...           0\n", "2       That pass from <PERSON> to <PERSON><PERSON> was amazing.            0\n", "3                 <PERSON><PERSON> and <PERSON><PERSON> were so so good man            0\n", "4    \" Keep <PERSON><PERSON>'s name out your mouth,  dude is wo...           0\n", "..                                                 ...         ...\n", "648                                                  r           1\n", "649                                     Corrupt league           1\n", "650                                               Like           0\n", "651                                                  r           1\n", "652                                     Corrupt league           1\n", "\n", "[653 rows x 2 columns]\n", "Confusion Matrix for New Testing Dataset:\n", "[[250   0]\n", " [  0 403]]\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjYAAAHHCAYAAACskBIUAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy80BEi2AAAACXBIWXMAAA9hAAAPYQGoP6dpAABht0lEQVR4nO3dd1gU1/oH8O/SdumIlBVFwAooiBKvEqyxYIklGhMSo9hvvGKMUePPKBaMYkmsMZZoxHg11mgiGnuNoib2qMEasVAMClhC2z2/P7hMXAHddReR4fvxmedhz5w5c2Zd4OU958wohBACRERERDJgVtodICIiIjIVBjZEREQkGwxsiIiISDYY2BAREZFsMLAhIiIi2WBgQ0RERLLBwIaIiIhkg4ENERERyQYDGyIiIpINBjb00ly+fBlt27aFo6MjFAoFNm/ebNL2//zzTygUCsTGxpq03bKsRYsWaNGihcnae/jwIQYMGAC1Wg2FQoGPP/7YZG3Ti4uNjYVCocCff/5Z2l0hKnUMbMqZq1ev4t///jeqVasGlUoFBwcHhIaGYu7cufj7779L9NwRERE4d+4cpkyZgpUrV+K1114r0fO9TH369IFCoYCDg0OR7+Ply5ehUCigUCjwxRdfGNz+nTt3MHHiRJw+fdoEvX1xU6dORWxsLAYPHoyVK1eiV69eJXo+b29vKBQKDB06tNC+/fv3Q6FQYMOGDSXah2cp6N/zNlMF21OnTjX5HwTGmjhxos612tjYoGrVqujUqROWL1+O7OzsF25727ZtmDhxouk6a6RX8f2nwixKuwP08mzduhU9evSAUqlE7969UbduXeTk5OCXX37BqFGjcP78eSxZsqREzv33338jPj4eY8eORWRkZImcw8vLC3///TcsLS1LpP3nsbCwwOPHj7Flyxa88847OvtWrVoFlUqFrKysF2r7zp07mDRpEry9vREUFKT3cTt37nyh8xVn7969aNy4MSZMmGDSdp/nm2++wZgxY+Dh4fFSz/s8c+bMwcOHD6XX27Ztw/fff4/Zs2fDxcVFKn/99ddNcr6pU6fi7bffRteuXXXKe/XqhfDwcCiVSpOc50UsXLgQdnZ2yM7Oxu3bt7Fjxw7069cPc+bMQVxcHDw9PQ1uc9u2bViwYMErE9wU9/7Tq4WBTTlx/fp1hIeHw8vLC3v37kWlSpWkfUOGDMGVK1ewdevWEjv/3bt3AQBOTk4ldg6FQgGVSlVi7T+PUqlEaGgovv/++0KBzerVq9GxY0ds3LjxpfTl8ePHsLGxgZWVlUnbTU1Nhb+/v8nay8vLg1arfWY/69Spg4SEBEybNg3z5s0z2blN4elfcMnJyfj+++/RtWtXeHt7v7R+mJubw9zc/KWdryhvv/22TjA3fvx4rFq1Cr1790aPHj1w9OjRUuwdlSuCyoUPP/xQABCHDx/Wq35ubq6Ijo4W1apVE1ZWVsLLy0uMGTNGZGVl6dTz8vISHTt2FIcOHRINGzYUSqVS+Pj4iBUrVkh1JkyYIADobF5eXkIIISIiIqSvn1RwzJN27twpQkNDhaOjo7C1tRW1atUSY8aMkfZfv35dABDLly/XOW7Pnj2iSZMmwsbGRjg6OorOnTuLCxcuFHm+y5cvi4iICOHo6CgcHBxEnz59xKNHj577fkVERAhbW1sRGxsrlEqluH//vrTv+PHjAoDYuHGjACBmzpwp7UtLSxMjRowQdevWFba2tsLe3l60a9dOnD59Wqqzb9++Qu/fk9fZvHlzUadOHfHbb7+Jpk2bCmtrazFs2DBpX/PmzaW2evfuLZRKZaHrb9u2rXBychK3b98u8vqK68P169eFEEKkpKSIfv36CTc3N6FUKkVgYKCIjY3VaaPg/2fmzJli9uzZolq1asLMzEycOnWq2Pe14PPVr18/oVKpdPpX0Kf169frHHPr1i3Rt29f4ebmJqysrIS/v79YtmyZtF+r1YqKFSuK4cOHS2UajUY4OjoKMzMznf+7adOmCXNzc/HgwYNi+/ikmTNn6rwvBVauXCkaNGggVCqVqFChgnj33XdFYmKiTp1Lly6Jbt26CXd3d6FUKkXlypXFu+++K9LT04UQosj3PyIiQgghxPLlywudV5/vzQJnzpwRzZo1EyqVSlSuXFlMnjxZfPvtt0Vey9MKvnfu3r1b5P5BgwYJAGLnzp1S2cGDB8Xbb78tPD09hZWVlahSpYr4+OOPxePHj6U6ERERRV7zk+91SEiIcHZ2FiqVSjRo0KDQZ0GI5//cEEKIrKwsMX78eFG9enWpP6NGjdL5efes959eLczYlBNbtmxBtWrV9E6JDxgwACtWrMDbb7+NESNG4NixY4iJicHFixexadMmnbpXrlzB22+/jf79+yMiIgLffvst+vTpg+DgYNSpUwfdunWDk5MThg8fjvfeew8dOnSAnZ2dQf0/f/483nzzTQQGBiI6OhpKpRJXrlzB4cOHn3nc7t270b59e1SrVg0TJ07E33//jfnz5yM0NBQnT54s9Ff1O++8Ax8fH8TExODkyZNYunQp3NzcMH36dL362a1bN3z44Yf44Ycf0K9fPwD52RpfX180aNCgUP1r165h8+bN6NGjB3x8fJCSkoLFixejefPmuHDhAjw8PODn54fo6GiMHz8egwYNQtOmTQHoDm+kpaWhffv2CA8PxwcffAB3d/ci+zd37lzs3bsXERERiI+Ph7m5ORYvXoydO3di5cqVxQ71+Pn5YeXKlRg+fDiqVKmCESNGAABcXV3x999/o0WLFrhy5QoiIyPh4+OD9evXo0+fPkhPT8ewYcN02lq+fDmysrIwaNAgKJVKODs7P/d9HTt2LL777rvnZm1SUlLQuHFjKBQKREZGwtXVFT///DP69++PzMxMfPzxx1AoFAgNDcXBgwel486ePYuMjAyYmZnh8OHD6NixIwDg0KFDqF+/vsGf1ydNmTIFUVFReOeddzBgwADcvXsX8+fPR7NmzXDq1Ck4OTkhJycHYWFhyM7OxtChQ6FWq3H79m3ExcUhPT0djo6OWLlyJQYMGIB//etfGDRoEACgevXqzzz38743AeD27dto2bIlFAoFxowZA1tbWyxdutRkw1q9evXCkiVLsHPnTrRp0wYAsH79ejx+/BiDBw9GxYoVcfz4ccyfPx+3bt3C+vXrAQD//ve/cefOHezatQsrV64s1O7cuXPRuXNn9OzZEzk5OVizZg169OiBuLg46f9Pn58bWq0WnTt3xi+//IJBgwbBz88P586dw+zZs3Hp0iVpTs2LvP9USko7sqKSl5GRIQCILl266FX/9OnTAoAYMGCATvnIkSMFALF3716pzMvLSwAQBw8elMpSU1OFUqkUI0aMkMqe/Gv9SfpmbGbPnv3MvwqfPMeTGZugoCDh5uYm0tLSpLIzZ84IMzMz0bt370Ln69evn06bb731lqhYsWKx53zyOmxtbYUQQrz99tuiVatWQoj8TIBarRaTJk0q8j3IysoSGo2m0HUolUoRHR0tlf36669FZqOEyM/KABCLFi0qct+TGRshhNixY4cAID7//HNx7do1YWdnJ7p27frcaxTinyzAk+bMmSMAiP/+979SWU5OjggJCRF2dnYiMzNTui4AwsHBQaSmphp8vr59+wqVSiXu3LkjhCg6Y9O/f39RqVIl8ddff+m0Ex4eLhwdHaWMwMyZM4W5ubnUt3nz5gkvLy/xr3/9S4wePVoIkf9/5+TkpJPZeZ6nMzZ//vmnMDc3F1OmTNGpd+7cOWFhYSGVnzp1qsjs09NsbW2LzBIUl7HR53tz6NChQqFQ6GTO0tLShLOzs0kyNvfv3xcAxFtvvSWVPZmZKRATEyMUCoW4ceOGVDZkyJBCmdvi2sjJyRF169YVb7zxhlSmz8+NlStXCjMzM3Ho0CGd8kWLFhXKchf3/tOrhauiyoHMzEwAgL29vV71t23bBgD45JNPdMoL/kp/ei6Ov7+/lEUA8v+Kr127Nq5du/bCfX5awdycH3/8EVqtVq9jkpKScPr0afTp00cnKxAYGIg2bdpI1/mkDz/8UOd106ZNkZaWJr2H+nj//fexf/9+JCcnY+/evUhOTsb7779fZF2lUgkzs/xvQ41Gg7S0NNjZ2aF27do4efKk3udUKpXo27evXnXbtm2Lf//734iOjka3bt2gUqmwePFivc/1tG3btkGtVuO9996TyiwtLfHRRx/h4cOHOHDggE797t27w9XV1eDzjBs3Dnl5eZg2bVqR+4UQ2LhxIzp16gQhBP766y9pCwsLQ0ZGhvSeNm3aFBqNBkeOHAGQn5lp2rQpmjZtikOHDgEAfv/9d6Snp+t8tg31ww8/QKvV4p133tHpj1qtRs2aNbFv3z4AgKOjIwBgx44dePz48Quf72n6fG9u374dISEhOpPSnZ2d0bNnT5P0oSDb9eDBA6nM2tpa+vrRo0f466+/8Prrr0MIgVOnTunV7pNt3L9/HxkZGWjatKnO940+PzfWr18PPz8/+Pr66vwfvfHGGwAg/R9R2cHAphxwcHAAoPuD5Vlu3LgBMzMz1KhRQ6dcrVbDyckJN27c0CmvWrVqoTYqVKiA+/fvv2CPC3v33XcRGhqKAQMGwN3dHeHh4Vi3bt0zg5yCftauXbvQPj8/P/z111949OiRTvnT11KhQgUAMOhaOnToAHt7e6xduxarVq1Cw4YNC72XBbRaLWbPno2aNWtCqVTCxcUFrq6u0tCIvipXrmzQROEvvvgCzs7OOH36NObNmwc3Nze9j33ajRs3ULNmTSlAK+Dn5yftf5KPj88LnadatWrSsEZSUlKh/Xfv3kV6ejqWLFkCV1dXna0g6EtNTQUANGjQADY2NlIQUxDYNGvWDL/99huysrKkfU2aNHmh/gL5y/yFEKhZs2ahPl28eFHqj4+PDz755BMsXboULi4uCAsLw4IFCwz6DBRFn+/NGzduFPn5LO4za6iCVWNP/mGVmJgo/cFhZ2cHV1dXNG/eHAD0vua4uDg0btwYKpUKzs7OcHV1xcKFC3WO1+fnxuXLl3H+/PlC/z+1atUC8M9nhsoOzrEpBxwcHODh4YHff//doOMUCoVe9YpbjSGEeOFzaDQandfW1tY4ePAg9u3bh61bt2L79u1Yu3Yt3njjDezcudNkK0KMuZYCSqUS3bp1w4oVK3Dt2rVnLlWdOnUqoqKi0K9fP0yePBnOzs4wMzPDxx9/rHdmCtD961Ufp06dkn5gnzt3TifbUtIM7euTxo4di5UrV2L69OmFViQVvF8ffPABIiIiijw+MDAQQH5GqVGjRjh48CCuXLmC5ORkNG3aFO7u7sjNzcWxY8dw6NAh+Pr6vlB26ck+KRQK/Pzzz0V+tp6cu/Pll1+iT58++PHHH7Fz50589NFHiImJwdGjR1GlSpUXOr8pPs/GKvi5UxAoaTQatGnTBvfu3cPo0aPh6+sLW1tb3L59G3369NHrc3/o0CF07twZzZo1w9dff41KlSrB0tISy5cvx+rVq6V6+vzc0Gq1CAgIwKxZs4o814ssU6fSxcCmnHjzzTexZMkSxMfHIyQk5Jl1vby8oNVqcfnyZemvbiB/YmZ6ejq8vLxM1q8KFSogPT29UPnTf+UDgJmZGVq1aoVWrVph1qxZmDp1KsaOHYt9+/ahdevWRV4HACQkJBTa98cff8DFxQW2trbGX0QR3n//fXz77bcwMzNDeHh4sfU2bNiAli1bYtmyZTrl6enpOktn9Q0y9fHo0SP07dsX/v7+eP311zFjxgy89dZbaNiw4Qu15+XlhbNnz0Kr1epkbf744w9pv6lUr14dH3zwARYvXoxGjRrp7HN1dYW9vT00Gk2Rn4enNW3aFNOnT8fu3bvh4uICX19fKBQK1KlTB4cOHcKhQ4fw5ptvGt1fIQR8fHykDMCzBAQEICAgAOPGjcORI0cQGhqKRYsW4fPPPwdg2s9BAS8vL1y5cqVQeVFlL6Jg4m9YWBiA/ED60qVLWLFiBXr37i3V27VrV6Fji7vejRs3QqVSYceOHTqTnJcvX16o7vN+blSvXh1nzpxBq1atnvv+lsT7T6bHoahy4tNPP4WtrS0GDBiAlJSUQvuvXr2KuXPnAsgfSgHybz72pIK/aApWHJhC9erVkZGRgbNnz0plSUlJhVZe3bt3r9CxBXMCiruzaaVKlRAUFIQVK1boBE+///47du7cKV1nSWjZsiUmT56Mr776Cmq1uth65ubmhf56Xr9+PW7fvq1TVhCAFRUEGmr06NFITEzEihUrMGvWLHh7eyMiIuKF7xDboUMHJCcnY+3atVJZXl4e5s+fDzs7O2mIwVTGjRuH3NxczJgxQ6fc3Nwc3bt3x8aNG4vMThbcS6lA06ZNkZ2djTlz5qBJkybSL62mTZti5cqVuHPnjlHza4D8VXLm5uaYNGlSof9nIQTS0tIA5M+Dy8vL09kfEBAAMzMznf8XW1tbk3wGnhQWFob4+Hidu1rfu3cPq1atMrrt1atXY+nSpQgJCUGrVq0A/JNFevL9EEJIP3+eVNzn3tzcHAqFQiez++effxa6K7A+Pzfeeecd3L59G998802hun///bfOcHVJvP9keszYlBPVq1fH6tWr8e6778LPz0/nzsNHjhyRlucCQL169RAREYElS5YgPT0dzZs3x/Hjx7FixQp07doVLVu2NFm/wsPDMXr0aLz11lv46KOP8PjxYyxcuBC1atXSmQQYHR2NgwcPomPHjvDy8kJqaiq+/vprVKlS5ZlzIGbOnIn27dsjJCQE/fv3l5Z7Ozo6lujdTM3MzDBu3Ljn1nvzzTcRHR2Nvn374vXXX8e5c+ewatUqVKtWTade9erV4eTkhEWLFsHe3h62trZo1KiRwfNV9u7di6+//hoTJkyQlp8vX74cLVq0QFRUVKFgQR+DBg3C4sWL0adPH5w4cQLe3t7YsGEDDh8+jDlz5ug9aV1fBVmbFStWFNo3bdo07Nu3D40aNcLAgQPh7++Pe/fu4eTJk9i9e7fOL7qQkBBYWFggISFBWr4LAM2aNcPChQsBwOjApnr16vj8888xZswY/Pnnn+jatSvs7e1x/fp1bNq0CYMGDcLIkSOxd+9eREZGokePHqhVqxby8vKwcuVKKVgrEBwcjN27d2PWrFnw8PCAj49PocyVoT799FP897//RZs2bTB06FBpuXfVqlVx7949vbMUGzZsgJ2dHXJycqQ7Dx8+fBj16tWTlnADgK+vL6pXr46RI0fi9u3bcHBwwMaNG4ucxxYcHAwA+OijjxAWFgZzc3OEh4ejY8eOmDVrFtq1a4f3338fqampWLBgAWrUqKHzR5I+Pzd69eqFdevW4cMPP8S+ffsQGhoKjUaDP/74A+vWrcOOHTukx7+UxPtPJaBU1mJRqbl06ZIYOHCg8Pb2FlZWVsLe3l6EhoaK+fPn69yMKjc3V0yaNEn4+PgIS0tL4enp+cwb9D3t6WXGxS33FiL/Blp169YVVlZWonbt2uK///1voeXee/bsEV26dBEeHh7CyspKeHh4iPfee09cunSp0DmeXhK9e/duERoaKqytrYWDg4Po1KlTsTfoe3pZaFHLaIvy5HLv4hS33HvEiBGiUqVKwtraWoSGhor4+Pgil2n/+OOPwt/fX1hYWBR5g76iPNlOZmam8PLyEg0aNBC5ubk69YYPHy7MzMxEfHz8M6+huP/vlJQU0bdvX+Hi4iKsrKxEQEBAof+HZ30GDD3f5cuXhbm5eZFLpFNSUsSQIUOEp6ensLS0FGq1WrRq1UosWbKkUDsNGzYUAMSxY8ekslu3bgkAwtPTU+9+FijuBn0bN24UTZo0Eba2tsLW1lb4+vqKIUOGiISEBCGEENeuXRP9+vUT1atXFyqVSjg7O4uWLVuK3bt367Tzxx9/iGbNmglra2u9b9D3tKI+W6dOnRJNmzYVSqVSVKlSRcTExIh58+YJACI5OfmZ1/z0DThVKpWoUqWKePPNN8W3335b6GeGEEJcuHBBtG7dWtjZ2QkXFxcxcOBAcebMmULfv3l5eWLo0KHC1dVVKBQKnZ8Jy5YtEzVr1hRKpVL4+vqK5cuXv9DPDSHyl4pPnz5d1KlTRyiVSlGhQgURHBwsJk2aJDIyMp77/tOrRSHES5xFRkREZcLHH3+MxYsX4+HDh6X+uAYiQ3CODRFROff0E+nT0tKwcuVKNGnShEENlTmcY0NEVM6FhISgRYsW8PPzQ0pKCpYtW4bMzExERUWVdteIDMbAhoionOvQoQM2bNiAJUuWQKFQoEGDBli2bBmaNWtW2l0jMhjn2BAREZFscI4NERERyQYDGyIiIpINzrEpQ7RaLe7cuQN7e3ve2puIqIwRQuDBgwfw8PAo9NBYU8rKykJOTo5J2rKysoJKpTL4uGnTpmHMmDEYNmyYdBf7rKwsjBgxAmvWrEF2djbCwsLw9ddfw93dXTouMTERgwcPxr59+2BnZ4eIiAjExMTAwkL/cIWBTRly584dPpCNiKiMu3nz5gs/2PR5srKy4ONlh+RUzfMr60GtVuP69esGBTe//vorFi9eLD10tsDw4cOxdetWrF+/Ho6OjoiMjES3bt1w+PBhAPkPSO3YsSPUajWOHDmCpKQk9O7dG5aWlpg6dare5+fk4TIkIyMDTk5O8Fn8Ccyslc8/gKgM8ux9obS7QFQi8pCLX7AN6enpcHR0LJFzZGZmwtHRETdOeMPB3risUOYDLbyC/0RGRgYcHBz0Oubhw4do0KABvv76a3z++ecICgrCnDlzkJGRAVdXV6xevRpvv/02gPwH5fr5+SE+Ph6NGzfGzz//jDfffBN37tyRsjiLFi3C6NGjcffuXVhZWenVB2ZsypCC4SczayXMbQxPDRKVBRYKy9LuAlHJ+F8a4WVMJbCzV8DO3rjzaJF/fGZmpk65UqnUear6k4YMGYKOHTuidevW0lPpAeDEiRPIzc1F69atpTJfX19UrVpVCmzi4+MREBCgMzQVFhaGwYMH4/z586hfv75e/WZgQ0REJDMaoYXGyPEYjdACQKEpEBMmTCjyIcJr1qzByZMn8euvvxbal5ycDCsrKzg5OemUu7u7Izk5WarzZFBTsL9gn74Y2BAREcmMFgJaGBfZFBx/8+ZNnaGoorI1N2/exLBhw7Br164XmmxsSlzuTURERMVycHDQ2YoKbE6cOIHU1FQ0aNAAFhYWsLCwwIEDBzBv3jxYWFjA3d0dOTk5SE9P1zkuJSUFarUaQP5E5ZSUlEL7C/bpi4ENERGRzGhN9E9frVq1wrlz53D69Glpe+2119CzZ0/pa0tLS+zZs0c6JiEhAYmJiQgJCQGQ/8yyc+fOITU1Vaqza9cuODg4wN/fX+++cCiKiIhIZjRCQGPkomdDjre3t0fdunV1ymxtbVGxYkWpvH///vjkk0/g7OwMBwcHDB06FCEhIWjcuDEAoG3btvD390evXr0wY8YMJCcnY9y4cRgyZEixk5WLwsCGiIiIStzs2bNhZmaG7t2769ygr4C5uTni4uIwePBghISEwNbWFhEREYiOjjboPAxsiIiIZMaUk4df1P79+3Veq1QqLFiwAAsWLCj2GC8vL2zbts2o8zKwISIikhktBDSlHNiUFk4eJiIiItlgxoaIiEhmXoWhqNLCwIaIiEhmXvaqqFcJh6KIiIhINpixISIikhnt/zZj2yiLGNgQERHJjMYEq6KMPb60MLAhIiKSGY2ACZ7ubZq+vGycY0NERESywYwNERGRzHCODREREcmGFgpooDC6jbKIQ1FEREQkG8zYEBERyYxW5G/GtlEWMbAhIiKSGY0JhqKMPb60cCiKiIiIZIMZGyIiIpkpzxkbBjZEREQyoxUKaIWRq6KMPL60cCiKiIiIZIMZGyIiIpnhUBQRERHJhgZm0Bg5KKMxUV9eNgY2REREMiNMMMdGcI4NERERUelixoaIiEhmOMeGiIiIZEMjzKARRs6xKaOPVOBQFBEREckGMzZEREQyo4UCWiNzF1qUzZQNAxsiIiKZKc9zbDgURURERLLBjA0REZHMmGbyMIeiiIiI6BWQP8fGyIdgciiKiIiIqHQxY0NERCQzWhM8K4qrooiIiOiVwDk2REREJBtamJXb+9hwjg0REREZZeHChQgMDISDgwMcHBwQEhKCn3/+WdrfokULKBQKne3DDz/UaSMxMREdO3aEjY0N3NzcMGrUKOTl5RncF2ZsiIiIZEYjFNAII2/QZ8DxVapUwbRp01CzZk0IIbBixQp06dIFp06dQp06dQAAAwcORHR0tHSMjY3NP+fSaNCxY0eo1WocOXIESUlJ6N27NywtLTF16lSD+s3AhoiISGY0Jpg8rDFgKKpTp046r6dMmYKFCxfi6NGjUmBjY2MDtVpd5PE7d+7EhQsXsHv3bri7uyMoKAiTJ0/G6NGjMXHiRFhZWendFw5FERERkcloNBqsWbMGjx49QkhIiFS+atUquLi4oG7duhgzZgweP34s7YuPj0dAQADc3d2lsrCwMGRmZuL8+fMGnZ8ZGyIiIpnRCjNojVwVpf3fqqjMzEydcqVSCaVSWaj+uXPnEBISgqysLNjZ2WHTpk3w9/cHALz//vvw8vKCh4cHzp49i9GjRyMhIQE//PADACA5OVknqAEgvU5OTjao3wxsiIiIZMaUQ1Genp465RMmTMDEiRML1a9duzZOnz6NjIwMbNiwAREREThw4AD8/f0xaNAgqV5AQAAqVaqEVq1a4erVq6hevbpR/XwaAxsiIiIq1s2bN+Hg4CC9LipbAwBWVlaoUaMGACA4OBi//vor5s6di8WLFxeq26hRIwDAlStXUL16dajVahw/flynTkpKCgAUOy+nOJxjQ0REJDNa/LMy6kU37f/aKljCXbAVF9gU6oNWi+zs7CL3nT59GgBQqVIlAEBISAjOnTuH1NRUqc6uXbvg4OAgDWfpixkbIiIimTHNDfr0P37MmDFo3749qlatigcPHmD16tXYv38/duzYgatXr2L16tXo0KEDKlasiLNnz2L48OFo1qwZAgMDAQBt27aFv78/evXqhRkzZiA5ORnjxo3DkCFD9A6kCjCwISIiIqOkpqaid+/eSEpKgqOjIwIDA7Fjxw60adMGN2/exO7duzFnzhw8evQInp6e6N69O8aNGycdb25ujri4OAwePBghISGwtbVFRESEzn1v9MXAhoiISGZM86wo/Y9ftmxZsfs8PT1x4MCB57bh5eWFbdu26X3O4jCwISIikhktFNDCuDsPG3t8aWFgQ0REJDMvO2PzKimbvSYiIiIqAjM2REREMmOaG/SVzdwHAxsiIiKZ0QoFtEY+3dvY40tL2QzHiIiIiIrAjA0REZHMaE0wFGXsDf5KCwMbIiIimTHN073LZmBTNntNREREVARmbIiIiGRGAwU0Rt5gz9jjSwsDGyIiIpnhUBQRERGRDDBjQ0REJDMaGD+UpDFNV146BjZEREQyU56HohjYEBERyQwfgklEREQkA8zYEBERyYyAAloj59gILvcmIiKiVwGHooiIiIhkgBkbIiIimdEKBbTCuKEkY48vLQxsiIiIZEZjgqd7G3t8aSmbvSYiIiIqAjM2REREMsOhKCIiIpINLcygNXJQxtjjS0vZ7DURERFREZixISIikhmNUEBj5FCSsceXFgY2REREMsM5NkRERCQbwgRP9xa88zARERFR6WLGhoiISGY0UEBj5EMsjT2+tDCwISIikhmtMH6OjFaYqDMvGYeiiIiISDaYsaFyxWFTKqyPZcLydjaElQLZtW2R3lONvMpKqY7bhGtQXXikc9yDNs64P6iy9Nr8bg6cv7kD5fmHECozPGpeAek91YB52UzdUvnTqc9feHtwKpxd83DtgjW+HlcZCadtSrtbZCJaE0weNvb40lKqve7Tpw8UCgWmTZumU75582YoFIb9gvD29sacOXOeW+/MmTPo3Lkz3NzcoFKp4O3tjXfffRepqakGnY/KJuX5R3gYVhEpU6sjNcoHijwBt8+vQ5Gl1an3sFUF3FriK23pH6j/2akRcI35E8gTSPm8OtIiPWG7/z4c16a83IshekHNO9/HoAl3sGqWGkPCauHaBRWmrL4Gx4q5pd01MhEtFCbZyqJSD8dUKhWmT5+O+/fvl/i57t69i1atWsHZ2Rk7duzAxYsXsXz5cnh4eODRo0fPb4DKvLvjfPCoZQXkeqqQ622NtCFVYPFXLqyu/a1TT6s0g7aCpbQJG3Npn+rsQ1jeykbaR1WQ62ONrPr2yAh3h/32NCBX+/QpiV453Qb9he2rnbFzrTMSL6swb3QVZP+tQNh790q7a0RGK/XApnXr1lCr1YiJiXlmvY0bN6JOnTpQKpXw9vbGl19+Ke1r0aIFbty4geHDh0OhUBSb7Tl8+DAyMjKwdOlS1K9fHz4+PmjZsiVmz54NHx8fAMD+/fuhUCiwdetWBAYGQqVSoXHjxvj999+ldtLS0vDee++hcuXKsLGxQUBAAL7//nudc7Vo0QJDhw7Fxx9/jAoVKsDd3R3ffPMNHj16hL59+8Le3h41atTAzz///KJvHZmA2WMNAEBrZ65TbnsoHZX7XYD6k0twXJUMRfY/AYsy4TFyq6qgdbKUyv6uZw+zv7WwvJX9cjpO9IIsLLWoGfgYJw/ZS2VCKHDqkD38gx+XYs/IlAruPGzspq+FCxciMDAQDg4OcHBwQEhIiM7vt6ysLAwZMgQVK1aEnZ0dunfvjpQU3Sx3YmIiOnbsCBsbG7i5uWHUqFHIy8sz+NpLPbAxNzfH1KlTMX/+fNy6davIOidOnMA777yD8PBwnDt3DhMnTkRUVBRiY2MBAD/88AOqVKmC6OhoJCUlISkpqch21Go18vLysGnTJgjx7Oneo0aNwpdffolff/0Vrq6u6NSpE3Jz89O0WVlZCA4OxtatW/H7779j0KBB6NWrF44fP67TxooVK+Di4oLjx49j6NChGDx4MHr06IHXX38dJ0+eRNu2bdGrVy88fswfJqVCK1AhNglZtW2QW1UlFT9q4oS0jzyROqEaMt9yhe3B+6g476a03zw9Fxon3elp2v+9Nk83/JuQ6GVycNbA3AJIv6v7Gb7/lwUquPLzKxcFc2yM3fRVpUoVTJs2DSdOnMBvv/2GN954A126dMH58+cBAMOHD8eWLVuwfv16HDhwAHfu3EG3bt2k4zUaDTp27IicnBwcOXIEK1asQGxsLMaPH2/wtZd6YAMAb731FoKCgjBhwoQi98+aNQutWrVCVFQUatWqhT59+iAyMhIzZ84EADg7O8Pc3Bz29vZQq9VQq9VFttO4cWN89tlneP/99+Hi4oL27dtj5syZhaJGAJgwYQLatGmDgIAArFixAikpKdi0aRMAoHLlyhg5ciSCgoJQrVo1DB06FO3atcO6det02qhXrx7GjRuHmjVrYsyYMVCpVHBxccHAgQNRs2ZNjB8/HmlpaTh79myR/c3OzkZmZqbORqZTYekdWN7MQtrwqjrlj9o4IyvIHrleKjxuWgFpkZ6wOZ4Ji2RmY4iIitKpUyd06NABNWvWRK1atTBlyhTY2dnh6NGjyMjIwLJlyzBr1iy88cYbCA4OxvLly3HkyBEcPXoUALBz505cuHAB//3vfxEUFIT27dtj8uTJWLBgAXJycgzqyysR2ADA9OnTsWLFCly8eLHQvosXLyI0NFSnLDQ0FJcvX4ZGozHoPFOmTEFycjIWLVqEOnXqYNGiRfD19cW5c+d06oWEhEhfOzs7o3bt2lLfNBoNJk+ejICAADg7O8POzg47duxAYmKiThuBgYHS1+bm5qhYsSICAgKkMnd3dwAoduJyTEwMHB0dpc3T09Oga6XiVVh6G9YnHyBlQjVoKlo+s25OzfyVIhbJ+d9cGifLQpkZs/+9fjqTQ/SqybxnDk0e4PRUdqaCSx7u3+XnVy60UEjPi3rh7QUnD2s0GqxZswaPHj1CSEgITpw4gdzcXLRu3Vqq4+vri6pVqyI+Ph4AEB8fj4CAAOn3IgCEhYUhMzNTyvro65UJbJo1a4awsDCMGTOmxM9VsWJF9OjRA1988QUuXrwIDw8PfPHFF3ofP3PmTMydOxejR4/Gvn37cPr0aYSFhRWKKi0tdX9hKhQKnbKCuUBabdETTseMGYOMjAxpu3nzZpH1yABC5Ac1xzOROsEHGner5x5i+Wf+xGJNhfwf+tm1bWCZmAWzjH9+MajOPoTW2gy5VZRFtkH0qsjLNcPlszao3+SBVKZQCAQ1eYgLJ7jcWy6ECVZEif8FNk+PHGRnF529PnfuHOzs7KBUKvHhhx9i06ZN8Pf3R3JyMqysrODk5KRT393dHcnJyQCA5ORknaCmYH/BPkO8UuH5tGnTEBQUhNq1a+uU+/n54fDhwzplhw8fRq1atWBunj/p08rKyuDsTcFx1atXL7Qq6ujRo6haNX+I4v79+7h06RL8/Pykc3fp0gUffPABgPzA5NKlS/D39zf4/M+iVCqhVPIXpSlVWHoHtr+k4+6nXtCqzGB2P3/elLAxh1CawSI5Gza/pOPv+g7Q2pvD6kYWnFYkIcvPFrle1gCArEA75FZRouL8m0j/QA3z9Dw4rUnGg3YVActX5m8FomL9sMQFI+fcxKUzNkg4ZYO3Bt6FykaLnWucS7trZCKmfLr306MFEyZMwMSJEwvVr127Nk6fPo2MjAxs2LABEREROHDggFF9eBGvVGATEBCAnj17Yt68eTrlI0aMQMOGDTF58mS8++67iI+Px1dffYWvv/5aquPt7Y2DBw8iPDwcSqUSLi4uhdqPi4vDmjVrEB4ejlq1akEIgS1btmDbtm1Yvny5Tt3o6GhUrFgR7u7uGDt2LFxcXNC1a1cAQM2aNbFhwwYcOXIEFSpUwKxZs5CSkmLywIZMz35n/nJW94nXdcrT/lMFj1pWgLBQQHX2Eey3psEsW4u8ipb4u5EDMrq7/VPZXIG7Y7zh/M1tuI+9CqE0w6MWFZDxru5fG0SvqgM/VYBjRQ16j0pGBdc8XDtvjbE9fZD+17OHZal8unnzJhwcHKTXxf3BbWVlhRo1agAAgoOD8euvv2Lu3Ll49913kZOTg/T0dJ2sTUpKijQnVq1WF1qAUzD/tbh5s8V5pQIbID+gWLt2rU5ZgwYNsG7dOowfPx6TJ09GpUqVEB0djT59+ugc9+9//xvVq1dHdnZ2kaue/P39YWNjgxEjRuDmzZtQKpWoWbMmli5dil69eunUnTZtGoYNG4bLly8jKCgIW7ZsgZVV/rDFuHHjcO3aNYSFhcHGxgaDBg1C165dkZGRYfo3hEwqcX3AM/drXKyQGl3tue1oXK1w9zMfU3WL6KX7abkLflpe+A9AkgdT3nm4YAm3wcdrtcjOzkZwcDAsLS2xZ88edO/eHQCQkJCAxMREaT5rSEgIpkyZgtTUVLi55f8huWvXLjg4OBicNFCI5617Lmf279+Pli1b4v79+4XGA0tbZmYmHB0dUf27MTC3UT3/AKIyqGqPc8+vRFQG5Ylc7MePyMjIeKFAQR8Fvye67OwHS9vnzyF8ltxHOfix7bd69XfMmDFo3749qlatigcPHmD16tWYPn06duzYgTZt2mDw4MHYtm0bYmNj4eDggKFDhwIAjhw5AiB/wnFQUBA8PDwwY8YMJCcno1evXhgwYACmTp1qUL9fuYwNERERlS2pqano3bs3kpKS4OjoiMDAQCmoAYDZs2fDzMwM3bt3R3Z2NsLCwnSmk5ibmyMuLg6DBw9GSEgIbG1tERERgejoaIP7wsCGiIhIZkzxrCdDjl+2bNkz96tUKixYsAALFiwoto6Xlxe2bdum9zmLw8DmKS1atHjuXYmJiIheZaZcFVXWcG0qERERyQYzNkRERDJTnjM2DGyIiIhkpjwHNhyKIiIiItlgxoaIiEhmynPGhoENERGRzAgYtly7uDbKIgY2REREMlOeMzacY0NERESywYwNERGRzJTnjA0DGyIiIpkpz4ENh6KIiIhINpixISIikpnynLFhYENERCQzQiggjAxMjD2+tHAoioiIiGSDGRsiIiKZ0UJh9A36jD2+tDCwISIikpnyPMeGQ1FEREQkG8zYEBERyUx5njzMwIaIiEhmyvNQFAMbIiIimSnPGRvOsSEiIiLZYMaGiIhIZoQJhqLKasaGgQ0REZHMCABCGN9GWcShKCIiIpINZmyIiIhkRgsFFLzzMBEREckBV0URERERyQAzNkRERDKjFQooeIM+IiIikgMhTLAqqowui+JQFBEREckGMzZEREQyU54nDzOwISIikhkGNkRERCQb5XnyMOfYEBERkWwwsCEiIpKZglVRxm76iomJQcOGDWFvbw83Nzd07doVCQkJOnVatGgBhUKhs3344Yc6dRITE9GxY0fY2NjAzc0No0aNQl5enkHXzqEoIiIimckPTIydY6N/3QMHDmDIkCFo2LAh8vLy8Nlnn6Ft27a4cOECbG1tpXoDBw5EdHS09NrGxkb6WqPRoGPHjlCr1Thy5AiSkpLQu3dvWFpaYurUqXr3hYENERERGWX79u06r2NjY+Hm5oYTJ06gWbNmUrmNjQ3UanWRbezcuRMXLlzA7t274e7ujqCgIEyePBmjR4/GxIkTYWVlpVdfOBRFREQkMwWroozdACAzM1Nny87Ofu75MzIyAADOzs465atWrYKLiwvq1q2LMWPG4PHjx9K++Ph4BAQEwN3dXSoLCwtDZmYmzp8/r/e1M2NDREQkM+J/m7FtAICnp6dO+YQJEzBx4sRij9Nqtfj4448RGhqKunXrSuXvv/8+vLy84OHhgbNnz2L06NFISEjADz/8AABITk7WCWoASK+Tk5P17jcDGyIiIirWzZs34eDgIL1WKpXPrD9kyBD8/vvv+OWXX3TKBw0aJH0dEBCASpUqoVWrVrh69SqqV69usv5yKIqIiEhmTDkU5eDgoLM9K7CJjIxEXFwc9u3bhypVqjyzj40aNQIAXLlyBQCgVquRkpKiU6fgdXHzcorCwIaIiEhuhIk2fU8nBCIjI7Fp0ybs3bsXPj4+zz3m9OnTAIBKlSoBAEJCQnDu3DmkpqZKdXbt2gUHBwf4+/vr3RcORREREcmNCR6pAAOOHzJkCFavXo0ff/wR9vb20pwYR0dHWFtb4+rVq1i9ejU6dOiAihUr4uzZsxg+fDiaNWuGwMBAAEDbtm3h7++PXr16YcaMGUhOTsa4ceMwZMiQ5w5/PYkZGyIiIjLKwoULkZGRgRYtWqBSpUrStnbtWgCAlZUVdu/ejbZt28LX1xcjRoxA9+7dsWXLFqkNc3NzxMXFwdzcHCEhIfjggw/Qu3dvnfve6IMZGyIiIpkx9M7BxbWhf91nV/b09MSBAwee246Xlxe2bdum/4mLwMCGiIhIZsrz0705FEVERESywYwNERGR3AiFQZN/i22jDGJgQ0REJDMve47Nq4RDUURERCQbzNgQERHJjSkfFlXG6BXY/PTTT3o32Llz5xfuDBERERmvPK+K0iuw6dq1q16NKRQKaDQaY/pDRERE9ML0Cmy0Wm1J94OIiIhMqYwOJRnLqDk2WVlZUKlUpuoLERERmUB5HooyeFWURqPB5MmTUblyZdjZ2eHatWsAgKioKCxbtszkHSQiIiIDveSne79KDA5spkyZgtjYWMyYMQNWVlZSed26dbF06VKTdo6IiIjIEAYHNt999x2WLFmCnj17wtzcXCqvV68e/vjjD5N2joiIiF6EwkRb2WPwHJvbt2+jRo0ahcq1Wi1yc3NN0ikiIiIyQjm+j43BGRt/f38cOnSoUPmGDRtQv359k3SKiIiI6EUYnLEZP348IiIicPv2bWi1Wvzwww9ISEjAd999h7i4uJLoIxERERmCGRv9denSBVu2bMHu3btha2uL8ePH4+LFi9iyZQvatGlTEn0kIiIiQxQ83dvYrQx6ofvYNG3aFLt27TJ1X4iIiIiM8sI36Pvtt99w8eJFAPnzboKDg03WKSIiInpxQuRvxrZRFhkc2Ny6dQvvvfceDh8+DCcnJwBAeno6Xn/9daxZswZVqlQxdR+JiIjIEJxjo78BAwYgNzcXFy9exL1793Dv3j1cvHgRWq0WAwYMKIk+EhEREenF4IzNgQMHcOTIEdSuXVsqq127NubPn4+mTZuatHNERET0Akwx+be8TB729PQs8kZ8Go0GHh4eJukUERERvTiFyN+MbaMsMngoaubMmRg6dCh+++03qey3337DsGHD8MUXX5i0c0RERPQCyvFDMPXK2FSoUAEKxT8pqUePHqFRo0awsMg/PC8vDxYWFujXrx+6du1aIh0lIiIieh69Aps5c+aUcDeIiIjIZDjH5tkiIiJKuh9ERERkKuV4ufcL36APALKyspCTk6NT5uDgYFSHiIiIiF6UwZOHHz16hMjISLi5ucHW1hYVKlTQ2YiIiKiUlePJwwYHNp9++in27t2LhQsXQqlUYunSpZg0aRI8PDzw3XfflUQfiYiIyBDlOLAxeChqy5Yt+O6779CiRQv07dsXTZs2RY0aNeDl5YVVq1ahZ8+eJdFPIiIioucyOGNz7949VKtWDUD+fJp79+4BAJo0aYKDBw+atndERERkuIJVUcZuZZDBgU21atVw/fp1AICvry/WrVsHID+TU/BQTCIiIio9BXceNnYriwwObPr27YszZ84AAP7v//4PCxYsgEqlwvDhwzFq1CiTd5CIiIhIXwbPsRk+fLj0devWrfHHH3/gxIkTqFGjBgIDA03aOSIiInoB5fg+NgZnbJ7m5eWFbt26MaghIiIqp2JiYtCwYUPY29vDzc0NXbt2RUJCgk6drKwsDBkyBBUrVoSdnR26d++OlJQUnTqJiYno2LEjbGxs4ObmhlGjRiEvL8+gvuiVsZk3b57eDX700UcGdYCIiIhMSwETPN3bgLoHDhzAkCFD0LBhQ+Tl5eGzzz5D27ZtceHCBdja2gLIH/HZunUr1q9fD0dHR0RGRqJbt244fPgwAECj0aBjx45Qq9U4cuQIkpKS0Lt3b1haWmLq1Kn691sI8dxL9/Hx0a8xhQLXrl3T++RkmMzMTDg6OqL6d2NgbqMq7e4QlYiqPc6VdheISkSeyMV+/IiMjIwSu0t/we8Jr+mfw0xl3O8JbVYWbowe90L9vXv3Ltzc3HDgwAE0a9YMGRkZcHV1xerVq/H2228DAP744w/4+fkhPj4ejRs3xs8//4w333wTd+7cgbu7OwBg0aJFGD16NO7evQsrKyu9zq1XxqZgFRS9Gjx7X4CFwrK0u0FUInbcOV3aXSAqEZkPtKhQ6yWdzIQPwczMzNQpViqVUCqVzzw0IyMDAODs7AwAOHHiBHJzc9G6dWupjq+vL6pWrSoFNvHx8QgICJCCGgAICwvD4MGDcf78edSvX1+vbhs9x4aIiIheMSa887CnpyccHR2lLSYm5pmn1mq1+PjjjxEaGoq6desCAJKTk2FlZVXotjDu7u5ITk6W6jwZ1BTsL9inL6MegklERETydvPmTZ2hqOdla4YMGYLff/8dv/zyS0l3rUjM2BAREcmNCTM2Dg4OOtuzApvIyEjExcVh3759qFKlilSuVquRk5OD9PR0nfopKSlQq9VSnadXSRW8LqijDwY2REREMvOy7zwshEBkZCQ2bdqEvXv3Flp0FBwcDEtLS+zZs0cqS0hIQGJiIkJCQgAAISEhOHfuHFJTU6U6u3btgoODA/z9/fXuC4eiiIiIyChDhgzB6tWr8eOPP8Le3l6aE+Po6Ahra2s4Ojqif//++OSTT+Ds7AwHBwcMHToUISEhaNy4MQCgbdu28Pf3R69evTBjxgwkJydj3LhxGDJkyHOHv570QhmbQ4cO4YMPPkBISAhu374NAFi5cmWpjacRERHRE0w4FKWPhQsXIiMjAy1atEClSpWkbe3atVKd2bNn480330T37t3RrFkzqNVq/PDDD9J+c3NzxMXFwdzcHCEhIfjggw/Qu3dvREdHG3TpBmdsNm7ciF69eqFnz544deoUsrOzAeQv7Zo6dSq2bdtmaJNERERkSi/5kQp63BIPKpUKCxYswIIFC4qt4+XlZXQcYXDG5vPPP8eiRYvwzTffwNLyn3uphIaG4uTJk0Z1hoiIiMgYBmdsEhIS0KxZs0Lljo6OhWY7ExER0ctn6OTf4tooiwzO2KjValy5cqVQ+S+//IJq1aqZpFNERERkhII7Dxu7lUEGBzYDBw7EsGHDcOzYMSgUCty5cwerVq3CyJEjMXjw4JLoIxERERniJU8efpUYPBT1f//3f9BqtWjVqhUeP36MZs2aQalUYuTIkRg6dGhJ9JGIiIhILwYHNgqFAmPHjsWoUaNw5coVPHz4EP7+/rCzsyuJ/hEREZGByvMcmxe+QZ+VlZVBdwIkIiKil+QlL/d+lRgc2LRs2RIKRfETivbu3WtUh4iIiIhelMGBTVBQkM7r3NxcnD59Gr///jsiIiJM1S8iIiJ6USYYiio3GZvZs2cXWT5x4kQ8fPjQ6A4RERGRkcrxUJTJnu79wQcf4NtvvzVVc0REREQGM9nTvePj46FSqUzVHBEREb2ocpyxMTiw6datm85rIQSSkpLw22+/ISoqymQdIyIiohfD5d4GcHR01HltZmaG2rVrIzo6Gm3btjVZx4iIiIgMZVBgo9Fo0LdvXwQEBKBChQol1SciIiKiF2LQ5GFzc3O0bduWT/EmIiJ6lZXjZ0UZvCqqbt26uHbtWkn0hYiIiEygYI6NsVtZZHBg8/nnn2PkyJGIi4tDUlISMjMzdTYiIiKi0qL3HJvo6GiMGDECHTp0AAB07txZ59EKQggoFApoNBrT95KIiIgMU0YzLsbSO7CZNGkSPvzwQ+zbt68k+0NERETG4n1snk+I/Cts3rx5iXWGiIiIyBgGLfd+1lO9iYiI6NXAG/TpqVatWs8Nbu7du2dUh4iIiMhIHIrSz6RJkwrdeZiIiIjoVWFQYBMeHg43N7eS6gsRERGZAIei9MD5NURERGVEOR6K0vsGfQWrooiIiIheVXpnbLRabUn2g4iIiEylHGdsDJpjQ0RERK8+zrEhIiIi+SjHGRuDH4JJRERE9KpixoaIiEhuynHGhoENERGRzJTnOTYciiIiIiLZYMaGiIhIbsrxUBQzNkRERDJTMBRl7GaIgwcPolOnTvDw8IBCocDmzZt19vfp0wcKhUJna9eunU6de/fuoWfPnnBwcICTkxP69++Phw8fGtQPBjZERERktEePHqFevXpYsGBBsXXatWuHpKQkafv+++919vfs2RPnz5/Hrl27EBcXh4MHD2LQoEEG9YNDUURERHJTCkNR7du3R/v27Z9ZR6lUQq1WF7nv4sWL2L59O3799Ve89tprAID58+ejQ4cO+OKLL+Dh4aFXP5ixISIikhthog1AZmamzpadnf3C3dq/fz/c3NxQu3ZtDB48GGlpadK++Ph4ODk5SUENALRu3RpmZmY4duyY3udgYENERETF8vT0hKOjo7TFxMS8UDvt2rXDd999hz179mD69Ok4cOAA2rdvD41GAwBITk6Gm5ubzjEWFhZwdnZGcnKy3ufhUBQREZHMKP63GdsGANy8eRMODg5SuVKpfKH2wsPDpa8DAgIQGBiI6tWrY//+/WjVqpUxXdXBjA0REZHcmHAoysHBQWd70cDmadWqVYOLiwuuXLkCAFCr1UhNTdWpk5eXh3v37hU7L6coDGyIiIhkpjSWexvq1q1bSEtLQ6VKlQAAISEhSE9Px4kTJ6Q6e/fuhVarRaNGjfRul0NRREREZLSHDx9K2RcAuH79Ok6fPg1nZ2c4Oztj0qRJ6N69O9RqNa5evYpPP/0UNWrUQFhYGADAz88P7dq1w8CBA7Fo0SLk5uYiMjIS4eHheq+IApixISIikh8TDkXp67fffkP9+vVRv359AMAnn3yC+vXrY/z48TA3N8fZs2fRuXNn1KpVC/3790dwcDAOHTqkM7S1atUq+Pr6olWrVujQoQOaNGmCJUuWGNQPZmyIiIjk6CU/EqFFixYQoviT7tix47ltODs7Y/Xq1Ub1gxkbIiIikg1mbIiIiGTGFJN/S3rycElhYENERCQ3fLo3ERERUdnHjA0REZHMcCiKiIiI5INDUURERERlHzM2REREMsOhKCIiIpKPcjwUxcCGiIhIbspxYMM5NkRERCQbzNgQERHJDOfYEBERkXxwKIqIiIio7GPGhoiISGYUQkAhjEu5GHt8aWFgQ0REJDcciiIiIiIq+5ixISIikhmuiiIiIiL54FAUERERUdnHjA0REZHMcCiKiIiI5KMcD0UxsCEiIpKZ8pyx4RwbIiIikg1mbIiIiOSGQ1FEREQkJ2V1KMlYHIoiIiIi2WDGhoiISG6EyN+MbaMMYmBDREQkM1wVRURERCQDzNgQERHJDVdFERERkVwotPmbsW2URRyKIiIiItlgYENUjE59/sKKYxew5dpZzI27jNpBj0u7S0QGWzvfDWEeQVg4vrJUlpOlwFdjKuPtOnXRpUYAogd44/7dfxL4mffM8dn71fBe/Tp40zsQPYP98dVnlfHoAX9llBnCRFsZVO4+pXfv3sXgwYNRtWpVKJVKqNVqhIWF4fDhw6XdNXqFNO98H4Mm3MGqWWoMCauFaxdUmLL6Ghwr5pZ214j0lnDaGlv/WxE+/n/rlC+aWBlHdzli3OI/8cUPV3AvxRLR/b2l/QozICQsA5Nir2HZLxcxck4iTh2yx7zRni/5CuhFFayKMnYri8pdYNO9e3ecOnUKK1aswKVLl/DTTz+hRYsWSEtLK+2u0Suk26C/sH21M3audUbiZRXmja6C7L8VCHvvXml3jUgvfz8yw/RIL3w88ybsHTVS+aNMM+z43hn/nngbQU0eombg3/hkViIu/GaHiydsAAD2Thp0ikhDrXp/w71KLuo3fYhOEX/h92O2pXU5ZKiC+9gYuxng4MGD6NSpEzw8PKBQKLB58+anuiQwfvx4VKpUCdbW1mjdujUuX76sU+fevXvo2bMnHBwc4OTkhP79++Phw4cG9aNcBTbp6ek4dOgQpk+fjpYtW8LLywv/+te/MGbMGHTu3BkAoFAosHDhQrRv3x7W1taoVq0aNmzYoNPO6NGjUatWLdjY2KBatWqIiopCbu4/f8lPnDgRQUFB+Pbbb1G1alXY2dnhP//5DzQaDWbMmAG1Wg03NzdMmTLlpV4/6cfCUouagY9x8pC9VCaEAqcO2cM/mMNRVDZ89VkV/KtVJho00/2lcPmsDfJyzVC/6T/lVWtmw61yDi6eKDpwSUu2wOGfnRAYYtgvGCpfHj16hHr16mHBggVF7p8xYwbmzZuHRYsW4dixY7C1tUVYWBiysrKkOj179sT58+exa9cuxMXF4eDBgxg0aJBB/ShXq6Ls7OxgZ2eHzZs3o3HjxlAqlUXWi4qKwrRp0zB37lysXLkS4eHhOHfuHPz8/AAA9vb2iI2NhYeHB86dO4eBAwfC3t4en376qdTG1atX8fPPP2P79u24evUq3n77bVy7dg21atXCgQMHcOTIEfTr1w+tW7dGo0aNiuxHdnY2srOzpdeZmZkmfDeoOA7OGphbAOl3db897v9lAc8a2cUcRfTq2L/ZCVfOWWP+tkuF9t1LtYCllRZ2T2RxAMDJNRf3UnU/8zGDvRC/wxHZWWZo3CYDw7+4WaL9JtMpjRv0tW/fHu3bty9ynxACc+bMwbhx49ClSxcAwHfffQd3d3ds3rwZ4eHhuHjxIrZv345ff/0Vr732GgBg/vz56NChA7744gt4eHjo1Y9ylbGxsLBAbGwsVqxYAScnJ4SGhuKzzz7D2bNnder16NEDAwYMQK1atTB58mS89tprmD9/vrR/3LhxeP311+Ht7Y1OnTph5MiRWLdunU4bWq0W3377Lfz9/dGpUye0bNkSCQkJmDNnDmrXro2+ffuidu3a2LdvX7H9jYmJgaOjo7R5enJ8m4ieLfW2JRaOr4zRX92Alcq432z/nnQbX+1IwMTl13DnhhUWT6r8/IPo1WDCycOZmZk625N/cOvr+vXrSE5ORuvWraUyR0dHNGrUCPHx8QCA+Ph4ODk5SUENALRu3RpmZmY4duyY3ucqV4ENkD/H5s6dO/jpp5/Qrl077N+/Hw0aNEBsbKxUJyQkROeYkJAQXLx4UXq9du1ahIaGQq1Ww87ODuPGjUNiYqLOMd7e3rC3/2cow93dHf7+/jAzM9MpS01NLbavY8aMQUZGhrTdvMm/ll6GzHvm0OQBTq55OuUVXPJ0Vo4QvYqunLVB+l+WGBJWG+0966G9Zz2cjbfDj8tc0N6zHiq45iE3xwwPM8x1jku/awlnN93PvLNbHqrWzEZIWCaGTb+FuBUuSEvh90B54+npqfNHdkxMjMFtJCcnA8j/vfckd3d3aV9ycjLc3Nx09ltYWMDZ2Vmqo49y+QlVqVRo06YN2rRpg6ioKAwYMAATJkxAnz59nntsfHw8evbsiUmTJiEsLAyOjo5Ys2YNvvzyS516lpaWOq8VCkWRZVpt8XdAUiqVxQ6XUcnJyzXD5bM2qN/kAeK3OwIAFAqBoCYP8VNsxVLuHdGzBTV9gMV7/9Ap+3J4VXjWyMI7Q1Lh6pEDC0stTv1ih6YdMwAAN68okXrbCn7Bj4ptt2AeaW5Ouft7uEwy5VDUzZs34eDgIJW/6r+XymVg8zR/f3+d2dtHjx5F7969dV7Xr18fAHDkyBF4eXlh7Nix0v4bN268tL7Sy/HDEheMnHMTl87YIOGUDd4aeBcqGy12rnEu7a4RPZONnRbevlk6ZSobLewraKTysPfuYcnEyrB30sDWXoMFY6vAL/gR/P43Of74Hnvcv2uJ2kGPobLV4kaCCksne6BOw4dQe+a89GuiF2DCp3s7ODjoBDYvQq1WAwBSUlJQqVIlqTwlJQVBQUFSnadHMfLy8nDv3j3peH2Uq8AmLS0NPXr0QL9+/RAYGAh7e3v89ttvmDFjhjSZCQDWr1+P1157DU2aNMGqVatw/PhxLFu2DABQs2ZNJCYmYs2aNWjYsCG2bt2KTZs2ldYlUQk58FMFOFbUoPeoZFRwzcO189YY29MH6X9ZPv9golfchxNvw0whMHmgN3KzFXitxQNExtyS9lupBH5eVRGLJ1ZGbo4Crh45CG2fgXcjix86J3oWHx8fqNVq7NmzRwpkMjMzcezYMQwePBhA/rSP9PR0nDhxAsHBwQCAvXv3QqvVFrvIpijlKrCxs7NDo0aNMHv2bFy9ehW5ubnw9PTEwIED8dlnn0n1Jk2ahDVr1uA///kPKlWqhO+//x7+/v4AgM6dO2P48OGIjIxEdnY2OnbsiKioKEycOLGUropKyk/LXfDTcpfS7gaR0WZuvKLz2kolEBlzG5Ext4usHxT6EHO2XC5yH5UNpbEq6uHDh7hy5Z/P2vXr13H69Gk4OzujatWq+Pjjj/H555+jZs2a8PHxQVRUFDw8PNC1a1cAgJ+fH9q1a4eBAwdi0aJFyM3NRWRkJMLDw/VeEZXfb2FsrkpeFAoFNm3aJL3Rr5LMzEw4OjqiBbrAQsHMAcnTjjunS7sLRCUi84EWFWpdQ0ZGhtFDO8We43+/J0LaRcPCUmVUW3m5WYjfPl7v/u7fvx8tW7YsVB4REYHY2FgIITBhwgQsWbIE6enpaNKkCb7++mvUqlVLqnvv3j1ERkZiy5YtMDMzQ/fu3TFv3jzY2dnp3e9ylbEhIiKiktGiRQs8K1eiUCgQHR2N6OjoYus4Oztj9erVRvWDgQ0REZHMlMZQ1KuCgc1TODJHRERlnlbkb8a2UQYxsCEiIpKbJ+4cbFQbZRDvtERERESywYwNERGRzChggjk2JunJy8fAhoiISG5MeOfhsoZDUURERCQbzNgQERHJDJd7ExERkXxwVRQRERFR2ceMDRERkcwohIDCyMm/xh5fWhjYEBERyY32f5uxbZRBHIoiIiIi2WDGhoiISGY4FEVERETyUY5XRTGwISIikhveeZiIiIio7GPGhoiISGZ452EiIiKSDw5FEREREZV9zNgQERHJjEKbvxnbRlnEwIaIiEhuOBRFREREVPYxY0NERCQ3vEEfERERyUV5fqQCh6KIiIhINpixISIikptyPHmYgQ0REZHcCADGLtcum3ENAxsiIiK54RwbIiIiIhlgxoaIiEhuBEwwx8YkPXnpGNgQERHJTTmePMyhKCIiIpINZmyIiIjkRgtAYYI2yiAGNkRERDLDVVFEREREL2jixIlQKBQ6m6+vr7Q/KysLQ4YMQcWKFWFnZ4fu3bsjJSWlRPrCwIaIiEhuCiYPG7sZoE6dOkhKSpK2X375Rdo3fPhwbNmyBevXr8eBAwdw584ddOvWzdRXDYBDUURERPJTCquiLCwsoFarC5VnZGRg2bJlWL16Nd544w0AwPLly+Hn54ejR4+icePGxvXzKczYEBERUbEyMzN1tuzs7CLrXb58GR4eHqhWrRp69uyJxMREAMCJEyeQm5uL1q1bS3V9fX1RtWpVxMfHm7y/DGyIiIjkxoRDUZ6ennB0dJS2mJiYQqdr1KgRYmNjsX37dixcuBDXr19H06ZN8eDBAyQnJ8PKygpOTk46x7i7uyM5Odnkl86hKCIiIrkx4XLvmzdvwsHBQSpWKpWFqrZv3176OjAwEI0aNYKXlxfWrVsHa2trIztiGGZsiIiIZKZgubexGwA4ODjobEUFNk9zcnJCrVq1cOXKFajVauTk5CA9PV2nTkpKSpFzcozFwIaIiIhM6uHDh7h69SoqVaqE4OBgWFpaYs+ePdL+hIQEJCYmIiQkxOTn5lAUERGR3LzkVVEjR45Ep06d4OXlhTt37mDChAkwNzfHe++9B0dHR/Tv3x+ffPIJnJ2d4eDggKFDhyIkJMTkK6IABjZERETyoxWAwsjARqv/8bdu3cJ7772HtLQ0uLq6okmTJjh69ChcXV0BALNnz4aZmRm6d++O7OxshIWF4euvvzauf8VgYENERERGWbNmzTP3q1QqLFiwAAsWLCjxvjCwISIikptSuEHfq4KBDRERkeyYILBB2QxsuCqKiIiIZIMZGyIiIrnhUBQRERHJhlbA6KEkA1ZFvUo4FEVERESywYwNERGR3Aht/mZsG2UQAxsiIiK54RwbIiIikg3OsSEiIiIq+5ixISIikhsORREREZFsCJggsDFJT146DkURERGRbDBjQ0REJDcciiIiIiLZ0GoBGHkfGm3ZvI8Nh6KIiIhINpixISIikhsORREREZFslOPAhkNRREREJBvM2BAREclNOX6kAgMbIiIimRFCC2Hk07mNPb60MLAhIiKSGyGMz7hwjg0RERFR6WLGhoiISG6ECebYlNGMDQMbIiIiudFqAYWRc2TK6BwbDkURERGRbDBjQ0REJDcciiIiIiK5EFothJFDUWV1uTeHooiIiEg2mLEhIiKSGw5FERERkWxoBaAon4ENh6KIiIhINpixISIikhshABh7H5uymbFhYENERCQzQisgjByKEmU0sOFQFBERkdwIrWk2Ay1YsADe3t5QqVRo1KgRjh8/XgIX92wMbIiIiMhoa9euxSeffIIJEybg5MmTqFevHsLCwpCamvpS+8HAhoiISGaEVphkM8SsWbMwcOBA9O3bF/7+/li0aBFsbGzw7bffltBVFo2BDRERkdy85KGonJwcnDhxAq1bt5bKzMzM0Lp1a8THx5fEFRaLk4fLkIKJXHnINfq+S0SvqswHZfM27kTPk/kw/7P9MiblmuL3RB5yAQCZmZk65UqlEkqlUqfsr7/+gkajgbu7u065u7s7/vjjD+M6YiAGNmXIgwcPAAC/YFsp94So5FSoVdo9ICpZDx48gKOjY4m0bWVlBbVajV+STfN7ws7ODp6enjplEyZMwMSJE03SfklgYFOGeHh44ObNm7C3t4dCoSjt7sheZmYmPD09cfPmTTg4OJR2d4hMjp/xl0sIgQcPHsDDw6PEzqFSqXD9+nXk5OSYpD0hRKHfN09nawDAxcUF5ubmSElJ0SlPSUmBWq02SV/0xcCmDDEzM0OVKlVKuxvljoODA3/ok6zxM/7ylFSm5kkqlQoqlarEz/MkKysrBAcHY8+ePejatSsAQKvVYs+ePYiMjHypfWFgQ0REREb75JNPEBERgddeew3/+te/MGfOHDx69Ah9+/Z9qf1gYENERERGe/fdd3H37l2MHz8eycnJCAoKwvbt2wtNKC5pDGyIiqFUKjFhwoQix5OJ5ICfcTK1yMjIlz709DSFKKsPgyAiIiJ6Cm/QR0RERLLBwIaIiIhkg4ENERERyQYDGyIiIpINBjb0yurTpw8UCgWmTZumU75582aD77zs7e2NOXPmPLfemTNn0LlzZ7i5uUGlUsHb2xvvvvsuUlNTDTofUUm7e/cuBg8ejKpVq0KpVEKtViMsLAyHDx8u7a4RlSoGNvRKU6lUmD59Ou7fv1/i57p79y5atWoFZ2dn7NixAxcvXsTy5cvh4eGBR48elfj5iQzRvXt3nDp1CitWrMClS5fw008/oUWLFkhLSyvtrhGVLkH0ioqIiBBvvvmm8PX1FaNGjZLKN23aJJ7+6G7YsEH4+/sLKysr4eXlJb744gtpX/PmzQXyn3MrbUXZtGmTsLCwELm5ucX2ad++fQKAiIuLEwEBAUKpVIpGjRqJc+fOSXX++usvER4eLjw8PIS1tbWoW7euWL16tU47zZs3F5GRkWLYsGHCyclJuLm5iSVLloiHDx+KPn36CDs7O1G9enWxbds2g94zKh/u378vAIj9+/cXWweA+Prrr0W7du2ESqUSPj4+Yv369Tp1Pv30U1GzZk1hbW0tfHx8xLhx40ROTo60f8KECaJevXpi2bJlwtPTU9ja2orBgweLvLw8MX36dOHu7i5cXV3F559/XmLXSmQoZmzolWZubo6pU6di/vz5uHXrVpF1Tpw4gXfeeQfh4eE4d+4cJk6ciKioKMTGxgIAfvjhB1SpUgXR0dFISkpCUlJSke2o1Wrk5eVh06ZNEM+5vdOoUaPw5Zdf4tdff4Wrqys6deqE3NxcAEBWVhaCg4OxdetW/P777xg0aBB69eqF48eP67SxYsUKuLi44Pjx4xg6dCgGDx6MHj164PXXX8fJkyfRtm1b9OrVC48fPzbwXSO5s7Ozg52dHTZv3ozs7Oxi60VFRaF79+44c+YMevbsifDwcFy8eFHab29vj9jYWFy4cAFz587FN998g9mzZ+u0cfXqVfz888/Yvn07vv/+eyxbtgwdO3bErVu3cODAAUyfPh3jxo3DsWPHSux6iQxS2pEVUXEiIiJEly5dhBBCNG7cWPTr108IUThj8/7774s2bdroHDtq1Cjh7+8vvfby8hKzZ89+7jk/++wzYWFhIZydnUW7du3EjBkzRHJysrS/IGOzZs0aqSwtLU1YW1uLtWvXFttux44dxYgRI6TXzZs3F02aNJFe5+XlCVtbW9GrVy+pLCkpSQAQ8fHxz+03lT8bNmwQFSpUECqVSrz++utizJgx4syZM9J+AOLDDz/UOaZRo0Zi8ODBxbY5c+ZMERwcLL2eMGGCsLGxEZmZmVJZWFiY8Pb2FhqNRiqrXbu2iImJMcVlERmNGRsqE6ZPn44VK1bo/LVZ4OLFiwgNDdUpCw0NxeXLl6HRaAw6z5QpU5CcnIxFixahTp06WLRoEXx9fXHu3DmdeiEhIdLXzs7OqF27ttQ3jUaDyZMnIyAgAM7OzrCzs8OOHTuQmJio00ZgYKD0tbm5OSpWrIiAgACprOD5Kpy4TEXp3r077ty5g59++gnt2rXD/v370aBBAylTCeh+TgteP/k9tHbtWoSGhkKtVsPOzg7jxo0r9Dn19vaGvb299Nrd3R3+/v4wMzPTKePnlF4VDGyoTGjWrBnCwsIwZsyYEj9XxYoV0aNHD3zxxRe4ePEiPDw88MUXX+h9/MyZMzF37lyMHj0a+/btw+nTpxEWFoacnBydepaWljqvFQqFTlnByi+tVmvE1ZCcqVQqtGnTBlFRUThy5Aj69OmDCRMm6HVsfHw8evbsiQ4dOiAuLg6nTp3C2LFjDf6cFpTxc0qvCgY2VGZMmzYNW7ZsQXx8vE65n59foSWuhw8fRq1atWBubg4AsLKyMjh7U3Bc9erVC62KOnr0qPT1/fv3cenSJfj5+Unn7tKlCz744APUq1cP1apVw6VLlww+N5Gh/P39dT6rT35OC14XfE6PHDkCLy8vjB07Fq+99hpq1qyJGzduvNT+EpUEPt2byoyAgAD07NkT8+bN0ykfMWIEGjZsiMmTJ+Pdd99FfHw8vvrqK3z99ddSHW9vbxw8eBDh4eFQKpVwcXEp1H5cXBzWrFmD8PBw1KpVC0IIbNmyBdu2bcPy5ct16kZHR6NixYpwd3fH2LFj4eLigq5duwIAatasiQ0bNuDIkSOoUKECZs2ahZSUFPj7+5v+TaFyKS0tDT169EC/fv0QGBgIe3t7/Pbbb5gxYwa6dOki1Vu/fj1ee+01NGnSBKtWrcLx48exbNkyAPmf08TERKxZswYNGzbE1q1bsWnTptK6JCKTYcaGypTo6OhCKe8GDRpg3bp1WLNmDerWrYvx48cjOjoaffr00Tnuzz//RPXq1eHq6lpk2/7+/rCxscGIESMQFBSExo0bY926dVi6dCl69eqlU3fatGkYNmwYgoODkZycjC1btsDKygoAMG7cODRo0ABhYWFo0aIF1Gq1FPQQmYKdnR0aNWqE2bNno1mzZqhbty6ioqIwcOBAfPXVV1K9SZMmYc2aNQgMDMR3332H77//XgqwO3fujOHDhyMyMhJBQUE4cuQIoqKiSuuSiExGIcRz1rUSkWT//v1o2bIl7t+/Dycnp9LuDlGxFAoFNm3axKCayh1mbIiIiEg2GNgQERGRbHAoioiIiGSDGRsiIiKSDQY2REREJBsMbIiIiEg2GNgQERGRbDCwISKD9OnTR+feKC1atMDHH3/80vuxf/9+KBQKpKenF1tHoVBg8+bNerc5ceJEBAUFGdWvP//8EwqFAqdPnzaqHSJ6MQxsiGSgT58+UCgUUCgUsLKyQo0aNRAdHY28vLwSP/cPP/yAyZMn61VXn2CEiMgYfFYUkUy0a9cOy5cvR3Z2NrZt24YhQ4bA0tKyyCei5+TkSI+AMJazs7NJ2iEiMgVmbIhkQqlUQq1Ww8vLC4MHD0br1q3x008/Afhn+GjKlCnw8PBA7dq1AQA3b97EO++8AycnJzg7O6NLly74888/pTY1Gg0++eQTODk5oWLFivj000/x9K2vnh6Kys7OxujRo+Hp6QmlUokaNWpg2bJl+PPPP9GyZUsAQIUKFaBQKKTneWm1WsTExMDHxwfW1taoV68eNmzYoHOebdu2oVatWrC2tkbLli11+qmv0aNHo1atWrCxsUG1atUQFRWF3NzcQvUWL14MT09P2NjY4J133kFGRobO/qVLl8LPzw8qlQq+vr46D1wlotLFwIZIpqytrZGTkyO93rNnDxISErBr1y7ExcUhNzcXYWFhsLe3x6FDh3D48GHY2dmhXbt20nFffvklYmNj8e233+KXX37BvXv3nvsE6N69e+P777/HvHnzcPHiRSxevBh2dnbw9PTExo0bAQAJCQlISkrC3LlzAQAxMTH47rvvsGjRIpw/fx7Dhw/HBx98gAMHDgDID8C6deuGTp064fTp0xgwYAD+7//+z+D3xN7eHrGxsbhw4QLmzp2Lb775BrNnz9apc+XKFaxbtw5btmzB9u3bcerUKfznP/+R9q9atQrjx4/HlClTcPHiRUydOhVRUVFYsWKFwf0hohIgiKjMi4iIEF26dBFCCKHVasWuXbuEUqkUI0eOlPa7u7uL7Oxs6ZiVK1eK2rVrC61WK5VlZ2cLa2trsWPHDiGEEJUqVRIzZsyQ9ufm5ooqVapI5xJCiObNm4thw4YJIYRISEgQAMSuXbuK7Oe+ffsEAHH//n2pLCsrS9jY2IgjR47o1O3fv7947733hBBCjBkzRvj7++vsHz16dKG2ngZAbNq0qdj9M2fOFMHBwdLrCRMmCHNzc3Hr1i2p7OeffxZmZmYiKSlJCCFE9erVxerVq3XamTx5sggJCRFCCHH9+nUBQJw6darY8xJRyeEcGyKZiIuLg52dHXJzc6HVavH+++9j4sSJ0v6AgACdeTVnzpzBlStXYG9vr9NOVlYWrl69ioyMDCQlJaFRo0bSPgsLC7z22muFhqMKnD59Gubm5mjevLne/b5y5QoeP36MNm3a6JTn5OSgfv36AICLFy/q9AMAQkJC9D5HgbVr12LevHm4evUqHj58iLy8PDg4OOjUqVq1KipXrqxzHq1Wi4SEBNjb2+Pq1avo378/Bg4cKNXJy8uDo6Ojwf0hItNjYEMkEy1btsTChQthZWUFDw8PWFjofnvb2trqvH748CGCg4OxatWqQm25urq+UB+sra0NPubhw4cAgK1bt+oEFED+vCFTiY+PR8+ePTFp0iSEhYXB0dERa9aswZdffmlwX7/55ptCgZa5ubnJ+kpEL46BDZFM2NraokaNGnrXb9CgAdauXQs3N7dCWYsClSpVwrFjx9CsWTMA+ZmJEydOoEGDBkXWDwgIgFarxYEDB9C6detC+wsyRhqNRirz9/eHUqlEYmJisZkePz8/aSJ0gaNHjz7/Ip9w5MgReHl5YezYsVLZjRs3CtVLTEzEnTt34OHhIZ3HzMwMtWvXhru7Ozw8PHDt2jX07NnToPMT0cvBycNE5VTPnj3h4uKCLl264NChQ7h+/Tr279+Pjz76CLdu3QIADBs2DNOmTcPmzZvxxx9/4D//+c8z70Hj7e2NiIgI9OvXD5s3b5baXLduHQDAy8sLCoUCcXFxuHv3Lh4+fAh7e3uMHDkSw4cPx4oVK3D16lWcPHkS8+fPlybkfvjhh7h8+TJGjRqFhIQErF69GrGxsQZdb82aNZGYmIg1a9bg6tWrmDdvXpEToVUqFSIiInDmzBkcOnQIH330Ed555x2o1WoAwKRJkxATE4N58+bh0qVLOHfuHJYvX45Zs2YZ1B8iKhkMbIjKKRsbGxw8eBBVq1ZFt27d4Ofnh/79+yMrK0vK4IwYMQK9evVCREQEQkJCYG9vj7feeuuZ7S5cuBBvv/02/vOf/8DX1xcDBw7Eo0ePAACVK1fGpEmT8H//939wd3dHZGQkAGDy5MmIiopCTEwM/Pz80K5dO2zduhU+Pj4A8ue9bNy4EZs3b0a9evWwaNEiTJ061aDr7dy5M4YPH47IyEgEBQXhyJEjiIqKKlSvRo0a6NatGzp06IC2bdsiMDBQZzn3gAEDsHTpUixfvhwBAQFo3rw5YmNjpb4SUelSiOJmARIRERGVMczYEBERkWwwsCEiIiLZYGBDREREssHAhoiIiGSDgQ0RERHJBgMbIiIikg0GNkRERCQbDGyIiIhINhjYEBERkWwwsCEiIiLZYGBDREREssHAhoiIiGTj/wHEQAT48PyKKgAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Convert the new test data into TF-IDF feature vectors\n", "X_new_test = tfidf_vectorizer.transform(new_test_texts)\n", "\n", "# Predict using the new test dataset\n", "new_test_predictions = model.predict(X_new_test)\n", "\n", "# Display the results\n", "new_test_data['Prediction'] = new_test_predictions\n", "print(new_test_data[['Comment', 'Prediction']])\n", "\n", "# Confusion Matrix for the new testing dataset\n", "new_test_conf_matrix = confusion_matrix(new_test_data['Prediction'], new_test_predictions)\n", "new_test_accuracy = accuracy_score(new_test_data['Prediction'], new_test_predictions)\n", "new_test_precision = precision_score(new_test_data['Prediction'], new_test_predictions, average='weighted')\n", "new_test_recall = recall_score(new_test_data['Prediction'], new_test_predictions, average='weighted')\n", "new_test_f1 = f1_score(new_test_data['Prediction'], new_test_predictions, average='weighted')\n", "\n", "print('Confusion Matrix for New Testing Dataset:')\n", "print(new_test_conf_matrix)\n", "print(f'Testing Accuracy: {new_test_accuracy:.2f}')\n", "print(f'Testing Precision: {new_test_precision:.2f}')\n", "print(f'Testing Recall: {new_test_recall:.2f}')\n", "print(f'Testing F1 Score: {new_test_f1:.2f}')\n", "\n", "# Display Confusion Matrix for the new testing dataset\n", "disp_new = ConfusionMatrixDisplay(confusion_matrix=new_test_conf_matrix, display_labels=['Not Spam', 'Spam'])\n", "disp_new.plot()\n", "plt.title('Confusion Matrix for New Testing Dataset')\n", "plt.show()\n", "\n", "# Optionally, save the predictions to a CSV file\n", "new_test_data.to_csv('new_test_predictions.csv', index=False)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}