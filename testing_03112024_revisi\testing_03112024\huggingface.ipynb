{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "API_URL = \"https://api-inference.huggingface.co/models/Qwen/Qwen2.5-Coder-32B-Instruct-GGUF\"\n", "headers = {\"Authorization\": \"Bearer *************************************\"}\n", "\n", "def query(payload):\n", "\tresponse = requests.post(API_URL, headers=headers, json=payload)\n", "\treturn response.json()\n", "\t\n", "output = query({\n", "\t\"inputs\": \"Can you please let us know more details about your \",\n", "})"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'error': 'Model Qwen/Qwen2.5-Coder-32B-Instruct-GGUF is currently loading',\n", " 'estimated_time': 20.0}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["output"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}