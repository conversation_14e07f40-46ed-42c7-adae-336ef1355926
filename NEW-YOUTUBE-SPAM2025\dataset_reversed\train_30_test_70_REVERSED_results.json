{"naive_bayes": {"accuracy": 0.875, "classification_report": {"0": {"precision": 0.9115191986644408, "recall": 0.8285280728376327, "f1-score": 0.8680445151033387, "support": 659.0}, "1": {"precision": 0.8449931412894376, "recall": 0.9207772795216741, "f1-score": 0.8812589413447782, "support": 669.0}, "accuracy": 0.875, "macro avg": {"precision": 0.8782561699769391, "recall": 0.8746526761796534, "f1-score": 0.8746517282240585, "support": 1328.0}, "weighted avg": {"precision": 0.8780056953633284, "recall": 0.875, "f1-score": 0.8747014813349072, "support": 1328.0}}, "confusion_matrix": [[546, 113], [53, 616]], "train_size": 568, "test_size": 1328, "train_test_ratio": 0.42771084337349397}, "logistic_regression": {"accuracy": 0.9141566265060241, "classification_report": {"0": {"precision": 0.8854314002828855, "recall": 0.9499241274658573, "f1-score": 0.9165446559297218, "support": 659.0}, "1": {"precision": 0.9468599033816425, "recall": 0.8789237668161435, "f1-score": 0.9116279069767442, "support": 669.0}, "accuracy": 0.9141566265060241, "macro avg": {"precision": 0.9161456518322639, "recall": 0.9144239471410004, "f1-score": 0.914086281453233, "support": 1328.0}, "weighted avg": {"precision": 0.9163769338469431, "recall": 0.9141566265060241, "f1-score": 0.9140677695972355, "support": 1328.0}}, "confusion_matrix": [[626, 33], [81, 588]], "train_size": 568, "test_size": 1328, "train_test_ratio": 0.42771084337349397}, "random_forest": {"accuracy": 0.8900602409638554, "classification_report": {"0": {"precision": 0.8234552332912989, "recall": 0.9908952959028832, "f1-score": 0.8994490358126722, "support": 659.0}, "1": {"precision": 0.9887850467289719, "recall": 0.7907324364723468, "f1-score": 0.8787375415282392, "support": 669.0}, "accuracy": 0.8900602409638554, "macro avg": {"precision": 0.9061201400101354, "recall": 0.890813866187615, "f1-score": 0.8890932886704557, "support": 1328.0}, "weighted avg": {"precision": 0.9067426167173555, "recall": 0.8900602409638554, "f1-score": 0.8890153086467943, "support": 1328.0}}, "confusion_matrix": [[653, 6], [140, 529]], "train_size": 568, "test_size": 1328, "train_test_ratio": 0.42771084337349397}, "svm": {"accuracy": 0.9118975903614458, "classification_report": {"0": {"precision": 0.8904899135446686, "recall": 0.9377845220030349, "f1-score": 0.9135254988913526, "support": 659.0}, "1": {"precision": 0.9353312302839116, "recall": 0.8863976083707026, "f1-score": 0.9102072141212586, "support": 669.0}, "accuracy": 0.9118975903614458, "macro avg": {"precision": 0.91291057191429, "recall": 0.9120910651868688, "f1-score": 0.9118663565063057, "support": 1328.0}, "weighted avg": {"precision": 0.9130794021730976, "recall": 0.9118975903614458, "f1-score": 0.9118538629642494, "support": 1328.0}}, "confusion_matrix": [[618, 41], [76, 593]], "train_size": 568, "test_size": 1328, "train_test_ratio": 0.42771084337349397}}