{"dataset_info": {"total_samples": 1896, "features": ["CONTENT"], "target": "CLASS", "class_distribution": {"1": 955, "0": 941}}, "training_timestamp": "2025-06-30T00:04:23.785317", "splits_trained": ["train_75_test_25", "train_70_test_30", "train_65_test_35"], "models_trained": ["naive_bayes", "logistic_regression", "random_forest", "svm"], "results": {"train_75_test_25": {"naive_bayes": {"accuracy": 0.8839662447257384, "classification_report": {"0": {"precision": 0.9128440366972477, "recall": 0.8468085106382979, "f1-score": 0.8785871964679912, "support": 235.0}, "1": {"precision": 0.859375, "recall": 0.9205020920502092, "f1-score": 0.8888888888888888, "support": 239.0}, "accuracy": 0.8839662447257384, "macro avg": {"precision": 0.8861095183486238, "recall": 0.8836553013442535, "f1-score": 0.88373804267844, "support": 474.0}, "weighted avg": {"precision": 0.8858839105988463, "recall": 0.8839662447257384, "f1-score": 0.8837815097350684, "support": 474.0}}, "confusion_matrix": [[199, 36], [19, 220]], "train_size": 1422, "test_size": 474}, "logistic_regression": {"accuracy": 0.9240506329113924, "classification_report": {"0": {"precision": 0.8932806324110671, "recall": 0.9617021276595744, "f1-score": 0.9262295081967213, "support": 235.0}, "1": {"precision": 0.9592760180995475, "recall": 0.8870292887029289, "f1-score": 0.9217391304347826, "support": 239.0}, "accuracy": 0.9240506329113924, "macro avg": {"precision": 0.9262783252553073, "recall": 0.9243657081812516, "f1-score": 0.923984319315752, "support": 474.0}, "weighted avg": {"precision": 0.9265567867982967, "recall": 0.9240506329113924, "f1-score": 0.9239653725741404, "support": 474.0}}, "confusion_matrix": [[226, 9], [27, 212]], "train_size": 1422, "test_size": 474}, "random_forest": {"accuracy": 0.919831223628692, "classification_report": {"0": {"precision": 0.8803088803088803, "recall": 0.9702127659574468, "f1-score": 0.9230769230769231, "support": 235.0}, "1": {"precision": 0.9674418604651163, "recall": 0.8702928870292888, "f1-score": 0.9162995594713657, "support": 239.0}, "accuracy": 0.919831223628692, "macro avg": {"precision": 0.9238753703869983, "recall": 0.9202528264933678, "f1-score": 0.9196882412741444, "support": 474.0}, "weighted avg": {"precision": 0.9242430200922989, "recall": 0.919831223628692, "f1-score": 0.9196596448032349, "support": 474.0}}, "confusion_matrix": [[228, 7], [31, 208]], "train_size": 1422, "test_size": 474}, "svm": {"accuracy": 0.930379746835443, "classification_report": {"0": {"precision": 0.904, "recall": 0.9617021276595744, "f1-score": 0.931958762886598, "support": 235.0}, "1": {"precision": 0.9598214285714286, "recall": 0.899581589958159, "f1-score": 0.9287257019438445, "support": 239.0}, "accuracy": 0.930379746835443, "macro avg": {"precision": 0.9319107142857144, "recall": 0.9306418588088667, "f1-score": 0.9303422324152213, "support": 474.0}, "weighted avg": {"precision": 0.9321462477396022, "recall": 0.930379746835443, "f1-score": 0.9303285908078678, "support": 474.0}}, "confusion_matrix": [[226, 9], [24, 215]], "train_size": 1422, "test_size": 474}}, "train_70_test_30": {"naive_bayes": {"accuracy": 0.8822495606326889, "classification_report": {"0": {"precision": 0.9056603773584906, "recall": 0.851063829787234, "f1-score": 0.8775137111517367, "support": 282.0}, "1": {"precision": 0.8618421052631579, "recall": 0.9128919860627178, "f1-score": 0.8866328257191202, "support": 287.0}, "accuracy": 0.8822495606326889, "macro avg": {"precision": 0.8837512413108242, "recall": 0.8819779079249759, "f1-score": 0.8820732684354284, "support": 569.0}, "weighted avg": {"precision": 0.8835587181469606, "recall": 0.8822495606326889, "f1-score": 0.8821133348438968, "support": 569.0}}, "confusion_matrix": [[240, 42], [25, 262]], "train_size": 1327, "test_size": 569}, "logistic_regression": {"accuracy": 0.9226713532513181, "classification_report": {"0": {"precision": 0.8914473684210527, "recall": 0.9609929078014184, "f1-score": 0.9249146757679181, "support": 282.0}, "1": {"precision": 0.9584905660377359, "recall": 0.8850174216027874, "f1-score": 0.9202898550724637, "support": 287.0}, "accuracy": 0.9226713532513181, "macro avg": {"precision": 0.9249689672293943, "recall": 0.9230051647021029, "f1-score": 0.9226022654201909, "support": 569.0}, "weighted avg": {"precision": 0.9252635331240194, "recall": 0.9226713532513181, "f1-score": 0.9225819454698595, "support": 569.0}}, "confusion_matrix": [[271, 11], [33, 254]], "train_size": 1327, "test_size": 569}, "random_forest": {"accuracy": 0.929701230228471, "classification_report": {"0": {"precision": 0.8954248366013072, "recall": 0.9716312056737588, "f1-score": 0.9319727891156463, "support": 282.0}, "1": {"precision": 0.9695817490494296, "recall": 0.8885017421602788, "f1-score": 0.9272727272727272, "support": 287.0}, "accuracy": 0.929701230228471, "macro avg": {"precision": 0.9325032928253685, "recall": 0.9300664739170188, "f1-score": 0.9296227581941867, "support": 569.0}, "weighted avg": {"precision": 0.9328291140575657, "recall": 0.929701230228471, "f1-score": 0.9296021076588487, "support": 569.0}}, "confusion_matrix": [[274, 8], [32, 255]], "train_size": 1327, "test_size": 569}, "svm": {"accuracy": 0.929701230228471, "classification_report": {"0": {"precision": 0.9060402684563759, "recall": 0.9574468085106383, "f1-score": 0.9310344827586207, "support": 282.0}, "1": {"precision": 0.955719557195572, "recall": 0.9024390243902439, "f1-score": 0.9283154121863799, "support": 287.0}, "accuracy": 0.929701230228471, "macro avg": {"precision": 0.9308799128259739, "recall": 0.9299429164504411, "f1-score": 0.9296749474725003, "support": 569.0}, "weighted avg": {"precision": 0.9310981873810671, "recall": 0.929701230228471, "f1-score": 0.9296630007652409, "support": 569.0}}, "confusion_matrix": [[270, 12], [28, 259]], "train_size": 1327, "test_size": 569}}, "train_65_test_35": {"naive_bayes": {"accuracy": 0.8810240963855421, "classification_report": {"0": {"precision": 0.9009584664536742, "recall": 0.8545454545454545, "f1-score": 0.8771384136858476, "support": 330.0}, "1": {"precision": 0.8632478632478633, "recall": 0.907185628742515, "f1-score": 0.8846715328467153, "support": 334.0}, "accuracy": 0.8810240963855421, "macro avg": {"precision": 0.8821031648507687, "recall": 0.8808655416439848, "f1-score": 0.8809049732662815, "support": 664.0}, "weighted avg": {"precision": 0.8819895786965343, "recall": 0.8810240963855421, "f1-score": 0.8809276633842359, "support": 664.0}}, "confusion_matrix": [[282, 48], [31, 303]], "train_size": 1232, "test_size": 664}, "logistic_regression": {"accuracy": 0.9141566265060241, "classification_report": {"0": {"precision": 0.8802228412256268, "recall": 0.9575757575757575, "f1-score": 0.9172714078374455, "support": 330.0}, "1": {"precision": 0.9540983606557377, "recall": 0.8712574850299402, "f1-score": 0.9107981220657277, "support": 334.0}, "accuracy": 0.9141566265060241, "macro avg": {"precision": 0.9171606009406823, "recall": 0.9144166213028488, "f1-score": 0.9140347649515866, "support": 664.0}, "weighted avg": {"precision": 0.9173831175654718, "recall": 0.9141566265060241, "f1-score": 0.9140152671028767, "support": 664.0}}, "confusion_matrix": [[316, 14], [43, 291]], "train_size": 1232, "test_size": 664}, "random_forest": {"accuracy": 0.9066265060240963, "classification_report": {"0": {"precision": 0.8681318681318682, "recall": 0.9575757575757575, "f1-score": 0.9106628242074928, "support": 330.0}, "1": {"precision": 0.9533333333333334, "recall": 0.8562874251497006, "f1-score": 0.9022082018927445, "support": 334.0}, "accuracy": 0.9066265060240963, "macro avg": {"precision": 0.9107326007326008, "recall": 0.906931591362729, "f1-score": 0.9064355130501187, "support": 664.0}, "weighted avg": {"precision": 0.9109892316518824, "recall": 0.9066265060240963, "f1-score": 0.9064100473202551, "support": 664.0}}, "confusion_matrix": [[316, 14], [48, 286]], "train_size": 1232, "test_size": 664}, "svm": {"accuracy": 0.9231927710843374, "classification_report": {"0": {"precision": 0.9020172910662824, "recall": 0.9484848484848485, "f1-score": 0.9246676514032496, "support": 330.0}, "1": {"precision": 0.9463722397476341, "recall": 0.8982035928143712, "f1-score": 0.9216589861751152, "support": 334.0}, "accuracy": 0.9231927710843374, "macro avg": {"precision": 0.9241947654069582, "recall": 0.9233442206496099, "f1-score": 0.9231633187891823, "support": 664.0}, "weighted avg": {"precision": 0.9243283646499745, "recall": 0.9231927710843374, "f1-score": 0.9231542565445194, "support": 664.0}}, "confusion_matrix": [[313, 17], [34, 300]], "train_size": 1232, "test_size": 664}}}}