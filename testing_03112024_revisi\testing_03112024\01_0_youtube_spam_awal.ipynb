{"cells": [{"cell_type": "code", "execution_count": 103, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import os\n", "import re\n", "import joblib\n", "import xgboost as xgb\n", "from sklearn.model_selection import train_test_split, cross_val_score\n", "from sklearn.naive_bayes import GaussianNB\n", "from sklearn.svm import SVC\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import classification_report, accuracy_score, confusion_matrix, recall_score, precision_score, f1_score\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.feature_extraction.text import ENGLISH_STOP_WORDS\n", "\n", "\n", "# Create directory if not exists\n", "output_folder = 'awal'\n", "os.makedirs(output_folder, exist_ok=True)\n"]}, {"cell_type": "code", "execution_count": 104, "metadata": {}, "outputs": [], "source": ["# Function to preprocess text data\n", "def preprocess_text(text):\n", "    # Remove URLs, special characters, numbers, and make lowercase\n", "    text = re.sub(r\"http\\S+|www\\S+|https\\S+\", '', text, flags=re.MULTILINE)\n", "    text = re.sub(r'[^A-Za-z\\s]', '', text)\n", "    text = text.lower()\n", "    \n", "    # Tokenize and remove stopwords\n", "    tokens = text.split()\n", "    cleaned_tokens = [word for word in tokens if word not in ENGLISH_STOP_WORDS]\n", "    \n", "    # Join tokens back to string\n", "    cleaned_text = ' '.join(cleaned_tokens)\n", "    return cleaned_text"]}, {"cell_type": "code", "execution_count": 105, "metadata": {}, "outputs": [], "source": ["# Function to load and preprocess training dataset\n", "def load_training_data():\n", "    # Load training dataset\n", "    train_val_dataset = pd.read_csv('../dataset/youtube_spam.csv')  # Load the uploaded training and validation dataset\n", "\n", "    # Preprocess 'CONTENT' column\n", "    train_val_dataset['clean_content'] = train_val_dataset['CONTENT'].apply(preprocess_text)\n", "    \n", "    # Use only 'CONTENT' as features and 'CLASS' as the target variable\n", "    X_train_val_raw = train_val_dataset['clean_content']  # Features (content of the comments)\n", "    y_train_val = train_val_dataset['CLASS']        # Target variable (spam or not spam)\n", "\n", "    return X_train_val_raw, y_train_val"]}, {"cell_type": "code", "execution_count": 106, "metadata": {}, "outputs": [], "source": ["\n", "# Function to perform feature extraction using TF-IDF\n", "def feature_extraction(X_train_val_raw):\n", "    # Convert text data to numerical data using TF-IDF Vectorizer\n", "    tfidf = TfidfVectorizer(stop_words='english', max_features=5000)\n", "    X_train_val = tfidf.fit_transform(X_train_val_raw)\n", "    return X_train_val, tfidf\n", "\n", "# Function to load and preprocess testing dataset\n", "def load_testing_data(tfidf):\n", "    # Load and preprocess the testing dataset\n", "    test_dataset = pd.read_excel('../dataset/youtube_comments_i6IOiUi6IYY.xlsx')  # Load the testing dataset\n", "    test_dataset['cleaned_comment'] = test_dataset['Comment'].apply(preprocess_text)  # Preprocess 'Comment' column\n", "    X_test_raw = test_dataset['cleaned_comment']  # Use the cleaned comments for testing\n", "    X_test = tfidf.transform(X_test_raw)  # Transform the test dataset using the same TF-IDF Vectorizer\n", "\n", "    # Add original and cleaned comments to dataframe for analysis\n", "    df = pd.DataFrame({'comment': test_dataset['Comment'], 'clean_comment': test_dataset['cleaned_comment']})\n", "\n", "    # Save the processed test dataset to CSV and Excel\n", "    test_dataset.to_csv(f'{output_folder}/processed_test_dataset.csv', index=False)\n", "    test_dataset.to_excel(f'{output_folder}/processed_test_dataset.xlsx', index=False)\n", "    print(f\"Processed test dataset saved to '{output_folder}/processed_test_dataset.csv' and '{output_folder}/processed_test_dataset.xlsx'\")\n", "\n", "    # return X_test\n", "    return X_test, df\n"]}, {"cell_type": "code", "execution_count": 107, "metadata": {}, "outputs": [], "source": ["\n", "# Function to initialize models\n", "def initialize_models():\n", "    return {\n", "        'Naive Bayes': GaussianNB(),\n", "        'SVM (linear kernel)': SVC(kernel='linear'),\n", "        'SVM (poly kernel)': SVC(kernel='poly'),\n", "        'SVM (rbf kernel)': SVC(kernel='rbf'),\n", "        'SVM (sigmoid kernel)': SVC(kernel='sigmoid'),\n", "        'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),\n", "        'Logistic Regression': LogisticRegression(max_iter=1000, random_state=42),\n", "        'XGBoost': xgb.XGBClassifier(use_label_encoder=False, eval_metric='mlogloss', random_state=42)\n", "    }\n"]}, {"cell_type": "code", "execution_count": 108, "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import cross_val_score\n", "# Function to train and validate models\n", "def train_and_validate_models(models, X_train, X_val, y_train, y_val, test_size):\n", "    validation_results = []\n", "    metrics_results = []\n", "\n", "    print(\"\\nTraining and Validation Phase\\n\")\n", "    for model_name, model in models.items():\n", "        # Apply cross-validation\n", "        # scores = cross_val_score(model, X_train.toarray(), y_train, cv=5, scoring='accuracy')\n", "        # average_accuracy = scores.mean()\n", "\n", "        # Train the model\n", "        model.fit(X_train.toarray(), y_train)\n", "        \n", "        # Save the trained model\n", "        model_filename = f'{output_folder}/{model_name.replace(\" \", \"_\")}_model.pkl'\n", "        joblib.dump(model, model_filename)\n", "        print(f\"Model {model_name} saved to {model_filename}\")\n", "        \n", "        # Validate the model\n", "        y_val_pred = model.predict(X_val.toarray())\n", "        accuracy = accuracy_score(y_val, y_val_pred)\n", "        recall = recall_score(y_val, y_val_pred, average='weighted')\n", "        precision = precision_score(y_val, y_val_pred, average='weighted')\n", "        f1 = f1_score(y_val, y_val_pred, average='weighted')\n", "        \n", "        # Append metrics to results\n", "        validation_results.append((model_name, accuracy))\n", "        metrics_results.append((model_name, 'Validation', accuracy, recall, precision, f1))\n", "        \n", "        # Display metrics\n", "        print(f\"Model: {model_name}\")\n", "        # print(f\"Cross-Validation Accuracy (5 folds): {average_accuracy:.4f}\")\n", "\n", "        print(f\"Validation Accuracy: {accuracy:.4f}\")\n", "        print(f\"Recall: {recall:.4f}, Precision: {precision:.4f}, F1 Score: {f1:.4f}\\n\")\n", "        \n", "        # Confusion Matrix Visualization\n", "        plot_confusion_matrix(y_val, y_val_pred, model_name, 'Validation', test_size)\n", "    \n", "    # Plotting validation results\n", "    plot_validation_results(validation_results, test_size)\n", "\n", "    return metrics_results\n", "\n", "# Function to plot confusion matrix\n", "def plot_confusion_matrix(y_true, y_pred, model_name, phase, test_size):\n", "    cm = confusion_matrix(y_true, y_pred)\n", "    plt.figure(figsize=(8, 6))\n", "    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', xticklabels=np.unique(y_true), yticklabels=np.unique(y_true))\n", "    plt.xlabel('Predicted')\n", "    plt.ylabel('Actual')\n", "    plt.title(f'Confusion Matrix for {model_name} ({phase})')\n", "    plt.savefig(f'{output_folder}/confusion_matrix_{model_name}_{phase.lower()}_test_size_{test_size}.png')\n", "    plt.close()\n", "\n", "# Function to plot validation results\n", "def plot_validation_results(validation_results, test_size):\n", "    model_names, accuracies = zip(*validation_results)\n", "    plt.figure(figsize=(10, 5))\n", "    plt.bar(model_names, accuracies, color='skyblue')\n", "    plt.xlabel('Model')\n", "    plt.ylabel('Validation Accuracy')\n", "    plt.title(f'Validation Accuracy for Different Models (test_size={test_size})')\n", "    plt.xticks(rotation=45)\n", "    plt.tight_layout()\n", "    plt.savefig(f'{output_folder}/validation_accuracy_test_size_{test_size}.png')\n", "    plt.close()"]}, {"cell_type": "code", "execution_count": 109, "metadata": {}, "outputs": [], "source": ["# Function to test models\n", "def test_models(models, X_test, df,test_size):\n", "    print(\"\\nTesting Phase\\n\")\n", "    test_results = []\n", "    metrics_results = []\n", "    \n", "    for model_name in models.keys():\n", "        # Load the trained model\n", "        model_filename = f'{output_folder}/{model_name.replace(\" \", \"_\")}_model.pkl'\n", "        model = joblib.load(model_filename)\n", "        print(f\"Model {model_name} loaded from {model_filename}\")\n", "        \n", "        # Predict the test set\n", "        y_test_pred = model.predict(X_test.toarray())\n", "        \n", "        # Store the predicted labels\n", "        test_results.append((model_name, y_test_pred))\n", "        \n", "        # Display the predicted labels\n", "        print(f\"Model: {model_name}\")\n", "        print(f\"Predicted Labels: {y_test_pred}\\n\")\n", "        \n", "        # Confusion Matrix Visualization for Testing\n", "        # Assuming we use some dummy labels for testing purposes (since actual test labels are not provided)\n", "        # y_test_dummy = [0] * len(y_test_pred)  # Example of creating dummy labels\n", "        # plot_confusion_matrix(y_test_dummy, y_test_pred, model_name, 'Testing', test_size)\n", "        # If actual labels are available for testing (optional)\n", "        if 'actual_labels' in df.columns:\n", "            y_test_actual = df['actual_labels']\n", "\n", "            # Calculate the metrics for testing\n", "            accuracy = accuracy_score(y_test_actual, y_test_pred)\n", "            recall = recall_score(y_test_actual, y_test_pred, average='weighted')\n", "            precision = precision_score(y_test_actual, y_test_pred, average='weighted')\n", "            f1 = f1_score(y_test_actual, y_test_pred, average='weighted')\n", "\n", "            # Append metrics to results\n", "            metrics_results.append((model_name, test_size, accuracy, precision, recall, f1))\n", "\n", "            # Confusion Matrix Visualization for Testing\n", "            plot_confusion_matrix(y_test_actual, y_test_pred, model_name, 'Testing', test_size)\n", "        else:\n", "            print(f\"Warning: Actual labels are not provided for meaningful evaluation of {model_name}\")\n", "\n", "    # Save test results to CSV\n", "    # output_df = pd.DataFrame()\n", "    # df = X_test.copy()\n", "    # OK\n", "    # -----------------\n", "    # output_df = pd.DataFrame({'comment': df['comment'], 'clean_comment': df['clean_comment']})\n", "    # for model_name, y_test_pred in test_results:\n", "    #     output_df[model_name] = y_test_pred\n", "    # output_df.to_csv(f'{output_folder}/test_predictions_test_size_{test_size}.csv', index=False)\n", "    # print(f\"Test predictions saved to '{output_folder}/test_predictions_test_size_{test_size}.csv'\")\n", "    #---------------------\n", "\n", "    # Save test results to CSV including original and cleaned comments\n", "    output_df = df.copy()\n", "    for model_name, y_test_pred in test_results:\n", "        output_df[model_name] = y_test_pred\n", "    output_df.to_csv(f'{output_folder}/test_predictions_test_size_{test_size}.csv', index=False)\n", "    print(f\"Test predictions saved to '{output_folder}/test_predictions_test_size_{test_size}.csv'\")\n", "\n", "    # Save metrics results to CSV\n", "    if metrics_results:\n", "        metrics_df = pd.DataFrame(metrics_results, columns=['Model', 'Test_Size', 'Accuracy', 'Precision', 'Recall', 'F1'])\n", "        metrics_df.to_csv(f'{output_folder}/test_metrics_results_test_size_{test_size}.csv', index=False)\n", "        print(f\"Test metrics results saved to '{output_folder}/test_metrics_results_test_size_{test_size}.csv'\")\n", "    \n", "    # Display statistics of prediction results\n", "    # for model_name, y_test_pred in test_results:\n", "    #     count_0 = (y_test_pred == 0).sum()\n", "    #     count_1 = (y_test_pred == 1).sum()\n", "    #     print(f\"Model: {model_name}\")\n", "    #     print(f\"Predicted 'ham' (0): {count_0}\")\n", "    #     print(f\"Predicted 'spam' (1): {count_1}\\n\")\n", "\n", "    # Display statistics of prediction results and save\n", "    stats = []\n", "    for model_name, _ in test_results:\n", "        count_0 = output_df[model_name].value_counts().get(0, 0)\n", "        count_1 = output_df[model_name].value_counts().get(1, 0)\n", "        stats.append({'Model': model_name, 'Predicted_Ham': count_0, 'Predicted_Spam': count_1})\n", "        print(f\"Model: {model_name}\")\n", "        print(f\"Predicted 'ham' (0): {count_0}\")\n", "        print(f\"Predicted 'spam' (1): {count_1}\\n\")\n", "    \n", "    # Save statistics to CSV\n", "    stats_df = pd.DataFrame(stats)\n", "    stats_df.to_csv(f'{output_folder}/prediction_statistics_test_size_{test_size}.csv', index=False)\n", "    print(f\"Prediction statistics saved to '{output_folder}/prediction_statistics_test_size_{test_size}.csv'\")\n", "\n", "    # Visualize the statistics\n", "    plt.figure(figsize=(12, 6))\n", "    for model_stat in stats:\n", "        plt.bar(model_stat['Model'], model_stat['Predicted_Ham'], color='blue', alpha=0.6, label='Ham')\n", "        plt.bar(model_stat['Model'], model_stat['Predicted_Spam'], bottom=model_stat['Predicted_Ham'], color='red', alpha=0.6, label='Spam')\n", "    \n", "    plt.xlabel('Model')\n", "    plt.ylabel('Count of Predictions')\n", "    plt.title(f'Prediction Counts for Each Model (test_size={test_size})')\n", "    plt.xticks(rotation=45)\n", "    plt.tight_layout()\n", "    plt.savefig(f'{output_folder}/prediction_statistics_visualization_test_size_{test_size}.png')\n", "    plt.close()\n", "\n", "    return metrics_results"]}, {"cell_type": "code", "execution_count": 110, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processed test dataset saved to 'awal/processed_test_dataset.csv' and 'awal/processed_test_dataset.xlsx'\n", "\n", "Using test_size = 0.2 for splitting the data\n", "\n", "\n", "Training and Validation Phase\n", "\n", "Model Naive <PERSON>es saved to awal/Naive_<PERSON>_model.pkl\n", "Model: <PERSON><PERSON>\n", "Validation Accuracy: 0.7423\n", "Recall: 0.7423, Precision: 0.7586, F1 Score: 0.7423\n", "\n", "Model SVM (linear kernel) saved to awal/SVM_(linear_kernel)_model.pkl\n", "Model: SVM (linear kernel)\n", "Validation Accuracy: 0.8980\n", "Recall: 0.8980, Precision: 0.9076, F1 Score: 0.8982\n", "\n", "Model SVM (poly kernel) saved to awal/SVM_(poly_kernel)_model.pkl\n", "Model: SVM (poly kernel)\n", "Validation Accuracy: 0.8699\n", "Recall: 0.8699, Precision: 0.8703, F1 Score: 0.8694\n", "\n", "Model SVM (rbf kernel) saved to awal/SVM_(rbf_kernel)_model.pkl\n", "Model: SVM (rbf kernel)\n", "Validation Accuracy: 0.9107\n", "Recall: 0.9107, Precision: 0.9146, F1 Score: 0.9109\n", "\n", "Model SVM (sigmoid kernel) saved to awal/SVM_(sigmoid_kernel)_model.pkl\n", "Model: SVM (sigmoid kernel)\n", "Validation Accuracy: 0.8801\n", "Recall: 0.8801, Precision: 0.8916, F1 Score: 0.8803\n", "\n", "Model Random Forest saved to awal/Random_Forest_model.pkl\n", "Model: Random Forest\n", "Validation Accuracy: 0.8622\n", "Recall: 0.8622, Precision: 0.8670, F1 Score: 0.8607\n", "\n", "Model Logistic Regression saved to awal/Logistic_Regression_model.pkl\n", "Model: Logistic Regression\n", "Validation Accuracy: 0.8801\n", "Recall: 0.8801, Precision: 0.8903, F1 Score: 0.8804\n", "\n", "Model XGBoost saved to awal/XGBoost_model.pkl\n", "Model: XGBoost\n", "Validation Accuracy: 0.8801\n", "Recall: 0.8801, Precision: 0.8916, F1 Score: 0.8803\n", "\n", "\n", "Testing Phase\n", "\n", "Model Naive Bayes loaded from awal/Naive_Bay<PERSON>_model.pkl\n", "Model: <PERSON><PERSON>\n", "Predicted Labels: [1 1 1 0 1 1 0 1 0 0 1 1 1 1 1 0 0 1 0 0 1 1 0 0 0 1 0 0 0 0 1 1 1 1 0 1 0\n", " 0 1 1 0 1 0 1 0 1 0 1 1 1 0 0 0 0 0 1 0 0 0 0 1 1 1 1 0 1 1 0 1 0 1 0 1 0\n", " 0 0 1 1 0 0 1 1 0 0 0 0 1 1 0 0 1 0 1 1 0 1 0 0 0 1 0 1 0 0 0 0 1 0 1 0 1\n", " 1 1 0 0 1 0 0 0 1 0 1 0 0 1 1 0 1 1 1 1 1 1 0 1 1 0 0 0 1 1 1 1 1 0 1 0 1\n", " 0 1 1 1 1 1 0 1 1 1 0 0 1 1 1 0 1 0 1 0 0 0 1 0 0 0 1 1 1 0 1 0 0 1 0 1 0\n", " 0 0 0 0 0 1 0 0 1 1 1 1 0 1 0 1 0 0 0 0 1 0 1 1 0 1 0 0 0 0 0 0 0 0 1 1 0\n", " 1 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 0 1 1 1 1 0 1 0 0 0 1 0 1 0\n", " 1 0 1 1 0 0 0 0 1 1 1 1 0 0 0 0 1 1 1 0 1 0 1 1 0 0 1 1 1 0 1 0 1 1 0 1 1\n", " 0 0 1 1 1 1 1 0 1 1 1 1 1 0 1 0 0 0 0 1 1 0 0 0 1 0 0 0 0 0 0 0 1 0 1 0 1\n", " 0 1 1 0 1 1 0 1 0 0 1 1 0 0 1 1 0 0 1 1 1 0 0 1 0 1 0 0 0 1 1 0 1 1 0 1 0\n", " 1 0 0 0 1 0 0 0 1 1 1 1 0 0 1 0 0 1 0 1 0 1 0 0 0 0 1 1 0 0 0 0 1 1 0 0 0\n", " 1 0 1 0 0 1 1 0 1 0 0 0 0 1 0 0 0 0 0 1 0 0 1 0 0 1 1 1 0 0 0 0 0 0 0 1 1\n", " 0 0 0 1 0 0 0 1 1 0 0 0 1 0 0 0 1 1 0 1 1 0 1 1 0 1 1 0 1 0 0 1 0 0 0 0 1\n", " 0 0 1 1 1 1 0 1 0 1 1 0 1 0 1 1 0 0 0 0 1 0 1 0 1 0 0 0 0 1 0 0 0 1 0 0 1\n", " 0 0 1 0 1 1 1 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 0 0 1 1 0 0 0 0 1 0 0 0 1 1 1\n", " 0 0 1 0 0 0 0 0 0 1 0 0 0 0 0 0 1 0 0 0 1 0 0 1 0 0 1 0 0 0 1 0 0 0 0 1 1\n", " 0 0 1 0 0 1 0 1 0 0 0 0 1 0 0 0 0 0 1 0 1 0 0 1 1 0 1 1 0 0 0 0 0 0 0 0 0\n", " 0 0 0 1 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of Na<PERSON>\n", "Model SVM (linear kernel) loaded from awal/SVM_(linear_kernel)_model.pkl\n", "Model: SVM (linear kernel)\n", "Predicted Labels: [0 0 0 0 0 1 0 1 0 0 1 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 0\n", " 0 1 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0 0 0 0\n", " 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1\n", " 1 1 0 0 1 1 0 0 1 0 0 0 0 0 0 0 0 1 1 0 0 0 1 1 1 0 0 0 0 1 0 0 1 0 1 0 1\n", " 0 0 0 0 1 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 1 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 1 1 0 0 1 1 0 1 0 0 0 0 0 0 0 0 0\n", " 0 0 1 1 0 0 0 0 1 0 1 1 0 0 0 0 0 1 1 0 0 0 1 0 0 0 1 0 0 0 0 0 1 0 0 0 0\n", " 0 0 1 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 1 0 1 0 1 0 0 1 0 0 0 1 0 0 0 0 0 0 1 0 0 1 0 1 0 1 0 0 0 1 0 0 1 0 0 1 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 1 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 0 0 1\n", " 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 1 1 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1\n", " 0 0 0 0 0 1 0 1 0 1 0 0 1 0 0 1 0 0 0 0 0 0 1 1 0 0 0 0 0 0 0 0 0 1 0 0 0\n", " 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 1 1 0 1 0 0 1 0 0 0 0 0 0 0 0 1 0\n", " 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of SVM (linear kernel)\n", "Model SVM (poly kernel) loaded from awal/SVM_(poly_kernel)_model.pkl\n", "Model: SVM (poly kernel)\n", "Predicted Labels: [0 0 0 0 1 1 0 0 0 0 1 1 1 0 0 0 0 1 0 1 0 0 0 0 1 0 0 1 0 0 0 0 0 0 0 1 0\n", " 0 1 1 1 1 1 1 1 1 1 0 1 1 0 0 1 0 1 1 1 0 0 1 0 1 0 1 1 1 0 1 1 0 0 0 1 0\n", " 1 1 0 1 0 0 1 1 0 1 0 0 1 1 0 0 0 0 0 1 1 0 0 1 0 1 1 0 0 0 0 0 0 0 1 1 1\n", " 1 1 1 1 1 1 1 1 1 0 0 1 0 0 0 0 0 1 1 0 0 0 0 1 1 0 0 0 0 1 0 1 1 0 1 0 1\n", " 1 0 1 1 1 1 0 0 1 0 1 1 1 1 0 0 0 0 1 1 1 1 0 0 0 1 1 1 0 0 1 0 0 1 0 1 1\n", " 0 1 0 1 0 1 0 0 1 1 1 0 1 1 1 1 1 0 1 0 1 1 0 1 0 1 1 0 0 0 1 1 0 1 0 1 0\n", " 1 1 1 0 0 1 0 0 1 1 1 0 0 0 1 0 1 1 0 1 1 1 0 0 1 1 0 1 1 1 0 0 1 0 0 0 1\n", " 1 1 1 1 1 1 1 0 1 0 1 1 0 0 0 0 1 1 1 1 0 0 1 0 1 0 1 1 1 1 1 1 1 0 0 0 0\n", " 0 0 1 1 1 1 1 0 1 0 0 0 1 0 1 0 0 0 1 1 0 1 0 1 1 0 0 0 0 1 0 1 1 0 0 0 1\n", " 1 1 1 1 0 1 1 1 0 1 1 1 1 0 1 0 0 1 1 1 0 1 0 1 0 1 0 0 1 1 1 1 1 0 1 1 0\n", " 1 0 1 1 1 1 1 0 0 0 1 0 1 0 0 0 1 0 1 0 1 1 1 1 1 1 1 0 0 0 0 1 1 1 1 1 0\n", " 1 1 1 0 0 1 1 1 0 1 1 1 0 1 0 1 1 0 1 1 1 1 1 1 1 0 1 0 1 1 1 1 1 1 1 0 1\n", " 0 1 0 0 1 1 0 1 1 0 1 0 1 0 1 1 1 1 0 0 0 1 0 1 0 1 1 1 1 1 1 1 0 1 1 1 1\n", " 1 1 0 1 1 1 1 1 0 1 0 1 1 0 0 1 1 1 0 1 1 1 1 1 1 0 0 0 1 1 1 1 0 1 1 1 1\n", " 0 0 1 0 1 0 0 1 1 1 1 0 0 1 0 0 1 0 1 1 0 0 0 1 0 0 1 1 0 0 1 1 1 1 1 0 1\n", " 1 1 1 1 1 0 1 1 0 0 1 1 0 1 1 1 0 1 1 1 1 1 1 1 1 1 1 1 1 1 0 1 1 0 1 1 0\n", " 1 1 1 1 1 0 1 0 1 0 1 0 0 1 0 1 1 1 1 0 1 1 0 1 1 0 1 1 1 1 0 1 0 1 1 1 1\n", " 1 1 0 0 0 1 1 0 1 1 1 1 1 1 1 1 1 1 0 1 1 0 1 1]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of SVM (poly kernel)\n", "Model SVM (rbf kernel) loaded from awal/SVM_(rbf_kernel)_model.pkl\n", "Model: SVM (rbf kernel)\n", "Predicted Labels: [0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 1 0 0 0 0 0 0 0 1 0\n", " 0 1 0 1 0 1 1 1 0 1 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0\n", " 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 0 0 0 1 1\n", " 0 1 0 0 1 0 1 1 1 0 0 1 0 0 0 0 0 0 1 0 0 0 0 1 1 0 0 0 0 1 0 0 1 0 1 0 0\n", " 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 1 0 0 0 0 0 1 0 0 0 0 0 0 0 0 1 1\n", " 0 0 0 1 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 1 1 0 1 0 0 0\n", " 1 1 0 0 0 0 0 0 1 1 1 0 0 0 1 0 1 1 0 0 1 1 0 0 0 1 0 0 1 0 0 0 1 0 0 0 1\n", " 0 1 0 1 1 1 1 0 1 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 1 0 0 0 0 0 0 1 1 0 0 0 0\n", " 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 1 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 1 0 0 0 1 0 1 0 1 0 0 1 1 0 1 0 0 0 1 0\n", " 1 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 1 0 1 1 1 1 1 0 0 0 0 0 0 1 1 1 0\n", " 0 1 0 0 0 0 0 1 0 0 1 1 0 0 0 1 0 0 1 0 0 1 0 0 0 0 1 0 0 1 1 1 1 1 0 0 0\n", " 0 1 0 0 1 0 0 0 1 0 1 0 0 0 1 1 0 0 0 0 0 0 0 0 0 0 1 1 0 0 1 0 0 1 1 1 1\n", " 0 1 0 0 0 1 1 1 0 0 0 1 1 0 0 0 1 1 0 1 0 1 1 0 0 0 0 0 1 0 1 1 0 1 1 0 0\n", " 0 0 0 0 0 0 1 0 0 1 1 0 0 1 0 0 1 0 1 1 0 0 0 1 0 0 1 1 0 0 0 1 0 1 0 0 0\n", " 1 1 0 1 1 0 0 1 0 0 1 1 0 1 1 0 0 1 0 1 0 0 1 0 1 1 1 0 1 1 0 1 1 0 1 1 0\n", " 1 1 0 1 1 0 1 0 1 0 1 0 0 1 0 1 1 1 0 0 1 1 0 0 0 0 0 0 1 1 0 1 0 1 1 1 1\n", " 1 1 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 0 1 1 0 1 1]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of SVM (rbf kernel)\n", "Model SVM (sigmoid kernel) loaded from awal/SVM_(sigmoid_kernel)_model.pkl\n", "Model: SVM (sigmoid kernel)\n", "Predicted Labels: [0 0 0 0 0 1 0 1 0 0 1 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 0\n", " 0 1 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 1 0 0 1 0 1 0 0 0 0 0\n", " 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1\n", " 1 1 0 0 1 1 0 0 1 0 0 0 0 0 0 0 0 1 1 0 0 0 1 1 1 0 0 0 0 1 0 0 1 0 1 0 1\n", " 0 0 0 0 1 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 1 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 1 1 0 0 1 1 0 1 0 0 0 0 0 0 0 0 0\n", " 0 0 1 1 0 0 0 0 1 0 1 1 0 0 0 0 0 1 1 0 0 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0\n", " 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 0 0\n", " 0 0 1 0 1 0 0 1 0 0 0 1 0 0 0 0 0 0 1 0 0 1 0 1 0 1 0 0 0 1 0 0 1 0 0 1 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 1 0 1 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 0 0 1\n", " 0 0 0 0 0 0 0 0 1 0 0 0 1 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1\n", " 0 0 0 0 0 1 0 1 0 1 0 0 1 0 0 1 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 1 0 0 0\n", " 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 1 0\n", " 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of SVM (sigmoid kernel)\n", "Model Random Forest loaded from awal/Random_Forest_model.pkl\n", "Model: Random Forest\n", "Predicted Labels: [0 0 0 0 0 1 0 1 0 0 1 1 0 0 0 1 0 1 0 1 0 0 0 0 1 0 0 1 1 0 1 0 0 0 0 1 0\n", " 0 1 1 1 1 1 1 1 1 1 1 0 1 1 1 1 0 1 0 1 0 1 0 1 0 0 1 1 1 0 1 1 0 0 1 0 0\n", " 1 1 0 1 0 0 1 1 0 1 1 0 1 1 0 1 1 0 0 0 1 0 0 1 0 0 1 0 0 0 1 0 1 1 1 1 0\n", " 1 1 1 0 1 1 1 1 1 0 0 1 1 0 0 0 0 1 1 0 1 1 1 1 1 0 1 0 1 1 1 1 1 1 0 0 1\n", " 1 0 1 1 1 1 1 1 0 0 1 1 1 1 1 0 1 0 1 1 1 1 0 1 0 1 1 1 1 1 1 1 0 1 1 1 1\n", " 0 1 0 1 1 0 1 0 0 1 1 1 1 1 1 1 1 0 1 0 1 1 0 1 1 1 0 1 1 0 1 1 0 1 0 0 0\n", " 0 1 1 0 1 1 0 1 1 1 1 0 1 0 1 0 1 1 0 1 1 1 0 0 1 1 0 0 1 0 1 1 1 0 1 0 1\n", " 1 1 1 1 1 1 1 0 1 0 1 1 1 1 1 0 1 1 1 1 1 0 1 0 1 0 1 1 0 1 1 1 0 0 0 0 0\n", " 0 1 1 1 0 0 1 1 1 1 0 0 1 0 0 0 0 0 0 1 0 1 1 1 1 1 0 1 1 1 1 1 1 1 0 0 1\n", " 1 0 1 0 0 0 1 1 0 1 1 1 1 0 1 0 0 1 1 1 1 1 1 1 0 1 0 1 1 1 1 1 1 0 1 1 1\n", " 1 0 1 1 1 1 1 0 1 0 1 1 0 1 0 1 1 1 1 0 1 0 1 1 1 1 1 0 1 1 0 1 1 1 1 1 0\n", " 1 1 1 1 0 0 1 1 1 1 1 1 1 0 1 1 1 0 1 1 1 1 1 1 1 1 1 0 1 1 1 1 1 1 1 0 1\n", " 0 1 1 1 1 1 0 1 1 1 1 1 0 0 1 1 1 1 1 1 1 1 0 1 0 1 1 1 1 1 1 0 1 1 1 1 0\n", " 1 1 1 1 0 1 1 1 1 1 0 1 1 0 1 1 1 1 0 1 0 1 1 1 1 0 0 0 1 1 1 1 1 1 1 1 1\n", " 1 1 1 0 0 1 1 1 0 1 1 1 0 1 0 0 1 0 1 1 1 1 0 1 0 1 1 1 0 1 1 1 1 1 1 1 1\n", " 1 1 1 1 1 0 1 1 1 0 1 1 0 1 1 1 0 1 1 1 1 1 1 1 1 1 0 1 1 1 1 1 1 0 1 1 0\n", " 1 1 1 1 1 1 1 1 1 0 1 1 1 1 0 1 1 1 1 0 1 1 1 1 0 0 1 1 1 1 1 1 1 1 1 1 1\n", " 1 1 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 1 1 0 1 1]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of Random Forest\n", "Model Logistic Regression loaded from awal/Logistic_Regression_model.pkl\n", "Model: Logistic Regression\n", "Predicted Labels: [0 0 0 0 0 0 0 0 0 1 1 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 0\n", " 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0 0 0 0\n", " 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1\n", " 0 1 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 1 1 0 0 0 1 1 1 0 0 0 0 1 0 0 1 0 1 0 0\n", " 0 0 0 0 1 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 1 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 1 1 0 0 1 1 0 1 0 1 1 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0 0 0 0 0 1 0 0 0 0\n", " 0 0 1 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 0 0\n", " 1 0 1 0 1 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 1 0 1 0 0 0 1 0 0 1 0 0 1 0\n", " 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 1 0 0 0\n", " 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0\n", " 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1\n", " 0 0 0 0 0 1 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 0 0 0 1 0 0 0\n", " 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 1 0\n", " 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of Logistic Regression\n", "Model XGBoost loaded from awal/XGBoost_model.pkl\n", "Model: XGBoost\n", "Predicted Labels: [0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 1 1 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1\n", " 0 0 0 0 1 0 0 0 0 0 1 0 1 0 1 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 1 0 0 0 0 0 0 0 1 0 0\n", " 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 1 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of XGBoost\n", "Test predictions saved to 'awal/test_predictions_test_size_0.2.csv'\n", "Model: <PERSON><PERSON>\n", "Predicted 'ham' (0): 381\n", "Predicted 'spam' (1): 272\n", "\n", "Model: SVM (linear kernel)\n", "Predicted 'ham' (0): 540\n", "Predicted 'spam' (1): 113\n", "\n", "Model: SVM (poly kernel)\n", "Predicted 'ham' (0): 265\n", "Predicted 'spam' (1): 388\n", "\n", "Model: SVM (rbf kernel)\n", "Predicted 'ham' (0): 443\n", "Predicted 'spam' (1): 210\n", "\n", "Model: SVM (sigmoid kernel)\n", "Predicted 'ham' (0): 544\n", "Predicted 'spam' (1): 109\n", "\n", "Model: Random Forest\n", "Predicted 'ham' (0): 199\n", "Predicted 'spam' (1): 454\n", "\n", "Model: Logistic Regression\n", "Predicted 'ham' (0): 562\n", "Predicted 'spam' (1): 91\n", "\n", "Model: XGBoost\n", "Predicted 'ham' (0): 618\n", "Predicted 'spam' (1): 35\n", "\n", "Prediction statistics saved to 'awal/prediction_statistics_test_size_0.2.csv'\n", "Metrics results saved to 'awal/metrics_results_test_size_0.2.csv'\n", "\n", "Using test_size = 0.25 for splitting the data\n", "\n", "\n", "Training and Validation Phase\n", "\n", "Model Naive <PERSON>es saved to awal/Naive_<PERSON>_model.pkl\n", "Model: <PERSON><PERSON>\n", "Validation Accuracy: 0.7566\n", "Recall: 0.7566, Precision: 0.7764, F1 Score: 0.7561\n", "\n", "Model SVM (linear kernel) saved to awal/SVM_(linear_kernel)_model.pkl\n", "Model: SVM (linear kernel)\n", "Validation Accuracy: 0.8875\n", "Recall: 0.8875, Precision: 0.9003, F1 Score: 0.8876\n", "\n", "Model SVM (poly kernel) saved to awal/SVM_(poly_kernel)_model.pkl\n", "Model: SVM (poly kernel)\n", "Validation Accuracy: 0.8712\n", "Recall: 0.8712, Precision: 0.8716, F1 Score: 0.8707\n", "\n", "Model SVM (rbf kernel) saved to awal/SVM_(rbf_kernel)_model.pkl\n", "Model: SVM (rbf kernel)\n", "Validation Accuracy: 0.8998\n", "Recall: 0.8998, Precision: 0.9057, F1 Score: 0.9000\n", "\n", "Model SVM (sigmoid kernel) saved to awal/SVM_(sigmoid_kernel)_model.pkl\n", "Model: SVM (sigmoid kernel)\n", "Validation Accuracy: 0.8793\n", "Recall: 0.8793, Precision: 0.8909, F1 Score: 0.8795\n", "\n", "Model Random Forest saved to awal/Random_Forest_model.pkl\n", "Model: Random Forest\n", "Validation Accuracy: 0.8548\n", "Recall: 0.8548, Precision: 0.8604, F1 Score: 0.8530\n", "\n", "Model Logistic Regression saved to awal/Logistic_Regression_model.pkl\n", "Model: Logistic Regression\n", "Validation Accuracy: 0.8793\n", "Recall: 0.8793, Precision: 0.8898, F1 Score: 0.8795\n", "\n", "Model XGBoost saved to awal/XGBoost_model.pkl\n", "Model: XGBoost\n", "Validation Accuracy: 0.8793\n", "Recall: 0.8793, Precision: 0.8909, F1 Score: 0.8795\n", "\n", "\n", "Testing Phase\n", "\n", "Model Naive Bayes loaded from awal/Naive_Bay<PERSON>_model.pkl\n", "Model: <PERSON><PERSON>\n", "Predicted Labels: [1 1 1 0 1 1 0 1 0 0 1 1 1 1 1 0 0 1 0 0 1 1 0 0 0 1 0 0 0 0 1 1 1 1 0 1 0\n", " 0 0 1 0 1 0 1 0 1 0 1 1 1 0 0 0 0 0 1 0 0 0 0 1 1 1 1 0 1 1 0 1 0 1 0 1 0\n", " 0 0 1 1 0 0 1 1 0 0 0 0 1 1 0 0 1 0 1 1 0 1 0 0 0 1 0 1 0 0 0 0 1 0 1 0 1\n", " 1 1 0 0 1 0 0 0 1 0 1 0 0 1 1 0 1 1 1 1 1 1 0 1 1 0 0 0 1 1 1 1 1 0 1 0 1\n", " 0 1 1 1 1 1 0 1 1 1 0 0 1 1 1 0 1 0 1 0 0 0 1 0 0 0 0 1 1 0 1 0 0 1 0 1 0\n", " 0 0 0 0 0 1 0 0 1 1 1 1 0 1 0 1 0 0 0 0 1 0 1 1 0 1 0 0 0 0 0 0 0 0 1 1 0\n", " 1 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 0 1 1 1 1 0 1 0 0 0 1 0 1 0\n", " 1 0 1 1 0 0 0 0 1 1 1 1 0 0 0 0 0 1 1 0 1 0 1 1 0 0 1 1 1 0 1 0 1 1 0 1 1\n", " 0 0 1 1 1 1 1 0 1 1 1 1 1 0 0 0 0 0 0 1 1 0 0 0 1 0 0 0 0 0 0 0 1 0 1 0 1\n", " 0 1 1 0 1 1 0 1 0 0 1 1 0 0 1 1 0 0 1 1 1 0 0 1 0 1 0 0 0 1 1 0 1 1 0 1 0\n", " 1 0 0 0 1 0 0 0 1 1 1 1 0 0 1 0 0 0 0 1 0 1 0 0 0 0 1 1 0 0 0 0 1 1 0 0 0\n", " 1 0 1 0 0 1 1 0 1 0 0 0 0 1 0 0 0 0 0 1 0 0 1 0 0 1 1 1 0 0 0 0 0 0 0 1 1\n", " 0 0 0 1 0 0 0 1 1 0 0 0 1 0 0 0 1 1 0 1 1 0 1 1 0 1 1 0 1 0 0 1 0 0 0 0 1\n", " 0 0 1 1 1 1 0 1 0 1 1 0 1 0 1 1 0 0 0 0 1 0 1 0 1 0 0 0 0 1 0 0 0 1 0 0 1\n", " 0 0 1 0 1 1 1 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 0 0 1 1 0 0 0 0 1 0 0 0 1 1 1\n", " 0 0 1 0 0 0 0 0 0 1 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 1 1\n", " 0 0 1 0 0 1 0 1 0 0 0 0 1 0 0 0 0 0 1 0 1 0 0 1 1 0 1 1 0 0 0 0 0 0 0 0 0\n", " 0 0 0 1 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of Na<PERSON>\n", "Model SVM (linear kernel) loaded from awal/SVM_(linear_kernel)_model.pkl\n", "Model: SVM (linear kernel)\n", "Predicted Labels: [0 0 0 0 0 0 0 1 0 0 1 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 0\n", " 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 1 0 1 0 0 0 0 0\n", " 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1\n", " 1 1 0 0 1 1 0 0 1 0 0 0 0 0 0 0 0 1 1 0 0 0 1 1 1 0 0 0 0 1 0 0 1 0 1 0 1\n", " 0 0 0 0 1 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 1 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 1 1 0 0 1 1 0 1 0 0 0 0 0 0 0 0 0\n", " 1 0 1 1 0 0 0 0 1 0 1 1 0 0 0 0 0 1 1 0 0 0 1 0 0 0 1 0 0 0 0 0 1 0 0 0 0\n", " 0 0 1 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 1 0 0 0 0\n", " 1 0 1 0 1 0 0 1 0 0 0 1 0 0 0 0 0 0 1 0 0 1 0 1 0 1 0 0 0 1 0 0 1 0 0 1 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 1 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 0 0 1\n", " 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 1 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1\n", " 0 0 0 0 0 1 0 1 0 1 0 0 1 0 0 1 0 0 0 0 1 0 1 1 0 0 0 0 0 0 0 0 0 1 0 0 0\n", " 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0\n", " 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of SVM (linear kernel)\n", "Model SVM (poly kernel) loaded from awal/SVM_(poly_kernel)_model.pkl\n", "Model: SVM (poly kernel)\n", "Predicted Labels: [0 0 0 0 1 1 0 0 0 0 1 1 1 0 0 0 0 1 0 1 0 0 0 0 1 0 0 1 1 0 0 0 0 0 0 1 0\n", " 0 1 1 1 1 1 1 1 1 1 0 1 1 0 0 1 0 1 0 1 0 0 1 0 1 0 1 1 0 0 1 1 0 1 0 0 0\n", " 1 1 0 1 0 0 1 1 0 1 0 0 1 1 0 0 0 0 0 1 1 0 0 1 0 1 1 0 0 0 0 0 0 0 1 1 1\n", " 1 1 1 1 1 1 1 1 1 0 1 1 0 0 0 0 0 1 1 0 0 0 0 1 1 0 0 0 0 1 1 1 1 0 1 0 1\n", " 1 0 1 1 1 1 0 0 1 0 1 1 1 1 0 0 1 0 1 1 1 1 0 0 0 1 1 1 0 0 1 0 0 1 0 1 1\n", " 0 1 0 1 0 1 1 0 0 1 1 0 1 1 1 1 1 0 1 0 1 1 0 1 0 1 1 0 0 0 1 1 0 1 0 0 0\n", " 1 1 1 0 0 1 0 0 1 1 1 0 0 0 1 0 1 1 0 1 1 1 0 0 1 1 0 1 1 1 0 0 1 0 0 0 1\n", " 1 1 1 1 1 1 1 0 1 0 1 1 0 0 0 0 1 1 1 1 0 0 1 0 1 0 1 1 1 1 1 1 1 0 0 0 0\n", " 0 0 1 1 1 1 1 0 1 0 1 0 0 0 1 0 0 0 1 1 0 1 0 1 1 0 1 0 0 0 0 1 1 0 0 0 1\n", " 1 1 1 1 0 1 1 1 0 1 1 1 1 0 1 1 0 1 1 1 0 1 0 1 0 1 0 0 1 1 1 1 1 0 0 1 0\n", " 1 0 1 1 1 1 1 0 0 0 1 1 1 0 1 0 1 0 1 0 1 1 1 1 1 1 1 0 1 1 0 0 1 1 1 1 0\n", " 1 1 1 0 0 1 1 1 0 1 1 1 0 1 0 1 1 0 1 1 1 1 1 0 1 0 1 0 1 1 1 1 1 1 1 0 1\n", " 0 1 0 0 1 1 0 1 1 0 1 0 1 0 1 1 1 1 0 0 0 1 0 1 0 1 1 1 1 1 1 1 0 1 1 1 1\n", " 1 1 0 1 1 1 1 1 0 1 0 1 1 0 0 1 1 1 0 1 1 1 1 1 1 0 0 0 1 1 1 1 0 1 1 1 1\n", " 0 0 1 0 1 0 0 1 1 1 1 0 0 1 0 0 1 0 1 1 0 0 0 1 1 0 1 1 0 0 0 1 1 1 1 0 1\n", " 1 1 1 1 1 0 1 1 0 0 1 1 0 1 1 1 0 1 1 1 1 1 1 1 1 1 1 1 1 1 0 1 1 0 1 1 0\n", " 1 1 1 1 1 0 1 0 1 0 1 0 0 1 0 1 1 1 1 0 1 1 0 1 0 0 1 1 1 1 0 1 0 1 1 1 1\n", " 1 1 0 0 0 1 1 0 1 1 1 1 1 1 1 1 1 1 0 1 1 0 1 1]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of SVM (poly kernel)\n", "Model SVM (rbf kernel) loaded from awal/SVM_(rbf_kernel)_model.pkl\n", "Model: SVM (rbf kernel)\n", "Predicted Labels: [0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 1 0 0 0 0 0 0 0 1 0\n", " 0 0 0 1 0 1 1 1 0 1 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0\n", " 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 0 0 0 1 1\n", " 0 1 0 0 1 0 1 1 1 0 0 1 0 0 0 0 0 0 1 0 0 0 0 1 1 0 0 0 0 1 0 0 1 0 1 0 0\n", " 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 1 0 0 0 0 0 1 0 0 0 0 0 0 0 0 1 1\n", " 0 0 0 1 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 1 1 0 1 0 0 0\n", " 1 1 0 0 0 0 0 0 1 1 1 0 0 0 1 0 1 1 0 0 1 1 0 0 0 1 0 0 1 0 0 0 1 0 0 0 1\n", " 0 1 0 1 1 1 1 0 1 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 1 0 0 0 0 0 0 1 1 0 0 0 0\n", " 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 1 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 1 0 0 0 1 0 1 0 1 0 0 1 1 0 1 0 0 0 1 0\n", " 1 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 1 0 1 1 1 1 1 0 0 0 0 0 0 1 1 1 0\n", " 0 1 0 0 0 0 0 1 0 0 1 1 0 0 0 1 0 0 1 0 0 1 0 0 0 0 1 0 0 1 1 1 1 1 0 0 0\n", " 0 1 0 0 1 0 0 0 1 0 1 0 0 0 1 1 0 0 0 0 0 0 0 0 0 0 1 1 0 0 1 0 0 1 1 1 1\n", " 0 1 0 0 0 1 1 1 0 0 0 1 1 0 0 0 1 1 0 1 1 1 1 0 0 0 0 0 1 0 1 1 0 1 1 0 0\n", " 0 0 0 0 0 0 1 0 0 1 1 0 0 1 0 0 1 0 1 1 0 0 0 1 0 0 1 1 0 0 0 1 0 1 0 0 0\n", " 1 1 0 1 1 0 0 1 0 0 1 1 0 1 1 0 0 1 0 1 0 0 1 0 1 1 0 0 1 1 0 1 1 0 1 1 0\n", " 1 1 0 1 1 0 1 0 1 0 1 0 0 1 0 1 1 1 0 0 1 1 0 0 0 0 0 0 1 1 0 1 0 1 1 1 1\n", " 1 1 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 0 1 1 0 1 1]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of SVM (rbf kernel)\n", "Model SVM (sigmoid kernel) loaded from awal/SVM_(sigmoid_kernel)_model.pkl\n", "Model: SVM (sigmoid kernel)\n", "Predicted Labels: [0 0 0 0 0 0 0 1 0 0 1 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 0\n", " 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 1 0 0 1 0 1 0 0 0 0 0\n", " 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1\n", " 1 1 0 0 1 1 0 0 1 0 0 0 0 0 0 0 0 1 1 0 0 0 1 1 1 0 0 0 0 1 0 0 1 0 1 0 1\n", " 0 0 0 0 1 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 1 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 1 1 0 0 1 1 0 1 0 0 0 0 0 0 0 0 0\n", " 0 0 1 1 0 0 0 0 1 0 1 1 0 0 0 0 0 1 1 0 0 0 1 0 0 0 1 0 0 0 0 0 1 0 0 0 0\n", " 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 0 0\n", " 0 0 1 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 1 0 0 1 0 1 0 1 0 0 0 1 0 0 1 0 0 1 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 1 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 0 0 1\n", " 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1\n", " 0 0 0 0 0 1 0 1 0 1 0 0 1 0 0 1 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 1 0 0 0\n", " 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0\n", " 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 1 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of SVM (sigmoid kernel)\n", "Model Random Forest loaded from awal/Random_Forest_model.pkl\n", "Model: Random Forest\n", "Predicted Labels: [1 0 1 0 0 1 0 1 0 0 1 1 0 0 0 1 0 1 0 1 0 0 0 1 1 0 1 1 1 0 1 0 0 0 0 1 0\n", " 0 1 1 1 1 1 1 1 1 1 1 0 1 1 1 1 0 1 0 1 0 1 0 1 1 1 1 1 1 0 1 1 0 0 1 0 0\n", " 1 1 1 1 0 0 1 1 0 1 1 0 1 1 0 1 1 0 0 0 1 0 0 1 0 0 1 0 0 0 1 1 1 1 1 1 1\n", " 1 1 1 0 1 1 1 1 0 0 1 1 1 0 0 1 0 1 1 0 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0 0 1\n", " 1 0 1 0 1 1 1 1 0 0 1 1 1 1 0 1 1 0 1 1 1 1 0 1 0 1 1 1 1 1 1 1 0 1 1 1 1\n", " 0 1 0 1 1 0 1 0 0 1 1 1 1 1 1 1 1 0 1 0 1 1 0 1 1 1 0 1 1 0 1 1 0 1 0 0 0\n", " 0 1 1 1 1 1 0 1 1 1 1 0 0 0 1 0 1 1 0 1 1 1 0 0 1 1 1 0 1 0 1 1 1 0 1 0 1\n", " 1 1 1 1 1 1 1 0 1 0 1 1 1 1 1 0 1 1 1 1 1 0 1 0 1 0 0 1 0 1 1 1 1 0 1 0 0\n", " 0 1 1 1 0 0 1 1 1 0 1 0 1 0 0 0 0 0 1 1 0 1 1 1 1 1 0 1 1 1 1 1 1 1 0 0 1\n", " 1 0 0 0 1 0 1 1 0 1 1 1 1 0 1 0 1 1 1 1 1 1 1 1 0 1 0 1 1 1 1 1 1 0 1 1 1\n", " 1 1 1 1 1 1 1 0 1 1 1 1 1 1 1 1 1 1 1 0 1 0 1 1 1 1 1 1 1 1 0 1 1 1 1 1 1\n", " 1 1 1 1 0 0 1 1 1 1 1 1 1 0 1 1 1 0 1 1 1 1 1 1 1 1 1 0 1 1 1 1 1 1 1 1 1\n", " 1 1 1 1 1 1 0 1 1 1 1 1 0 0 1 1 1 1 1 1 0 1 0 1 0 1 1 1 1 1 1 0 1 1 1 1 0\n", " 1 1 0 1 0 1 1 1 1 1 1 1 1 0 1 1 1 1 0 1 0 1 1 1 1 0 0 1 1 1 1 1 1 1 1 1 1\n", " 1 1 1 0 0 1 1 1 0 1 1 1 1 1 0 0 1 0 1 1 1 1 0 1 1 1 1 1 0 1 0 1 1 1 1 1 1\n", " 1 1 1 1 1 0 1 1 1 0 1 1 0 1 1 1 0 1 1 1 1 1 1 1 1 1 0 1 1 1 1 1 1 1 1 1 0\n", " 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 1 1 1 1 0 0 1 1 0 0 1 1 1 1 1 1 1 1 1 1 1 1\n", " 1 1 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 1 1 0 1 1]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of Random Forest\n", "Model Logistic Regression loaded from awal/Logistic_Regression_model.pkl\n", "Model: Logistic Regression\n", "Predicted Labels: [0 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 0\n", " 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0 0 0 0\n", " 0 1 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0\n", " 0 1 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 1 1 0 0 0 1 1 1 0 0 0 0 1 0 0 1 0 1 0 0\n", " 0 0 0 0 1 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 1 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 1 1 0 0 1 1 0 1 0 1 1 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0 0 0 0 0 1 0 0 0 0\n", " 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 1 0 0 1 0 0 0 0 0 0 0\n", " 1 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 1 0 1 0 0 0 1 0 0 1 0 0 1 0\n", " 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 1 0 0 0\n", " 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0\n", " 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1\n", " 0 0 0 0 1 1 0 1 0 0 0 0 1 0 0 0 0 0 0 0 1 0 1 0 0 1 0 0 0 0 0 0 0 1 0 0 0\n", " 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 1 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0\n", " 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of Logistic Regression\n", "Model XGBoost loaded from awal/XGBoost_model.pkl\n", "Model: XGBoost\n", "Predicted Labels: [0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 1 1 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0\n", " 0 0 0 0 1 0 0 0 0 0 1 0 1 0 1 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 1 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 1 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of XGBoost\n", "Test predictions saved to 'awal/test_predictions_test_size_0.25.csv'\n", "Model: <PERSON><PERSON>\n", "Predicted 'ham' (0): 388\n", "Predicted 'spam' (1): 265\n", "\n", "Model: SVM (linear kernel)\n", "Predicted 'ham' (0): 542\n", "Predicted 'spam' (1): 111\n", "\n", "Model: SVM (poly kernel)\n", "Predicted 'ham' (0): 263\n", "Predicted 'spam' (1): 390\n", "\n", "Model: SVM (rbf kernel)\n", "Predicted 'ham' (0): 444\n", "Predicted 'spam' (1): 209\n", "\n", "Model: SVM (sigmoid kernel)\n", "Predicted 'ham' (0): 544\n", "Predicted 'spam' (1): 109\n", "\n", "Model: Random Forest\n", "Predicted 'ham' (0): 175\n", "Predicted 'spam' (1): 478\n", "\n", "Model: Logistic Regression\n", "Predicted 'ham' (0): 562\n", "Predicted 'spam' (1): 91\n", "\n", "Model: XGBoost\n", "Predicted 'ham' (0): 620\n", "Predicted 'spam' (1): 33\n", "\n", "Prediction statistics saved to 'awal/prediction_statistics_test_size_0.25.csv'\n", "Metrics results saved to 'awal/metrics_results_test_size_0.25.csv'\n", "\n", "Using test_size = 0.3 for splitting the data\n", "\n", "\n", "Training and Validation Phase\n", "\n", "Model Naive <PERSON>es saved to awal/Naive_<PERSON>_model.pkl\n", "Model: <PERSON><PERSON>\n", "Validation Accuracy: 0.7785\n", "Recall: 0.7785, Precision: 0.7959, F1 Score: 0.7771\n", "\n", "Model SVM (linear kernel) saved to awal/SVM_(linear_kernel)_model.pkl\n", "Model: SVM (linear kernel)\n", "Validation Accuracy: 0.8961\n", "Recall: 0.8961, Precision: 0.9073, F1 Score: 0.8959\n", "\n", "Model SVM (poly kernel) saved to awal/SVM_(poly_kernel)_model.pkl\n", "Model: SVM (poly kernel)\n", "Validation Accuracy: 0.8739\n", "Recall: 0.8739, Precision: 0.8752, F1 Score: 0.8735\n", "\n", "Model SVM (rbf kernel) saved to awal/SVM_(rbf_kernel)_model.pkl\n", "Model: SVM (rbf kernel)\n", "Validation Accuracy: 0.9046\n", "Recall: 0.9046, Precision: 0.9100, F1 Score: 0.9046\n", "\n", "Model SVM (sigmoid kernel) saved to awal/SVM_(sigmoid_kernel)_model.pkl\n", "Model: SVM (sigmoid kernel)\n", "Validation Accuracy: 0.8927\n", "Recall: 0.8927, Precision: 0.9038, F1 Score: 0.8925\n", "\n", "Model Random Forest saved to awal/Random_Forest_model.pkl\n", "Model: Random Forest\n", "Validation Accuracy: 0.8620\n", "Recall: 0.8620, Precision: 0.8669, F1 Score: 0.8610\n", "\n", "Model Logistic Regression saved to awal/Logistic_Regression_model.pkl\n", "Model: Logistic Regression\n", "Validation Accuracy: 0.8944\n", "Recall: 0.8944, Precision: 0.9018, F1 Score: 0.8943\n", "\n", "Model XGBoost saved to awal/XGBoost_model.pkl\n", "Model: XGBoost\n", "Validation Accuracy: 0.8944\n", "Recall: 0.8944, Precision: 0.9034, F1 Score: 0.8943\n", "\n", "\n", "Testing Phase\n", "\n", "Model Naive Bayes loaded from awal/Naive_Bay<PERSON>_model.pkl\n", "Model: <PERSON><PERSON>\n", "Predicted Labels: [1 1 1 0 1 1 0 1 0 0 1 1 1 1 1 0 0 1 0 0 1 1 0 0 0 1 0 0 0 0 1 1 1 1 0 1 0\n", " 0 0 1 0 1 0 1 0 0 0 1 1 1 0 0 0 0 0 1 0 0 0 0 1 1 1 1 0 1 1 0 1 0 1 0 1 0\n", " 0 0 1 1 0 0 1 1 0 0 0 0 1 1 0 0 1 0 1 1 0 1 0 0 0 1 0 1 0 0 0 0 1 0 1 0 1\n", " 1 1 0 0 1 0 0 0 1 0 1 0 0 1 0 0 1 1 1 1 1 1 0 1 1 0 0 0 1 1 1 1 1 0 1 0 1\n", " 0 1 1 1 1 1 0 0 1 0 0 0 1 1 1 0 1 0 1 0 0 0 1 0 0 0 0 1 1 0 1 0 0 1 0 1 0\n", " 0 0 0 0 0 1 0 0 1 1 1 1 0 1 0 1 0 0 0 0 1 0 1 1 0 1 0 0 0 0 0 0 0 0 1 1 0\n", " 1 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 0 1 1 1 1 0 1 0 0 0 1 0 1 0\n", " 1 0 1 1 0 0 0 0 1 1 1 1 0 0 0 0 0 1 1 0 1 0 1 1 0 0 1 1 1 0 1 0 1 1 0 1 1\n", " 0 0 1 1 1 1 1 0 1 1 1 1 1 0 0 0 0 0 0 1 1 0 0 0 1 0 0 0 0 0 0 0 1 0 1 0 1\n", " 0 1 1 0 1 1 0 1 0 0 1 1 0 0 1 1 0 0 1 1 1 0 0 1 0 1 0 0 0 1 1 0 1 1 0 1 0\n", " 1 0 0 0 1 0 0 0 1 1 1 1 0 0 1 0 0 0 0 1 0 1 0 0 0 0 1 1 0 0 0 0 1 1 0 0 0\n", " 1 0 1 0 0 1 1 0 1 0 0 0 0 1 0 0 0 0 0 1 0 0 1 0 0 1 1 1 0 0 0 0 0 0 0 1 1\n", " 0 0 0 1 0 0 0 1 1 0 0 0 1 0 0 0 1 1 0 1 1 0 1 1 0 1 1 0 1 0 0 1 0 0 0 0 1\n", " 0 0 1 1 1 1 0 1 0 1 1 0 1 0 1 1 0 0 0 0 1 0 1 0 1 0 0 0 0 1 0 0 0 1 0 0 1\n", " 0 0 1 0 1 1 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 1 0 0 0 0 1 0 0 0 1 1 1\n", " 0 0 1 0 0 0 0 0 0 1 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 1 1\n", " 0 0 1 0 0 1 0 1 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 1 1 0 1 1 0 0 0 0 0 0 0 0 0\n", " 0 0 0 1 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of Na<PERSON>\n", "Model SVM (linear kernel) loaded from awal/SVM_(linear_kernel)_model.pkl\n", "Model: SVM (linear kernel)\n", "Predicted Labels: [0 0 0 0 0 0 0 1 0 0 1 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 0\n", " 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 1 0 1 1 0 1 0 0 0 0 0\n", " 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 1\n", " 1 1 0 0 1 1 0 0 1 0 0 0 0 0 0 0 0 1 1 0 0 0 1 1 1 0 0 0 0 1 0 0 1 0 1 0 1\n", " 0 0 0 0 1 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 0 0 0 1 0 1 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 1 1 0 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 1 1 1 0 0 1 1 0 1 0 0 0 0 0 0 0 0 0\n", " 1 0 1 1 0 0 0 0 1 0 1 1 0 0 0 0 0 1 1 0 0 0 1 0 0 0 1 0 0 0 1 0 1 0 0 0 0\n", " 0 0 1 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 1 0 0 0 0\n", " 1 0 1 0 1 0 0 1 0 0 0 1 0 0 1 0 0 0 1 0 0 1 0 1 0 1 0 0 0 1 0 0 1 0 0 1 0\n", " 1 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 1 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 1 0 0 1 0 0 0 0 0 0 1\n", " 0 0 0 0 0 0 0 1 1 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1\n", " 0 0 0 0 0 1 0 1 0 1 0 0 1 0 1 1 0 0 0 0 1 0 1 1 0 1 0 0 0 0 0 0 0 1 0 0 0\n", " 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0\n", " 0 0 1 0 0 0 0 0 0 0 0 1 1 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 1 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of SVM (linear kernel)\n", "Model SVM (poly kernel) loaded from awal/SVM_(poly_kernel)_model.pkl\n", "Model: SVM (poly kernel)\n", "Predicted Labels: [0 0 0 0 1 1 0 1 0 1 1 1 1 0 0 0 1 1 0 1 0 0 0 0 1 0 0 1 1 0 0 0 0 0 0 1 0\n", " 0 1 1 1 1 1 1 1 1 1 1 1 1 0 0 1 0 1 1 1 0 0 1 0 1 0 1 1 1 0 1 1 0 1 0 1 0\n", " 1 1 0 1 0 0 1 1 0 1 1 0 1 1 0 0 0 0 0 1 1 0 0 1 0 1 1 1 0 0 0 0 0 0 1 1 1\n", " 1 1 1 1 1 1 1 1 1 0 1 1 0 0 0 0 0 1 1 0 0 0 0 1 1 0 0 0 0 1 1 1 1 0 1 0 1\n", " 1 0 1 1 1 1 0 0 1 0 1 1 1 1 0 0 1 0 1 1 1 1 0 0 0 1 1 1 0 0 1 0 0 1 0 1 1\n", " 0 1 0 1 0 1 1 0 1 1 1 0 1 1 1 1 1 0 1 0 1 1 0 1 0 1 1 0 0 0 1 1 1 1 0 1 0\n", " 1 1 1 0 0 1 0 0 1 1 1 0 1 0 1 0 1 1 0 1 1 1 0 0 1 1 0 1 1 1 0 0 1 0 0 1 1\n", " 1 1 1 1 1 1 1 0 1 0 1 1 0 0 0 0 1 1 1 1 1 0 1 0 1 0 1 1 1 1 1 1 1 0 0 0 0\n", " 0 0 1 1 1 1 1 0 1 0 1 0 1 0 1 0 0 0 1 1 0 1 0 1 1 0 1 0 0 1 0 1 1 0 0 0 1\n", " 1 1 1 1 0 1 1 1 0 1 1 1 1 0 1 0 0 1 1 1 0 1 0 1 0 1 0 0 1 1 1 1 1 1 0 1 0\n", " 1 0 1 1 1 1 1 0 0 0 1 1 1 0 1 0 1 0 1 0 1 1 1 1 1 1 1 0 1 1 0 1 1 1 1 1 0\n", " 1 1 1 0 0 1 1 1 0 1 1 1 0 1 0 1 1 0 1 1 1 1 1 1 1 0 1 0 1 1 1 1 1 1 1 1 1\n", " 0 1 0 1 1 1 0 1 1 0 1 0 1 0 1 1 1 1 0 0 0 1 0 1 0 1 1 1 1 1 1 1 0 1 1 1 1\n", " 1 1 0 1 1 1 1 1 0 1 0 1 1 0 1 1 1 1 0 1 1 1 1 1 1 1 0 0 1 1 1 1 0 1 1 1 1\n", " 0 0 1 0 1 0 0 1 1 1 1 0 0 1 0 0 1 0 1 1 0 0 0 1 1 0 1 1 0 0 1 1 1 1 1 0 1\n", " 1 1 1 1 1 0 1 1 0 0 1 1 0 1 1 1 0 1 1 1 1 1 1 1 1 1 1 1 1 1 0 1 1 0 1 1 0\n", " 1 1 1 1 1 0 1 0 1 0 1 1 0 1 0 1 1 1 1 0 1 1 0 1 1 0 1 1 1 1 0 1 0 1 1 1 1\n", " 1 1 0 1 0 1 1 0 1 1 1 1 1 1 1 1 1 1 0 1 1 0 1 1]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of SVM (poly kernel)\n", "Model SVM (rbf kernel) loaded from awal/SVM_(rbf_kernel)_model.pkl\n", "Model: SVM (rbf kernel)\n", "Predicted Labels: [0 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 1 0 0 0 0 1 0 0 1 0 0 0 0 0 0 0 1 0\n", " 0 0 0 1 0 1 1 1 0 1 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0\n", " 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 0 0 0 1 1\n", " 0 1 0 0 0 0 1 1 1 0 0 1 0 0 0 0 0 0 1 0 0 0 0 1 1 0 0 0 0 1 0 0 1 0 1 0 0\n", " 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 1 0 0 0 0 0 1 0 0 0 0 0 0 0 0 1 1\n", " 0 0 0 1 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 1 1 0 1 0 0 0\n", " 1 1 0 0 0 0 0 0 1 1 1 0 1 0 1 0 1 1 0 1 1 1 0 0 0 1 0 0 1 0 0 0 1 0 0 0 1\n", " 0 1 0 1 1 1 1 0 1 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 1 0 1 0 0 0 0 1 1 0 0 0 0\n", " 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 1 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 1 0 0 0 1 0 1 0 1 0 0 1 1 0 1 0 0 0 0 0\n", " 1 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 1 0 1 1 1 1 1 0 0 0 0 0 0 1 1 1 0\n", " 0 1 0 0 0 0 0 1 0 0 1 1 0 0 0 1 0 0 1 0 0 1 0 0 0 0 1 0 0 1 1 1 1 1 0 0 0\n", " 0 1 0 0 1 0 0 0 1 0 1 0 0 0 1 1 0 0 0 0 0 0 0 0 0 0 1 1 0 0 1 0 0 1 1 1 1\n", " 0 1 0 0 1 1 1 1 0 0 0 1 1 0 1 0 1 1 0 1 1 1 1 0 0 0 0 0 1 0 1 1 0 1 1 0 0\n", " 0 0 0 0 0 0 1 0 0 1 1 0 0 1 0 0 1 0 1 1 0 0 0 1 0 0 1 1 0 0 0 1 0 1 0 0 0\n", " 1 1 0 1 1 0 0 1 0 0 1 1 0 1 1 0 0 1 0 1 0 0 1 0 1 1 1 0 1 1 0 1 1 0 1 1 0\n", " 1 1 0 1 1 0 1 0 1 0 1 0 0 1 0 1 1 1 0 0 1 1 0 0 0 0 0 0 1 1 0 1 0 1 1 1 1\n", " 1 1 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 0 1 1 0 1 1]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of SVM (rbf kernel)\n", "Model SVM (sigmoid kernel) loaded from awal/SVM_(sigmoid_kernel)_model.pkl\n", "Model: SVM (sigmoid kernel)\n", "Predicted Labels: [0 0 0 0 0 0 0 1 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 0\n", " 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 1 0 0 0 0 0\n", " 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 1\n", " 1 1 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 1 1 0 0 0 1 1 1 0 0 0 0 1 0 0 1 0 1 0 1\n", " 0 0 0 0 1 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 1 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0\n", " 1 1 0 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 1 1 1 0 0 1 1 0 1 0 0 0 0 0 0 0 0 0\n", " 0 0 1 1 0 0 0 0 1 0 1 1 0 0 0 0 0 1 1 0 0 0 1 0 0 0 1 0 0 0 1 0 1 0 0 0 0\n", " 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 0 0\n", " 0 0 1 0 1 0 0 1 0 0 0 1 0 0 0 0 0 0 1 0 0 1 0 1 0 1 0 0 0 1 0 0 1 0 0 1 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 1 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 0 0 1\n", " 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1\n", " 0 0 0 0 0 1 0 1 0 1 0 0 1 0 1 1 0 0 0 0 1 0 1 0 0 1 0 0 0 0 0 0 0 1 0 0 0\n", " 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0\n", " 0 0 1 0 0 0 0 0 0 0 0 1 1 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of SVM (sigmoid kernel)\n", "Model Random Forest loaded from awal/Random_Forest_model.pkl\n", "Model: Random Forest\n", "Predicted Labels: [1 0 1 0 0 1 0 1 0 0 1 1 0 0 0 1 1 1 0 1 0 0 0 1 1 0 1 1 1 0 1 0 0 0 0 1 0\n", " 0 1 1 1 1 1 1 1 1 1 1 0 1 1 1 1 0 1 0 1 0 1 1 1 0 1 1 1 1 0 1 1 0 0 1 0 0\n", " 1 1 1 1 0 0 1 1 0 1 1 0 1 1 0 1 1 0 0 0 1 0 1 1 0 0 1 0 0 0 1 1 1 1 1 1 1\n", " 1 1 1 0 1 1 1 1 1 0 1 1 1 0 0 1 0 1 1 0 1 1 1 1 1 1 1 0 1 1 1 1 1 0 1 0 1\n", " 1 0 1 0 1 1 1 0 0 0 1 1 1 1 0 1 1 0 1 1 1 1 0 1 0 1 1 1 1 1 1 1 0 1 1 1 1\n", " 0 1 0 1 1 0 1 0 0 1 1 1 1 1 1 1 1 0 1 0 1 1 0 1 1 1 0 1 1 0 1 1 0 1 0 0 0\n", " 0 1 1 1 1 1 0 1 1 1 1 0 1 0 1 0 1 1 0 1 1 1 0 0 1 1 1 1 1 0 1 1 1 0 1 0 1\n", " 1 1 1 1 1 1 1 0 1 0 1 1 1 1 1 0 1 1 1 1 1 0 1 0 1 0 1 1 0 1 1 1 1 0 1 1 0\n", " 0 1 1 1 0 0 1 1 1 1 1 0 1 0 0 0 0 0 1 1 0 1 1 1 1 1 0 1 1 1 1 1 1 1 0 0 1\n", " 1 0 1 0 0 0 1 1 1 1 1 1 1 0 1 0 0 1 1 1 1 1 1 1 0 1 0 1 1 1 1 1 1 0 1 1 1\n", " 1 0 1 1 1 1 1 0 1 1 1 1 0 1 1 1 1 1 1 0 1 1 1 1 1 1 1 1 1 1 0 1 1 1 1 1 1\n", " 1 1 1 1 0 0 1 1 1 1 1 1 1 0 1 1 1 0 1 1 1 1 1 1 1 1 1 0 1 1 1 1 1 1 1 0 1\n", " 0 1 1 1 1 1 0 0 1 1 1 1 1 0 1 1 1 1 1 1 1 1 0 1 0 1 1 1 1 1 1 0 1 1 1 1 1\n", " 1 1 1 1 0 1 1 1 1 1 1 1 1 0 1 1 1 1 0 1 1 1 1 1 1 0 0 1 1 1 1 1 1 1 1 1 1\n", " 1 1 1 0 0 1 1 1 0 1 1 1 1 1 0 0 1 0 1 1 0 0 0 1 1 1 1 1 0 1 0 1 1 1 1 1 1\n", " 1 1 1 1 1 0 1 1 1 1 1 1 0 1 1 1 0 1 1 1 1 1 1 1 1 1 0 1 1 1 1 1 1 1 1 1 0\n", " 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 1 1 1 1 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1\n", " 1 1 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 1 1 0 1 1]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of Random Forest\n", "Model Logistic Regression loaded from awal/Logistic_Regression_model.pkl\n", "Model: Logistic Regression\n", "Predicted Labels: [0 0 0 0 0 0 0 0 0 1 1 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 0\n", " 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 1 0 0 0 1 1 0 0 0 0 0\n", " 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0\n", " 0 1 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 1 1 0 0 0 1 1 1 0 0 0 0 1 0 0 1 0 1 0 1\n", " 0 0 0 0 1 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 0 0 0 1 0 1 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 1 1 0 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 1 1 1 0 0 1 1 0 1 0 1 1 0 0 0 0 0 0\n", " 0 0 1 0 0 0 0 0 1 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0 0 0 0 0 1 0 0 0 0\n", " 0 0 1 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 0 0\n", " 1 0 1 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 1 0 1 0 0 0 1 0 0 1 0 0 1 0\n", " 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 1 0 0 0\n", " 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 1 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1\n", " 0 0 0 0 1 1 0 1 0 0 0 0 1 0 1 0 0 0 0 0 1 0 1 0 0 1 0 0 0 0 0 0 0 1 0 0 0\n", " 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 1 0\n", " 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of Logistic Regression\n", "Model XGBoost loaded from awal/XGBoost_model.pkl\n", "Model: XGBoost\n", "Predicted Labels: [0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 1 1 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1\n", " 0 0 0 0 1 0 0 0 0 0 1 0 1 0 1 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 1 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 1 0 0 0 0 0 0 0 0 0 0 1 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of XGBoost\n", "Test predictions saved to 'awal/test_predictions_test_size_0.3.csv'\n", "Model: <PERSON><PERSON>\n", "Predicted 'ham' (0): 395\n", "Predicted 'spam' (1): 258\n", "\n", "Model: SVM (linear kernel)\n", "Predicted 'ham' (0): 528\n", "Predicted 'spam' (1): 125\n", "\n", "Model: SVM (poly kernel)\n", "Predicted 'ham' (0): 236\n", "Predicted 'spam' (1): 417\n", "\n", "Model: SVM (rbf kernel)\n", "Predicted 'ham' (0): 439\n", "Predicted 'spam' (1): 214\n", "\n", "Model: SVM (sigmoid kernel)\n", "Predicted 'ham' (0): 545\n", "Predicted 'spam' (1): 108\n", "\n", "Model: Random Forest\n", "Predicted 'ham' (0): 165\n", "Predicted 'spam' (1): 488\n", "\n", "Model: Logistic Regression\n", "Predicted 'ham' (0): 552\n", "Predicted 'spam' (1): 101\n", "\n", "Model: XGBoost\n", "Predicted 'ham' (0): 616\n", "Predicted 'spam' (1): 37\n", "\n", "Prediction statistics saved to 'awal/prediction_statistics_test_size_0.3.csv'\n", "Metrics results saved to 'awal/metrics_results_test_size_0.3.csv'\n", "\n", "Using test_size = 0.35 for splitting the data\n", "\n", "\n", "Training and Validation Phase\n", "\n", "Model Naive <PERSON>es saved to awal/Naive_<PERSON>_model.pkl\n", "Model: <PERSON><PERSON>\n", "Validation Accuracy: 0.7766\n", "Recall: 0.7766, Precision: 0.7956, F1 Score: 0.7749\n", "\n", "Model SVM (linear kernel) saved to awal/SVM_(linear_kernel)_model.pkl\n", "Model: SVM (linear kernel)\n", "Validation Accuracy: 0.8964\n", "Recall: 0.8964, Precision: 0.9068, F1 Score: 0.8962\n", "\n", "Model SVM (poly kernel) saved to awal/SVM_(poly_kernel)_model.pkl\n", "Model: SVM (poly kernel)\n", "Validation Accuracy: 0.8701\n", "Recall: 0.8701, Precision: 0.8722, F1 Score: 0.8695\n", "\n", "Model SVM (rbf kernel) saved to awal/SVM_(rbf_kernel)_model.pkl\n", "Model: SVM (rbf kernel)\n", "Validation Accuracy: 0.9051\n", "Recall: 0.9051, Precision: 0.9099, F1 Score: 0.9051\n", "\n", "Model SVM (sigmoid kernel) saved to awal/SVM_(sigmoid_kernel)_model.pkl\n", "Model: SVM (sigmoid kernel)\n", "Validation Accuracy: 0.8891\n", "Recall: 0.8891, Precision: 0.9006, F1 Score: 0.8888\n", "\n", "Model Random Forest saved to awal/Random_Forest_model.pkl\n", "Model: Random Forest\n", "Validation Accuracy: 0.8496\n", "Recall: 0.8496, Precision: 0.8559, F1 Score: 0.8483\n", "\n", "Model Logistic Regression saved to awal/Logistic_Regression_model.pkl\n", "Model: Logistic Regression\n", "Validation Accuracy: 0.8876\n", "Recall: 0.8876, Precision: 0.8951, F1 Score: 0.8875\n", "\n", "Model XGBoost saved to awal/XGBoost_model.pkl\n", "Model: XGBoost\n", "Validation Accuracy: 0.8891\n", "Recall: 0.8891, Precision: 0.8983, F1 Score: 0.8889\n", "\n", "\n", "Testing Phase\n", "\n", "Model Naive Bayes loaded from awal/Naive_Bay<PERSON>_model.pkl\n", "Model: <PERSON><PERSON>\n", "Predicted Labels: [1 1 1 0 1 1 0 1 1 0 1 1 1 1 1 0 1 1 0 0 1 1 0 0 0 1 0 0 0 0 1 1 1 1 0 1 0\n", " 0 0 1 0 1 0 0 0 0 0 1 1 1 0 0 0 0 0 1 0 0 0 0 1 1 1 1 0 1 1 0 1 0 1 0 1 0\n", " 0 0 1 1 0 0 1 1 0 0 0 0 1 1 0 0 1 0 1 1 0 1 1 0 0 1 0 1 0 0 0 0 1 0 1 0 1\n", " 1 1 0 0 1 0 0 0 1 0 1 0 0 1 0 0 1 1 0 1 1 1 0 1 1 0 0 0 1 1 0 1 1 0 1 0 1\n", " 0 1 1 1 1 1 0 0 1 0 0 0 1 1 1 0 0 0 1 0 0 0 1 0 0 0 0 1 1 0 1 0 0 1 0 0 0\n", " 0 0 0 0 0 1 0 0 1 1 1 1 0 1 0 1 0 0 0 0 1 0 1 1 0 1 0 0 0 0 0 0 0 0 1 1 0\n", " 1 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 0 1 1 1 1 0 1 0 0 0 1 0 1 0\n", " 0 0 1 1 0 0 0 0 1 1 1 1 0 0 0 0 0 0 1 0 1 0 1 1 0 0 1 1 1 0 1 0 1 1 0 1 1\n", " 0 0 1 1 1 1 1 0 1 1 1 1 1 0 0 0 0 0 0 1 1 0 0 0 1 0 0 0 0 0 0 1 1 0 1 0 1\n", " 1 1 1 0 1 1 0 1 0 0 1 1 0 0 1 1 0 0 1 1 1 0 0 1 0 1 0 0 0 1 1 0 1 1 0 1 0\n", " 0 0 1 0 1 0 0 0 1 1 1 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 1 1 0 0 0 0 1 1 0 0 0\n", " 1 0 1 0 0 1 1 0 1 0 0 0 0 1 0 0 0 0 0 1 0 0 1 0 0 1 1 0 0 0 0 0 0 0 1 1 1\n", " 0 0 0 1 0 0 0 1 1 0 0 0 1 0 0 0 1 1 0 1 1 0 1 1 0 1 1 0 1 0 0 1 0 0 0 0 1\n", " 0 0 1 1 1 1 0 1 0 1 1 0 1 0 1 1 0 0 0 0 1 0 1 0 1 0 0 0 0 1 0 0 0 1 0 0 1\n", " 0 0 1 0 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 1 0 0 0 0 1 0 0 0 1 1 1\n", " 0 0 1 0 0 0 0 0 0 1 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 1 1\n", " 0 0 1 0 0 1 0 1 0 0 0 0 1 0 1 0 0 0 1 0 0 0 0 1 1 0 1 1 0 0 0 0 0 0 0 0 0\n", " 0 0 1 1 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of Na<PERSON>\n", "Model SVM (linear kernel) loaded from awal/SVM_(linear_kernel)_model.pkl\n", "Model: SVM (linear kernel)\n", "Predicted Labels: [0 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 0\n", " 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 1 1 0 1 0 0 0 0 0\n", " 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 1\n", " 1 1 0 0 1 1 0 0 1 0 0 0 0 0 0 0 0 1 1 0 0 0 1 1 1 0 0 0 0 1 0 0 1 0 1 0 1\n", " 0 0 0 0 1 0 0 0 0 0 0 1 0 1 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 1 0 0 1 0 1 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0\n", " 1 1 0 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 1 1 1 0 0 1 1 0 1 0 0 0 0 0 0 0 0 0\n", " 0 0 1 1 0 0 0 0 1 0 1 1 0 0 0 0 0 1 1 0 0 0 1 0 0 0 0 0 0 0 1 0 1 0 0 0 0\n", " 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 1 0 1 1 0 0 0 1\n", " 1 0 1 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 1 0 1 0 0 0 1 0 0 1 0 0 1 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 1 0 0 0\n", " 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 1 1 0 0 0 0 0 1 0 0 1 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 1 1 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1\n", " 0 0 0 0 0 1 0 1 0 1 0 0 1 0 1 1 0 0 0 0 1 0 1 1 0 0 0 0 0 0 0 0 0 1 0 0 0\n", " 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0\n", " 0 0 1 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of SVM (linear kernel)\n", "Model SVM (poly kernel) loaded from awal/SVM_(poly_kernel)_model.pkl\n", "Model: SVM (poly kernel)\n", "Predicted Labels: [0 0 0 0 0 1 0 0 1 1 1 1 1 0 0 0 1 1 0 1 0 0 0 0 1 0 0 1 1 0 0 0 0 0 0 1 0\n", " 0 1 1 1 1 1 1 1 0 1 1 1 1 0 0 1 0 1 1 1 0 0 1 0 1 0 1 1 1 0 1 1 0 1 0 1 0\n", " 1 1 0 1 0 0 1 1 0 1 0 0 1 1 0 0 0 0 0 1 1 0 0 1 0 1 1 1 0 0 0 0 0 0 1 1 1\n", " 1 1 1 0 1 1 1 1 1 0 1 1 0 0 0 0 0 1 1 0 0 0 0 1 1 0 0 0 1 1 1 1 1 0 1 0 1\n", " 1 0 1 1 1 1 0 0 1 0 1 1 1 1 0 0 1 0 1 1 1 1 0 1 0 1 1 1 0 0 1 0 0 1 0 1 1\n", " 0 1 0 1 0 1 0 0 1 1 1 0 1 1 1 1 1 0 1 0 1 1 0 1 0 1 1 0 0 0 1 1 1 1 1 1 0\n", " 1 1 1 0 0 1 0 0 1 1 1 0 1 0 1 0 1 1 0 1 1 1 0 0 1 1 0 1 1 1 0 0 1 0 0 0 1\n", " 1 1 1 1 1 1 1 0 1 0 1 1 0 0 0 0 1 1 1 1 0 0 1 0 1 0 1 1 1 1 1 1 1 0 0 0 0\n", " 0 0 1 1 1 1 1 0 1 0 1 0 1 0 1 0 0 0 1 1 0 1 0 1 1 0 1 0 0 1 0 1 1 0 0 0 1\n", " 1 1 1 1 1 1 1 1 0 1 1 1 1 0 1 0 0 1 1 1 0 1 0 1 0 1 0 0 1 1 1 1 1 0 0 1 0\n", " 1 0 1 1 1 1 1 0 0 0 1 1 1 0 1 0 1 0 1 0 1 1 1 1 1 1 1 1 1 1 0 1 1 1 1 1 0\n", " 1 1 1 0 0 1 1 1 0 1 1 1 0 1 0 1 1 0 1 1 1 1 1 1 1 0 1 0 1 1 1 1 1 1 1 1 1\n", " 0 1 0 0 1 1 0 1 1 0 1 0 1 0 1 1 1 1 0 0 0 1 0 1 0 1 1 1 1 1 1 1 0 1 1 1 1\n", " 1 1 0 1 1 1 1 1 0 1 0 1 1 0 1 1 1 1 0 1 1 1 1 1 1 1 0 0 1 1 1 1 0 1 1 1 1\n", " 0 0 1 0 1 0 0 1 0 1 1 0 0 1 0 0 1 0 1 1 0 0 0 1 1 0 1 1 1 0 1 1 0 1 1 0 1\n", " 1 1 1 1 1 0 1 1 0 0 1 1 0 1 1 1 0 1 1 1 1 1 1 1 1 1 1 1 1 1 0 1 1 0 1 1 0\n", " 1 1 1 1 1 0 1 0 1 0 1 1 0 1 0 1 1 1 1 0 1 1 0 1 0 0 1 1 1 1 1 1 1 1 1 1 1\n", " 1 1 1 1 0 1 1 0 1 1 1 1 1 1 1 1 1 1 0 1 1 0 1 1]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of SVM (poly kernel)\n", "Model SVM (rbf kernel) loaded from awal/SVM_(rbf_kernel)_model.pkl\n", "Model: SVM (rbf kernel)\n", "Predicted Labels: [0 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 1 0 0 0 0 1 0 0 1 0 0 0 0 0 0 0 1 0\n", " 0 0 0 1 1 1 0 1 0 1 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0 0\n", " 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 0 0 0 1 1\n", " 0 1 0 0 1 0 1 1 1 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 1 0 0 1 0 1 0 0\n", " 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 1 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 1\n", " 0 0 0 1 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 1 1 0 1 1 0 0\n", " 0 1 0 0 0 0 0 0 1 1 1 0 1 0 1 0 1 1 0 1 1 1 0 0 0 0 0 0 1 0 0 0 1 0 0 0 1\n", " 0 1 0 0 1 1 1 0 1 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0 0 0 1 0 0 0 0 0\n", " 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 1 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 1 0 0 0 1 0 1 0 1 0 0 1 1 0 1 1 0 0 0 0\n", " 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 1 0 1 1 1 1 0 0 0 0 0 0 0 1 1 1 0\n", " 0 1 0 0 0 0 0 1 0 0 1 1 0 0 0 1 0 0 1 0 0 1 0 0 0 0 1 0 0 1 1 1 1 1 0 0 0\n", " 0 1 0 0 1 0 0 0 1 0 1 0 0 0 1 1 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 1 1 1 1\n", " 0 1 0 0 0 1 1 1 0 0 0 1 1 0 1 0 1 1 0 1 0 1 1 0 0 0 0 0 1 0 1 1 0 1 1 0 0\n", " 0 0 0 0 0 0 0 0 0 1 1 0 0 1 0 0 1 0 1 1 0 0 0 1 0 0 1 1 0 0 0 1 0 1 0 0 0\n", " 1 1 0 1 1 0 0 1 0 0 1 1 0 1 1 0 0 1 0 1 0 0 1 0 1 1 0 0 1 1 0 1 1 0 1 1 0\n", " 1 1 0 1 1 0 1 0 1 0 1 0 0 1 0 1 1 1 0 0 1 1 0 0 0 0 0 0 1 1 0 1 0 1 1 1 1\n", " 1 1 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 0 1 1 0 1 1]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of SVM (rbf kernel)\n", "Model SVM (sigmoid kernel) loaded from awal/SVM_(sigmoid_kernel)_model.pkl\n", "Model: SVM (sigmoid kernel)\n", "Predicted Labels: [0 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 0\n", " 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 1 0 0 0 0 0\n", " 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 1\n", " 1 1 0 0 1 1 0 0 1 0 0 0 0 0 0 0 0 1 1 0 0 0 1 1 1 0 0 0 0 1 0 0 1 0 1 0 1\n", " 0 0 0 0 1 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 1 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0\n", " 1 1 0 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 1 1 1 0 0 1 1 0 1 0 0 0 0 0 0 0 0 0\n", " 0 0 1 1 0 0 0 0 1 0 1 1 0 0 0 0 0 1 1 0 0 0 1 0 0 0 0 0 0 0 1 0 1 0 0 0 0\n", " 0 0 0 0 0 1 1 0 1 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 1 0 1 1 0 0 0 0\n", " 1 0 1 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 1 0 1 0 0 0 1 0 0 1 0 0 1 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 1 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1\n", " 0 0 0 0 0 1 0 1 0 1 0 0 1 0 1 1 0 0 0 0 1 0 1 1 0 1 0 0 0 0 0 0 0 1 0 0 0\n", " 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 1 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of SVM (sigmoid kernel)\n", "Model Random Forest loaded from awal/Random_Forest_model.pkl\n", "Model: Random Forest\n", "Predicted Labels: [1 0 1 0 0 1 0 1 0 0 1 1 0 0 0 0 1 1 0 1 0 0 0 1 1 0 1 1 1 0 1 0 1 0 0 1 0\n", " 0 1 1 1 1 1 1 1 1 1 1 0 1 1 1 1 0 1 0 1 0 1 0 1 0 0 1 1 1 0 1 1 0 0 1 0 0\n", " 1 1 1 1 0 0 1 1 1 1 1 1 1 1 0 1 1 0 0 0 1 0 0 1 0 0 1 0 0 0 1 1 1 1 1 1 1\n", " 1 1 1 0 1 1 1 1 0 0 1 1 1 0 0 1 0 1 1 0 1 1 1 1 1 0 1 1 1 1 1 1 1 0 0 0 1\n", " 1 0 1 0 1 1 1 0 1 0 1 1 1 1 1 1 0 0 1 1 1 1 0 1 0 1 1 1 1 1 1 1 0 1 1 1 1\n", " 0 1 0 1 1 0 1 0 0 1 1 1 1 1 1 1 1 0 1 0 1 1 0 1 1 1 0 1 1 0 1 1 0 1 0 0 0\n", " 0 1 1 1 1 1 0 1 1 1 1 0 1 0 1 0 1 1 0 0 1 1 0 0 1 1 1 0 1 0 1 1 1 0 1 0 1\n", " 1 1 1 1 1 1 1 0 1 0 1 1 1 1 1 0 1 1 1 1 1 0 1 0 1 0 0 1 0 1 1 1 1 0 1 1 0\n", " 0 1 1 1 0 0 1 1 1 0 1 0 1 0 0 0 0 0 1 1 0 1 1 1 1 1 0 1 1 1 0 1 1 1 0 0 1\n", " 1 0 0 0 0 0 1 1 1 1 1 1 1 0 1 0 1 1 1 1 1 1 1 1 0 1 0 1 1 1 1 1 1 0 1 1 1\n", " 1 1 1 1 1 1 1 1 1 1 1 1 0 1 1 1 1 1 1 0 1 0 1 1 1 1 1 1 1 1 0 1 1 1 1 1 1\n", " 1 1 1 1 0 1 1 1 1 1 1 1 1 0 1 1 1 0 1 1 1 1 1 1 1 1 1 0 1 1 1 1 1 1 1 1 1\n", " 1 1 1 1 1 1 0 1 1 1 1 1 0 0 1 1 1 1 1 1 1 1 0 1 0 1 1 1 1 1 1 0 1 1 1 1 1\n", " 1 1 0 1 0 1 1 1 1 1 1 1 1 0 1 1 1 1 0 1 0 1 1 1 1 0 0 1 1 1 1 1 1 1 1 1 1\n", " 1 1 1 0 0 0 1 1 0 1 1 0 1 1 0 0 1 0 1 1 1 1 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1\n", " 1 1 1 1 1 0 1 1 1 1 1 1 1 1 1 1 0 1 1 1 1 1 1 1 1 1 0 1 1 1 1 1 1 1 1 1 0\n", " 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 1 1 1 1 1 1 1 1 0 0 1 1 1 1 1 1 1 1 1 1 1 1\n", " 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 1 1 0 1 1]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of Random Forest\n", "Model Logistic Regression loaded from awal/Logistic_Regression_model.pkl\n", "Model: Logistic Regression\n", "Predicted Labels: [0 0 0 0 0 0 0 0 0 1 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0 0\n", " 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1\n", " 0 1 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 1 0 0 0 0 1 1 1 0 0 0 0 1 0 0 1 0 1 0 0\n", " 0 0 0 0 1 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0\n", " 1 1 0 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 0 1 1 1 0 0 1 1 0 0 0 1 1 0 0 0 0 0 0\n", " 0 0 1 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 1 0 0 0 0\n", " 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 0 0\n", " 1 0 1 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 1 0 1 0 1 0 0 0 1 0 0 1 0 0 1 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0\n", " 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0\n", " 0 0 0 0 0 0 0 0 1 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1\n", " 0 0 0 0 0 1 0 1 0 0 0 0 1 0 1 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 0 0 0 1 0 0 0\n", " 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of Logistic Regression\n", "Model XGBoost loaded from awal/XGBoost_model.pkl\n", "Model: XGBoost\n", "Predicted Labels: [0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 1 1 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1\n", " 0 1 0 0 1 0 0 0 0 0 1 0 1 0 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 0 0\n", " 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 1 0 0 1 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 1 0 1 0 0 0 0 0 0 0 0 1 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0\n", " 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]\n", "\n", "Warning: Actual labels are not provided for meaningful evaluation of XGBoost\n", "Test predictions saved to 'awal/test_predictions_test_size_0.35.csv'\n", "Model: <PERSON><PERSON>\n", "Predicted 'ham' (0): 397\n", "Predicted 'spam' (1): 256\n", "\n", "Model: SVM (linear kernel)\n", "Predicted 'ham' (0): 535\n", "Predicted 'spam' (1): 118\n", "\n", "Model: SVM (poly kernel)\n", "Predicted 'ham' (0): 239\n", "Predicted 'spam' (1): 414\n", "\n", "Model: SVM (rbf kernel)\n", "Predicted 'ham' (0): 451\n", "Predicted 'spam' (1): 202\n", "\n", "Model: SVM (sigmoid kernel)\n", "Predicted 'ham' (0): 546\n", "Predicted 'spam' (1): 107\n", "\n", "Model: Random Forest\n", "Predicted 'ham' (0): 167\n", "Predicted 'spam' (1): 486\n", "\n", "Model: Logistic Regression\n", "Predicted 'ham' (0): 566\n", "Predicted 'spam' (1): 87\n", "\n", "Model: XGBoost\n", "Predicted 'ham' (0): 612\n", "Predicted 'spam' (1): 41\n", "\n", "Prediction statistics saved to 'awal/prediction_statistics_test_size_0.35.csv'\n", "Metrics results saved to 'awal/metrics_results_test_size_0.35.csv'\n"]}], "source": ["# Main function to execute the process\n", "def main():\n", "    X_train_val_raw, y_train_val = load_training_data()\n", "    X_train_val, tfidf = feature_extraction(X_train_val_raw)\n", "    X_test, df = load_testing_data(tfidf)\n", "    models = initialize_models()\n", "\n", "    # Define test sizes for splitting\n", "    test_sizes = [0.2, 0.25, 0.3, 0.35]\n", "    # test_sizes = [0.2,0.3]\n", "    \n", "    for test_size in test_sizes:\n", "        print(f\"\\nUsing test_size = {test_size} for splitting the data\\n\")\n", "        \n", "        # Split the training and validation data\n", "        X_train, X_val, y_train, y_val = train_test_split(X_train_val, y_train_val, test_size=test_size, random_state=42)\n", "        \n", "        # Train and validate models\n", "        metrics_results = train_and_validate_models(models, X_train, X_val, y_train, y_val, test_size)\n", "        \n", "        # Test models\n", "        metrics_results += test_models(models, X_test,df, test_size)\n", "        \n", "        # Save metrics results to CSV\n", "        metrics_df = pd.DataFrame(metrics_results, columns=['Model', 'Phase', 'Accuracy', 'Recall', 'Precision', 'F1'])\n", "        metrics_df.to_csv(f'{output_folder}/metrics_results_test_size_{test_size}.csv', index=False)\n", "        print(f\"Metrics results saved to '{output_folder}/metrics_results_test_size_{test_size}.csv'\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}