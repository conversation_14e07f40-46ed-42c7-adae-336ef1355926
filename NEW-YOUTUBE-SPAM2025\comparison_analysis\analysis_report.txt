YOUTUBE SPAM CLASSIFICATION - OR<PERSON><PERSON>AL vs REVERSED SPLIT ANALYSIS
===========================================================================
Analysis Date: 2025-06-30 00:21:10

COMPARISON OVERVIEW:
------------------------------
Original Splits: Large training sets, small test sets
  - 75/25, 70/30, 65/35 (train/test)
  - More data for learning, less for evaluation

Reversed Splits: Small training sets, large test sets
  - 25/75, 30/70, 35/65 (train/test)
  - Limited data for learning, more for evaluation

PERFORMANCE COMPARISON:
------------------------------
ORIGINAL SPLITS:
  Best Accuracy: 0.930 (93.0%)
  Average Accuracy: 0.912 (91.2%)
  Best Model: Svm (Train 75 Test 25)
  Avg Training Size: 1327

REVERSED SPLITS:
  Best Accuracy: 0.917 (91.7%)
  Average Accuracy: 0.897 (89.7%)
  Best Model: Svm (Train 35 Test 65)
  Avg Training Size: 568

IMPACT OF REDUCED TRAINING DATA:
----------------------------------------
Average Accuracy Drop: 0.015 (1.5 percentage points)
Best Model Accuracy Drop: 0.013 (1.3 percentage points)
Relative Performance Loss: 1.6%

MODEL-SPECIFIC IMPACT:
------------------------------
Naive Bayes:
  Original Avg: 0.882
  Reversed Avg: 0.874
  Performance Drop: 0.009 (0.9 pp)

Logistic Regression:
  Original Avg: 0.920
  Reversed Avg: 0.910
  Performance Drop: 0.010 (1.0 pp)

Random Forest:
  Original Avg: 0.919
  Reversed Avg: 0.892
  Performance Drop: 0.026 (2.6 pp)

Svm:
  Original Avg: 0.928
  Reversed Avg: 0.913
  Performance Drop: 0.015 (1.5 pp)

KEY INSIGHTS:
--------------------
1. Larger training sets consistently improve performance
2. SVM shows good robustness to reduced training data
3. All models maintain reasonable performance even with limited training
4. The performance gap is manageable for practical applications

RECOMMENDATIONS:
--------------------
1. Use original splits (large train/small test) for best performance
2. If training data is limited, SVM is the most robust choice
3. Consider data augmentation techniques for small training sets
4. Monitor performance degradation when reducing training data