Model,<PERSON> Size,Best Parameters,Accuracy,Precision,Recall,F1 Score
RandomForest,0.2,"{'n_estimators': 150, 'min_samples_split': 10, 'max_depth': None}",0.914375,0.8928502482073911,0.8903061224489796,0.8898779652376173
<PERSON><PERSON><PERSON><PERSON><PERSON>,0.2,{'alpha': 0.5},0.8868750000000001,0.9062844678639965,0.9056122448979592,0.9056595792417121
XGBoost,0.2,"{'n_estimators': 50, 'max_depth': 6, 'learning_rate': 0.2}",0.9168749999999999,0.9159637259577684,0.9081632653061225,0.9080389030612245
SVM_Linear,0.2,{'C': 1},0.9112500000000001,0.9265821818601552,0.923469387755102,0.9234853254070078
SVM_RBF,0.2,"{'gamma': 'scale', 'C': 10}",0.905625,0.9186182560857428,0.9183673469387755,0.9183971344421732
SVM_Poly,0.2,"{'degree': 2, 'C': 1}",0.89625,0.9171246769217665,0.9132653061224489,0.9132653061224489
SVM_Sigmoid,0.2,"{'gamma': 'scale', 'C': 1}",0.9006250000000001,0.9112313781560791,0.9081632653061225,0.9081823904884093
RandomForest,0.25,"{'n_estimators': 50, 'min_samples_split': 2, 'max_depth': None}",0.9079470198675497,0.9068089281405158,0.9059304703476483,0.9058216519875019
NaiveBayes,0.25,{'alpha': 0.5},0.8894039735099337,0.8977959493819182,0.8977505112474438,0.897758210757139
XGBoost,0.25,"{'n_estimators': 50, 'max_depth': 6, 'learning_rate': 0.2}",0.9158940397350992,0.918343774683243,0.9100204498977505,0.9097255363709769
SVM_Linear,0.25,{'C': 1},0.9019867549668874,0.9288195802077478,0.9263803680981595,0.9263415677634502
SVM_RBF,0.25,"{'gamma': 'scale', 'C': 1}",0.8980132450331126,0.934796348428164,0.9304703476482618,0.9303795626724769
SVM_Poly,0.25,"{'degree': 2, 'C': 1}",0.8860927152317881,0.9123979254315455,0.9100204498977505,0.909973027266439
SVM_Sigmoid,0.25,"{'gamma': 'scale', 'C': 1}",0.8953642384105962,0.9129398041107831,0.9100204498977505,0.9099526942124325
RandomForest,0.3,"{'n_estimators': 150, 'min_samples_split': 10, 'max_depth': None}",0.9234383000512032,0.908754362625934,0.9080068143100511,0.9078603966676606
NaiveBayes,0.3,{'alpha': 0.5},0.8891397849462365,0.8931840878383791,0.8926746166950597,0.8927195027586003
XGBoost,0.3,"{'n_estimators': 50, 'max_depth': 6, 'learning_rate': 0.2}",0.9170148489503328,0.9183239696131124,0.909710391822828,0.9095383681613216
SVM_Linear,0.3,{'C': 1},0.9034203789042499,0.9198556391118067,0.9165247018739353,0.9165247018739353
SVM_RBF,0.3,"{'gamma': 'scale', 'C': 10}",0.9055657962109575,0.9238372682947672,0.9233390119250426,0.9233710733990003
SVM_Poly,0.3,"{'degree': 2, 'C': 1}",0.8862596006144393,0.9058307220782265,0.9011925042589438,0.9011494876214138
SVM_Sigmoid,0.3,"{'gamma': 'scale', 'C': 1}",0.9019918074756784,0.9067566877083141,0.9045996592844975,0.9046273481546317
RandomForest,0.35,"{'n_estimators': 150, 'min_samples_split': 10, 'max_depth': None}",0.9147332743884468,0.9035123145749941,0.9021897810218978,0.9019963747094534
NaiveBayes,0.35,{'alpha': 0.5},0.8863336280577659,0.8850389232176527,0.8846715328467153,0.8847025146982882
XGBoost,0.35,"{'n_estimators': 100, 'max_depth': 6, 'learning_rate': 0.1}",0.916274683171235,0.9117232535727299,0.9021897810218978,0.9018916154200383
SVM_Linear,0.35,{'C': 1},0.9001473622163279,0.9104322899904949,0.908029197080292,0.9080193967087272
SVM_RBF,0.35,"{'gamma': 'scale', 'C': 10}",0.9001473622163279,0.9184246632125899,0.9182481751824818,0.9182659573236085
SVM_Poly,0.35,"{'degree': 2, 'C': 1}",0.8886472148541114,0.8982390988355766,0.8948905109489051,0.8948434646534835
SVM_Sigmoid,0.35,"{'gamma': 'scale', 'C': 1}",0.9032242852932508,0.900979774565541,0.8992700729927007,0.8992790893345403
