{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import os\n", "import re\n", "import joblib\n", "import xgboost as xgb\n", "from joblib import Parallel, delayed\n", "from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold\n", "from sklearn.naive_bayes import GaussianNB\n", "from sklearn.svm import SVC\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import classification_report, accuracy_score, confusion_matrix, recall_score, precision_score, f1_score\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.feature_extraction.text import ENGLISH_STOP_WORDS\n", "\n", "\n", "# Create directory if not exists\n", "output_folder = 'majorityvote'\n", "os.makedirs(output_folder, exist_ok=True)\n"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["# Function to preprocess text data\n", "def preprocess_text(text):\n", "    # Remove URLs, special characters, numbers, and make lowercase\n", "    text = re.sub(r\"http\\S+|www\\S+|https\\S+\", '', text, flags=re.MULTILINE)\n", "    text = re.sub(r'[^A-Za-z\\s]', '', text)\n", "    text = text.lower()\n", "    \n", "    # Tokenize and remove stopwords\n", "    tokens = text.split()\n", "    cleaned_tokens = [word for word in tokens if word not in ENGLISH_STOP_WORDS]\n", "    \n", "    # Join tokens back to string\n", "    cleaned_text = ' '.join(cleaned_tokens)\n", "    return cleaned_text"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["# Function to load and preprocess training dataset\n", "def load_training_data():\n", "    # Load training dataset\n", "    train_val_dataset = pd.read_csv('../dataset/youtube_spam.csv')  # Load the uploaded training and validation dataset\n", "\n", "    # Preprocess 'CONTENT' column\n", "    train_val_dataset['clean_content'] = train_val_dataset['CONTENT'].apply(preprocess_text)\n", "    \n", "    # Use only 'CONTENT' as features and 'CLASS' as the target variable\n", "    X_train_val_raw = train_val_dataset['clean_content']  # Features (content of the comments)\n", "    y_train_val = train_val_dataset['CLASS']        # Target variable (spam or not spam)\n", "\n", "    return X_train_val_raw, y_train_val"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["\n", "# Function to perform feature extraction using TF-IDF\n", "def feature_extraction(X_train_val_raw):\n", "    # Convert text data to numerical data using TF-IDF Vectorizer\n", "    tfidf = TfidfVectorizer(stop_words='english', max_features=5000)\n", "    X_train_val = tfidf.fit_transform(X_train_val_raw)\n", "    return X_train_val, tfidf\n", "\n", "# Function to load and preprocess testing dataset\n", "def load_testing_data(tfidf):\n", "    # Load and preprocess the testing dataset\n", "    test_dataset = pd.read_excel('../dataset/youtube_comments_i6IOiUi6IYY.xlsx')  # Load the testing dataset\n", "    test_dataset['cleaned_comment'] = test_dataset['Comment'].apply(preprocess_text)  # Preprocess 'Comment' column\n", "    X_test_raw = test_dataset['cleaned_comment']  # Use the cleaned comments for testing\n", "    X_test = tfidf.transform(X_test_raw)  # Transform the test dataset using the same TF-IDF Vectorizer\n", "\n", "    # Add original and cleaned comments to dataframe for analysis\n", "    df = pd.DataFrame({'comment': test_dataset['Comment'], 'clean_comment': test_dataset['cleaned_comment']})\n", "\n", "    # Save the processed test dataset to CSV and Excel\n", "    test_dataset.to_csv(f'{output_folder}/processed_test_dataset.csv', index=False)\n", "    test_dataset.to_excel(f'{output_folder}/processed_test_dataset.xlsx', index=False)\n", "    print(f\"Processed test dataset saved to '{output_folder}/processed_test_dataset.csv' and '{output_folder}/processed_test_dataset.xlsx'\")\n", "\n", "    # return X_test\n", "    return X_test, df\n"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["\n", "# Function to initialize models\n", "def initialize_models():\n", "    return {\n", "        'Naive Bayes': GaussianNB(),\n", "        'SVM (linear kernel)': SVC(kernel='linear'),\n", "        'SVM (poly kernel)': SVC(kernel='poly'),\n", "        'SVM (rbf kernel)': SVC(kernel='rbf'),\n", "        'SVM (sigmoid kernel)': SVC(kernel='sigmoid'),\n", "        'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),\n", "        'Logistic Regression': LogisticRegression(max_iter=1000, random_state=42),\n", "        'XGBoost': xgb.XGBClassifier(use_label_encoder=False, eval_metric='mlogloss', random_state=42)\n", "    }\n"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["\n", "# Function to perform cross-validation\n", "def cross_validate_model(model, X_train, y_train, cv_folds=5):\n", "    skf = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)\n", "    scores = cross_val_score(model, X_train.toarray(), y_train, cv=skf, scoring='accuracy', n_jobs=-1)\n", "    return scores.mean(), scores.std()"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["# Function to train and evaluate a single model\n", "def train_and_evaluate_model(model_name, model, X_train, y_train, X_val, y_val, test_size):\n", "    # Apply cross-validation\n", "    mean_accuracy, std_accuracy = cross_validate_model(model, X_train, y_train, cv_folds=5)\n", "    print(f\"Model: {model_name}\")\n", "    print(f\"Cross-Validation Mean Accuracy (5 folds): {mean_accuracy:.4f} (+/- {std_accuracy:.4f})\")\n", "    \n", "    # Train the model on the entire training set after cross-validation\n", "    model.fit(X_train.toarray(), y_train)\n", "\n", "    # Save the trained model\n", "    model_filename = f'{output_folder}/{model_name.replace(\" \", \"_\")}_model.pkl'\n", "    joblib.dump(model, model_filename)\n", "    print(f\"Model {model_name} saved to {model_filename}\")\n", "\n", "    # Validate the model\n", "    y_val_pred = model.predict(X_val.toarray())\n", "    accuracy = accuracy_score(y_val, y_val_pred)\n", "    recall = recall_score(y_val, y_val_pred, average='weighted')\n", "    precision = precision_score(y_val, y_val_pred, average='weighted')\n", "    f1 = f1_score(y_val, y_val_pred, average='weighted')\n", "\n", "    # Display metrics\n", "    print(f\"Validation Accuracy: {accuracy:.4f}\")\n", "    print(f\"Recall: {recall:.4f}, Precision: {precision:.4f}, F1 Score: {f1:.4f}\\n\")\n", "\n", "    # Confusion Matrix Visualization\n", "    plot_confusion_matrix(y_val, y_val_pred, model_name, 'Validation', test_size)\n", "\n", "    # Return metrics results\n", "    return [(model_name, 'Validation', accuracy, recall, precision, f1)], [(model_name, accuracy)]\n"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["# Function to train and validate models using cross-validation\n", "def train_and_validate_models(models, X_train, X_val, y_train, y_val, test_size):\n", "    metrics_results = []\n", "    validation_results = []\n", "\n", "    print(\"\\nTraining and Validation Phase\\n\")\n", "    results = Parallel(n_jobs=-1)(delayed(train_and_evaluate_model)(model_name, model, X_train, y_train, X_val, y_val, test_size) for model_name, model in models.items())\n", "    for metrics_result, validation_result in results:\n", "        metrics_results.extend(metrics_result)\n", "        validation_results.extend(validation_result)\n", "    \n", "    # Plotting validation results\n", "    plot_validation_results(validation_results, test_size)\n", "\n", "    return metrics_results"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["# Function to plot confusion matrix\n", "def plot_confusion_matrix(y_true, y_pred, model_name, phase, test_size):\n", "    cm = confusion_matrix(y_true, y_pred)\n", "    plt.figure(figsize=(8, 6))\n", "    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', xticklabels=np.unique(y_true), yticklabels=np.unique(y_true))\n", "    plt.xlabel('Predicted')\n", "    plt.ylabel('Actual')\n", "    plt.title(f'Confusion Matrix for {model_name} ({phase})')\n", "    plt.savefig(f'{output_folder}/confusion_matrix_{model_name}_{phase.lower()}_test_size_{test_size}.png')\n", "    plt.close()\n", "\n", "# Function to plot validation results\n", "def plot_validation_results(validation_results, test_size):\n", "    model_names, accuracies = zip(*validation_results)\n", "    plt.figure(figsize=(10, 5))\n", "    plt.bar(model_names, accuracies, color='skyblue')\n", "    plt.xlabel('Model')\n", "    plt.ylabel('Validation Accuracy')\n", "    plt.title(f'Validation Accuracy for Different Models (test_size={test_size})')\n", "    plt.xticks(rotation=45)\n", "    plt.tight_layout()\n", "    plt.savefig(f'{output_folder}/validation_accuracy_test_size_{test_size}.png')\n", "    plt.close()\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["\n", "# # Function to train and validate models\n", "# def train_and_validate_models(models, X_train, X_val, y_train, y_val, test_size):\n", "#     validation_results = []\n", "#     metrics_results = []\n", "#     skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)\n", "\n", "#     print(\"\\nTraining and Validation Phase\\n\")\n", "#     for model_name, model in models.items():\n", "#         # Apply cross-validation\n", "#         # scores = cross_val_score(model, X_train.toarray(), y_train, cv=5, scoring='accuracy')\n", "#         # Cross-validate the model\n", "#         # with parallel_backend('threading'):  # Use parallel backend to speed up cross-validation\n", "#             # scores = cross_val_score(model, X_train.toarray(), y_train, cv=5, scoring='accuracy')\n", "#         # average_accuracy = scores.mean()\n", "\n", "#         # Train the model\n", "#         model.fit(X_train.toarray(), y_train)\n", "        \n", "#         # Save the trained model\n", "#         model_filename = f'{output_folder}/{model_name.replace(\" \", \"_\")}_model.pkl'\n", "#         joblib.dump(model, model_filename)\n", "#         print(f\"Model {model_name} saved to {model_filename}\")\n", "        \n", "#         # Validate the model\n", "#         y_val_pred = model.predict(X_val.toarray())\n", "#         accuracy = accuracy_score(y_val, y_val_pred)\n", "#         recall = recall_score(y_val, y_val_pred, average='weighted')\n", "#         precision = precision_score(y_val, y_val_pred, average='weighted')\n", "#         f1 = f1_score(y_val, y_val_pred, average='weighted')\n", "        \n", "#         # Append metrics to results\n", "#         validation_results.append((model_name, accuracy))\n", "#         metrics_results.append((model_name, 'Validation', accuracy, recall, precision, f1))\n", "       \n", "#         # Append metrics to results\n", "#         metrics_results.append((model_name, 'Cross-Validation', average_accuracy))\n", "\n", "#         # Display metrics\n", "#         print(f\"Model: {model_name}\")\n", "#         # print(f\"Cross-Validation Accuracy (5 folds): {average_accuracy:.4f}\")\n", "\n", "#         print(f\"Validation Accuracy: {accuracy:.4f}\")\n", "#         print(f\"Recall: {recall:.4f}, Precision: {precision:.4f}, F1 Score: {f1:.4f}\\n\")\n", "        \n", "#         # Confusion Matrix Visualization\n", "#         plot_confusion_matrix(y_val, y_val_pred, model_name, 'Validation', test_size)\n", "    \n", "#     # Plotting validation results\n", "#     plot_validation_results(validation_results, test_size)\n", "\n", "#     return metrics_results\n", "\n", "# # Function to plot confusion matrix\n", "# def plot_confusion_matrix(y_true, y_pred, model_name, phase, test_size):\n", "#     cm = confusion_matrix(y_true, y_pred)\n", "#     plt.figure(figsize=(8, 6))\n", "#     sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', xticklabels=np.unique(y_true), yticklabels=np.unique(y_true))\n", "#     plt.xlabel('Predicted')\n", "#     plt.ylabel('Actual')\n", "#     plt.title(f'Confusion Matrix for {model_name} ({phase})')\n", "#     plt.savefig(f'{output_folder}/confusion_matrix_{model_name}_{phase.lower()}_test_size_{test_size}.png')\n", "#     plt.close()\n", "\n", "# # Function to plot validation results\n", "# def plot_validation_results(validation_results, test_size):\n", "#     model_names, accuracies = zip(*validation_results)\n", "#     plt.figure(figsize=(10, 5))\n", "#     plt.bar(model_names, accuracies, color='skyblue')\n", "#     plt.xlabel('Model')\n", "#     plt.ylabel('Validation Accuracy')\n", "#     plt.title(f'Validation Accuracy for Different Models (test_size={test_size})')\n", "#     plt.xticks(rotation=45)\n", "#     plt.tight_layout()\n", "#     plt.savefig(f'{output_folder}/validation_accuracy_test_size_{test_size}.png')\n", "#     plt.close()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Function to test models\n", "def test_models(models, X_test, df,test_size):\n", "    print(\"\\nTesting Phase\\n\")\n", "    test_results = []\n", "    metrics_results = []\n", "    \n", "    for model_name in models.keys():\n", "        # Load the trained model\n", "        model_filename = f'{output_folder}/{model_name.replace(\" \", \"_\")}_model.pkl'\n", "        model = joblib.load(model_filename)\n", "        print(f\"Model {model_name} loaded from {model_filename}\")\n", "        \n", "        # Predict the test set\n", "        y_test_pred = model.predict(X_test.toarray())\n", "        \n", "        # Store the predicted labels\n", "        test_results.append((model_name, y_test_pred))\n", "        \n", "        # Display the predicted labels\n", "        print(f\"Model: {model_name}\")\n", "        # print(f\"Predicted Labels: {y_test_pred}\\n\")\n", "        print(f\"Predicted Labels: {y_test_pred[:10]} (showing first 10 predictions)\\n\")  # Display sample of predictions\n", "\n", "        \n", "        # Confusion Matrix Visualization for Testing\n", "        # Assuming we use some dummy labels for testing purposes (since actual test labels are not provided)\n", "        # y_test_dummy = [0] * len(y_test_pred)  # Example of creating dummy labels\n", "        # plot_confusion_matrix(y_test_dummy, y_test_pred, model_name, 'Testing', test_size)\n", "        # If actual labels are available for testing (optional)\n", "        if 'actual_labels' in df.columns:\n", "            y_test_actual = df['actual_labels']\n", "\n", "            # Calculate the metrics for testing\n", "            accuracy = accuracy_score(y_test_actual, y_test_pred)\n", "            recall = recall_score(y_test_actual, y_test_pred, average='weighted')\n", "            precision = precision_score(y_test_actual, y_test_pred, average='weighted')\n", "            f1 = f1_score(y_test_actual, y_test_pred, average='weighted')\n", "\n", "            # Append metrics to results\n", "            metrics_results.append((model_name, test_size, accuracy, precision, recall, f1))\n", "\n", "            # Confusion Matrix Visualization for Testing\n", "            plot_confusion_matrix(y_test_actual, y_test_pred, model_name, 'Testing', test_size)\n", "        else:\n", "            print(f\"Warning: Actual labels are not provided for meaningful evaluation of {model_name}\")\n", "\n", "    # Save test results to CSV\n", "    # output_df = pd.DataFrame()\n", "    # df = X_test.copy()\n", "    # OK\n", "    # -----------------\n", "    # output_df = pd.DataFrame({'comment': df['comment'], 'clean_comment': df['clean_comment']})\n", "    # for model_name, y_test_pred in test_results:\n", "    #     output_df[model_name] = y_test_pred\n", "    # output_df.to_csv(f'{output_folder}/test_predictions_test_size_{test_size}.csv', index=False)\n", "    # print(f\"Test predictions saved to '{output_folder}/test_predictions_test_size_{test_size}.csv'\")\n", "    #---------------------\n", "\n", "    # Save test results to CSV including original and cleaned comments\n", "    output_df = df.copy()\n", "    for model_name, y_test_pred in test_results:\n", "        # output_df[model_name] = y_test_pred\n", "        output_df[model_name + '_prediction'] = y_test_pred  # Add prediction for each model\n", "        # Calculate majority vote for each comment\n", "\n", "    # ----------------\n", "    # MAJORITY VOTE\n", "    predictions = np.array([y_pred for _, y_pred in test_results]).T\n", "    majority_vote = [np.bincount(row).argmax() for row in predictions]\n", "    output_df['majority_vote'] = majority_vote\n", "    # -----------------\n", "    \n", "    output_df.to_csv(f'{output_folder}/test_predictions_test_size_{test_size}.csv', index=False)\n", "    print(f\"Test predictions saved to '{output_folder}/test_predictions_test_size_{test_size}.csv'\")\n", "\n", "    # Save metrics results to CSV\n", "    if metrics_results:\n", "        metrics_df = pd.DataFrame(metrics_results, columns=['Model', 'Test_Size', 'Accuracy', 'Precision', 'Recall', 'F1'])\n", "        metrics_df.to_csv(f'{output_folder}/test_metrics_results_test_size_{test_size}.csv', index=False)\n", "        print(f\"Test metrics results saved to '{output_folder}/test_metrics_results_test_size_{test_size}.csv'\")\n", "    \n", "    # Display statistics of prediction results\n", "    # for model_name, y_test_pred in test_results:\n", "    #     count_0 = (y_test_pred == 0).sum()\n", "    #     count_1 = (y_test_pred == 1).sum()\n", "    #     print(f\"Model: {model_name}\")\n", "    #     print(f\"Predicted 'ham' (0): {count_0}\")\n", "    #     print(f\"Predicted 'spam' (1): {count_1}\\n\")\n", "\n", "    # Display statistics of prediction results and save\n", "    stats = []\n", "    for model_name, _ in test_results:\n", "        count_0 = output_df[model_name].value_counts().get(0, 0)\n", "        count_1 = output_df[model_name].value_counts().get(1, 0)\n", "        stats.append({'Model': model_name, 'Predicted_Ham': count_0, 'Predicted_Spam': count_1})\n", "        print(f\"Model: {model_name}\")\n", "        print(f\"Predicted 'ham' (0): {count_0}\")\n", "        print(f\"Predicted 'spam' (1): {count_1}\\n\")\n", "    \n", "    # Save statistics to CSV\n", "    stats_df = pd.DataFrame(stats)\n", "    stats_df.to_csv(f'{output_folder}/prediction_statistics_test_size_{test_size}.csv', index=False)\n", "    print(f\"Prediction statistics saved to '{output_folder}/prediction_statistics_test_size_{test_size}.csv'\")\n", "\n", "    # Visualize the statistics\n", "    plt.figure(figsize=(12, 6))\n", "    for model_stat in stats:\n", "        plt.bar(model_stat['Model'], model_stat['Predicted_Ham'], color='blue', alpha=0.6, label='Ham')\n", "        plt.bar(model_stat['Model'], model_stat['Predicted_Spam'], bottom=model_stat['Predicted_Ham'], color='red', alpha=0.6, label='Spam')\n", "    \n", "    plt.xlabel('Model')\n", "    plt.ylabel('Count of Predictions')\n", "    plt.title(f'Prediction Counts for Each Model (test_size={test_size})')\n", "    plt.xticks(rotation=45)\n", "    plt.tight_layout()\n", "    plt.savefig(f'{output_folder}/prediction_statistics_visualization_test_size_{test_size}.png')\n", "    plt.close()\n", "\n", "    return metrics_results"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'load_training_data' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[3], line 32\u001b[0m\n\u001b[0;32m     29\u001b[0m         \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMetrics results saved to \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00moutput_folder\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m/metrics_results_test_size_\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mtest_size\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.csv\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     31\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;18m__name__\u001b[39m \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m__main__\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[1;32m---> 32\u001b[0m     \u001b[43mmain\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[1;32mIn[3], line 3\u001b[0m, in \u001b[0;36mmain\u001b[1;34m()\u001b[0m\n\u001b[0;32m      2\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mmain\u001b[39m():\n\u001b[1;32m----> 3\u001b[0m     X_train_val_raw, y_train_val \u001b[38;5;241m=\u001b[39m \u001b[43mload_training_data\u001b[49m()\n\u001b[0;32m      4\u001b[0m     X_train_val, tfidf \u001b[38;5;241m=\u001b[39m feature_extraction(X_train_val_raw)\n\u001b[0;32m      5\u001b[0m     X_test, df \u001b[38;5;241m=\u001b[39m load_testing_data(tfidf)\n", "\u001b[1;31mNameError\u001b[0m: name 'load_training_data' is not defined"]}], "source": ["# Main function to execute the process\n", "def main():\n", "    X_train_val_raw, y_train_val = load_training_data()\n", "    X_train_val, tfidf = feature_extraction(X_train_val_raw)\n", "    X_test, df = load_testing_data(tfidf)\n", "    models = initialize_models()\n", "\n", "    # Define test sizes for splitting\n", "    test_sizes = [0.2, 0.25, 0.3, 0.35]\n", "    # test_sizes = [0.2,0.3]\n", "    \n", "    for test_size in test_sizes:\n", "        print(f\"\\nUsing test_size = {test_size} for splitting the data\\n\")\n", "        \n", "        # Split the training and validation data\n", "        X_train, X_val, y_train, y_val = train_test_split(X_train_val, y_train_val, test_size=test_size, random_state=42)\n", "        \n", "        # Train and validate models\n", "        metrics_results = train_and_validate_models(models, X_train, X_val, y_train, y_val, test_size)\n", "        \n", "        # Train and cross-validate models\n", "        # metrics_results = train_and_cross_validate_models(models, X_train_val, y_train_val)\n", "        # Test models\n", "        metrics_results += test_models(models, X_test,df, test_size)\n", "        \n", "        # Save metrics results to CSV\n", "        metrics_df = pd.DataFrame(metrics_results, columns=['Model', 'Phase', 'Accuracy', 'Recall', 'Precision', 'F1'])\n", "        metrics_df.to_csv(f'{output_folder}/metrics_results_test_size_{test_size}.csv', index=False)\n", "        print(f\"Metrics results saved to '{output_folder}/metrics_results_test_size_{test_size}.csv'\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}