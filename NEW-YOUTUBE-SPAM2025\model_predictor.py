#!/usr/bin/env python3
"""
YouTube Spam Classification - Model Predictor
==============================================

This script loads trained models and provides prediction functionality
for new YouTube comments.

Author: AI Assistant
Date: 2025-06-29
"""

import pickle
import os
import json
import re
import pandas as pd
from datetime import datetime

class SpamPredictor:
    """Load trained models and make predictions on new comments"""
    
    def __init__(self, models_dir="NEW-YOUTUBE-SPAM2025/dataset"):
        self.models_dir = models_dir
        self.loaded_models = {}
        self.load_available_models()
    
    def preprocess_text(self, text):
        """Clean and preprocess text data (same as training)"""
        if pd.isna(text):
            return ""
        
        # Convert to lowercase
        text = str(text).lower()
        
        # Remove URLs
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        text = re.sub(r'www\.(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        
        # Remove email addresses
        text = re.sub(r'\S+@\S+', '', text)
        
        # Remove special characters and digits
        text = re.sub(r'[^a-zA-Z\s]', '', text)
        
        # Remove extra whitespace
        text = ' '.join(text.split())
        
        return text
    
    def load_available_models(self):
        """Load all available trained models"""
        if not os.path.exists(self.models_dir):
            print(f"Models directory not found: {self.models_dir}")
            return
        
        model_files = [f for f in os.listdir(self.models_dir) if f.endswith('_model.pkl')]
        
        for model_file in model_files:
            model_path = os.path.join(self.models_dir, model_file)
            model_name = model_file.replace('_model.pkl', '')
            
            try:
                with open(model_path, 'rb') as f:
                    self.loaded_models[model_name] = pickle.load(f)
                print(f"Loaded model: {model_name}")
            except Exception as e:
                print(f"Error loading {model_file}: {e}")
        
        print(f"Total models loaded: {len(self.loaded_models)}")
    
    def predict_single(self, comment, model_name=None):
        """Predict spam/ham for a single comment"""
        if not self.loaded_models:
            return "No models loaded"
        
        # Preprocess the comment
        cleaned_comment = self.preprocess_text(comment)
        
        if model_name and model_name in self.loaded_models:
            model = self.loaded_models[model_name]
            prediction = model.predict([cleaned_comment])[0]
            probability = model.predict_proba([cleaned_comment])[0]
            
            return {
                'model': model_name,
                'prediction': 'SPAM' if prediction == 1 else 'HAM',
                'confidence': max(probability),
                'spam_probability': probability[1] if len(probability) > 1 else probability[0]
            }
        else:
            # Use all models for ensemble prediction
            predictions = {}
            for name, model in self.loaded_models.items():
                try:
                    pred = model.predict([cleaned_comment])[0]
                    prob = model.predict_proba([cleaned_comment])[0]
                    predictions[name] = {
                        'prediction': 'SPAM' if pred == 1 else 'HAM',
                        'confidence': max(prob),
                        'spam_probability': prob[1] if len(prob) > 1 else prob[0]
                    }
                except Exception as e:
                    predictions[name] = {'error': str(e)}
            
            return predictions
    
    def predict_batch(self, comments, model_name=None):
        """Predict spam/ham for multiple comments"""
        results = []
        for i, comment in enumerate(comments):
            result = self.predict_single(comment, model_name)
            result['comment_index'] = i
            result['original_comment'] = comment
            results.append(result)
        return results
    
    def get_model_info(self):
        """Get information about loaded models"""
        info = {
            'total_models': len(self.loaded_models),
            'available_models': list(self.loaded_models.keys()),
            'models_directory': self.models_dir
        }
        return info

def demo_predictions():
    """Demonstrate the prediction functionality"""
    predictor = SpamPredictor()
    
    # Test comments
    test_comments = [
        "This is a great video! Thanks for sharing.",
        "Check out my channel! Subscribe now! Free money!",
        "I really enjoyed this content. Very informative.",
        "CLICK HERE FOR FREE STUFF!!! www.spam-site.com",
        "Nice work on this video. Keep it up!",
        "Subscribe to my channel and I'll subscribe back! Check out my latest video!"
    ]
    
    print("SPAM PREDICTION DEMO")
    print("=" * 40)
    
    for i, comment in enumerate(test_comments, 1):
        print(f"\nComment {i}: {comment}")
        print("-" * 40)
        
        # Get prediction from best model (you can specify a specific model)
        result = predictor.predict_single(comment)
        
        if isinstance(result, dict) and 'model' in result:
            print(f"Prediction: {result['prediction']}")
            print(f"Confidence: {result['confidence']:.3f}")
        else:
            # Show results from all models
            for model_name, pred_result in result.items():
                if 'error' not in pred_result:
                    print(f"{model_name}: {pred_result['prediction']} (conf: {pred_result['confidence']:.3f})")

if __name__ == "__main__":
    demo_predictions()
